{"name": "@dcasks/backend", "version": "1.0.0", "description": "<PERSON>", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"// === BUILD & DEPLOYMENT ===": "", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build && yarn sentry:sourcemaps", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org dcasks-ltd --project node-nestjs ./dist && sentry-cli sourcemaps upload --org dcasks-ltd --project node-nestjs ./dist", "// === DEVELOPMENT ===": "", "start": "nest start", "start:dev": "nest start --watch", "start:sit": "NODE_ENV=sit nest start --watch", "start:debug": "nest start --debug --watch", "dev": "nest start --watch", "// === PRODUCTION ===": "", "start:prod": "node dist/main", "start:job": "SERVICE_NAME=JOB nest start", "start:dev:job": "SERVICE_NAME=JOB nest start --watch", "start:prod:job": "SERVICE_NAME=JOB node dist/main", "// === CODE QUALITY ===": "", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "// === UNIT TESTING ===": "", "test": "jest --config jest.config.js", "test:watch": "jest --config jest.config.js --watch", "test:cov": "jest --config jest.config.js --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --config jest.config.js --runInBand", "test:blockchain": "jest --config jest.config.js src/blockchain-event", "// === E2E TESTING (Development) ===": "", "test:e2e": "vitest run test/e2e/*.e2e.spec.ts", "test:e2e:watch": "vitest test/e2e/*.e2e.spec.ts", "test:e2e:ui": "vitest --ui test/e2e/*.e2e.spec.ts", "// === POOL PROCESSOR E2E TESTING ===": "", "test:e2e:pool-processor": "vitest run test/e2e/pool-processor/", "test:e2e:pool-processor:watch": "vitest test/e2e/pool-processor/", "test:e2e:pool-processor:us-q001": "vitest run test/e2e/pool-processor/us-q001-join-sale-queue.e2e.spec.ts", "// === E2E TESTING (CI/Automated) ===": "", "test:e2e:full": "./scripts/run-e2e-workflow.sh", "test:e2e:full:no-cleanup": "./scripts/run-e2e-workflow.sh --skip-cleanup", "test:e2e:full:no-seed": "./scripts/run-e2e-workflow.sh --skip-seed", "test:e2e:full:no-server": "./scripts/run-e2e-workflow.sh --no-server", "test:e2e:dev": "./scripts/run-e2e-workflow.sh --no-server --skip-seed --skip-cleanup", "// === DATABASE MANAGEMENT ===": "", "db:seed": "NODE_ENV=sit ts-node scripts/seed-database.ts", "db:cleanup": "NODE_ENV=sit ts-node scripts/cleanup-database.ts", "db:cleanup:force": "NODE_ENV=sit ts-node scripts/cleanup-database.ts --force", "// === UTILITIES ===": "", "generate-types": "typechain --target ethers-v5 --out-dir src/common/contracts/types './src/common/contracts/**/*.json'", "start:script": "ts-node -r tsconfig-paths/register", "seed:price-chart": "tsx scripts/seed-price-chart.ts", "seed:whole-cask": "yarn start:script scripts/seed-whole-cask.ts"}, "dependencies": {"@apollo/server": "^4.10.4", "@aws-sdk/client-s3": "^3.591.0", "@aws-sdk/client-ses": "^3.590.0", "@metaplex-foundation/digital-asset-standard-api": "^1.0.3", "@metaplex-foundation/js": "^0.20.1", "@metaplex-foundation/umi": "^0.9.1", "@metaplex-foundation/umi-bundle-defaults": "^0.9.1", "@nestjs/apollo": "^12.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.6", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@sentry/cli": "^2.42.3", "@sentry/nestjs": "^9.5.0", "@solana/spl-token": "^0.4.6", "@solana/web3.js": "^1.92.2", "axios": "^1.8.1", "bitcoinjs-message": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "ejs": "^3.1.10", "eth-sig-util": "^3.0.1", "ethereumjs-util": "^7.1.5", "ethers": "5.7.0", "graphql": "^16.8.1", "graphql-type-json": "^0.3.2", "mongoose": "^8.4.1", "node-schedule": "^2.1.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "twilio": "^5.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@typechain/ethers-v5": "^11.1.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/node-schedule": "^2.1.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.1", "typechain": "^8.3.2", "typechain-target-ethers-v5": "^5.0.1", "typescript": "^5.3.3", "vitest": "^2.1.8", "@vitest/ui": "^2.1.8", "mongodb-memory-server": "^10.1.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}
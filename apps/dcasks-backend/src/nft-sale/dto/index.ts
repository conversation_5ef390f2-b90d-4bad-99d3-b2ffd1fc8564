// NFT Sale System DTOs - Phase 2: Core Backend Services
// Export all Data Transfer Objects for the Sailing Whisky NFT Sale system

// Queue Management DTOs
export * from './queue.dto';

// Order Management DTOs
export * from './order.dto';

// NFT Management DTOs
export * from './nft.dto';

// Common Response DTOs
export class SuccessResponseDto {
  success: boolean;
  message?: string;
  timestamp: Date;
}

export class ErrorResponseDto {
  success: boolean;
  error: string;
  message: string;
  statusCode: number;
  timestamp: Date;
}

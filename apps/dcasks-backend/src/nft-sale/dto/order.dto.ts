import {
  IsString,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsNumber,
  IsEnum,
  IsArray,
  ArrayMaxSize,
  ArrayMinSize,
} from 'class-validator';
import { OrderStatus, PaymentMethod } from '../models';

// Create Order Request DTO
export class CreateOrderDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;

  @IsArray()
  @ArrayMinSize(1, { message: 'At least one NFT token ID is required' })
  @ArrayMaxSize(5, { message: 'Maximum 5 NFTs allowed per order' })
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  nftTokenIds: string[];

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;
}

// Order Response DTO
export class OrderResponseDto {
  orderId: string;

  status: OrderStatus;

  expiresAt: Date;

  nftTokenIds: string[];

  totalPrice: number;

  currency: string;

  createdAt: Date;

  paymentMethod?: PaymentMethod;

  paymentRecordId?: string;
}

// Order Details Response DTO (includes related data)
export class OrderDetailsResponseDto extends OrderResponseDto {
  nfts: Array<{
    tokenId: string;
    name: string;
    description?: string;
    imageUrl?: string;
    attributes?: any[];
    price: number;
    currency: string;
  }>;

  paymentRecord?: {
    paymentId: string;
    status: string;
    amount: number;
    currency: string;
    externalSessionUrl?: string;
    createdAt: Date;
    expiresAt?: Date;
  };

  queueSession?: {
    position: number;
    status: string;
    joinedAt: Date;
    poolId?: string;
  };
}

// Order History Response DTO
export class OrderHistoryResponseDto {
  orders: OrderResponseDto[];

  total: number;

  activeOrders: number;

  completedOrders: number;

  failedOrders: number;
}

// Order Status Update DTO (for internal use)
export class UpdateOrderStatusDto {
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @IsOptional()
  @IsString()
  substatus?: string;

  @IsOptional()
  @IsString()
  errorMessage?: string;
}

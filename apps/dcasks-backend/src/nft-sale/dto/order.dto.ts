import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON><PERSON>,
  IsEnum,
} from 'class-validator';
import { OrderStatus, PaymentMethod } from '../models';

// Create Order Request DTO
export class CreateOrderDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;

  @IsString()
  @IsNotEmpty()
  nftTokenId: string;

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;
}

// Order Response DTO
export class OrderResponseDto {
  orderId: string;

  status: OrderStatus;

  expiresAt: Date;

  nftTokenId: string;

  nftPrice: number;

  currency: string;

  createdAt: Date;

  paymentMethod?: PaymentMethod;

  paymentRecordId?: string;
}

// Order Details Response DTO (includes related data)
export class OrderDetailsResponseDto extends OrderResponseDto {
  nft: {
    tokenId: string;
    name: string;
    description?: string;
    imageUrl?: string;
    attributes?: any[];
    price: number;
    currency: string;
  };

  paymentRecord?: {
    paymentId: string;
    status: string;
    amount: number;
    currency: string;
    externalSessionUrl?: string;
    createdAt: Date;
    expiresAt?: Date;
  };

  queueSession?: {
    position: number;
    status: string;
    joinedAt: Date;
    poolId?: string;
  };
}

// Order History Response DTO
export class OrderHistoryResponseDto {
  orders: OrderResponseDto[];

  total: number;

  activeOrders: number;

  completedOrders: number;

  failedOrders: number;
}

// Order Status Update DTO (for internal use)
export class UpdateOrderStatusDto {
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @IsOptional()
  @IsString()
  substatus?: string;

  @IsOptional()
  @IsString()
  errorMessage?: string;
}

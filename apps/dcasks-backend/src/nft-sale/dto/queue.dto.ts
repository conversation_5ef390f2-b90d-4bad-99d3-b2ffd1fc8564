import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

// Queue Entry Request DTO
export class JoinQueueDto {
  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsString()
  ipAddress?: string;
}

// Queue Status Query DTO
export class QueueStatusQueryDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;
}

// Queue Entry Response DTO
export class QueueEntryResponseDto {
  position: number;
  sessionId: string;
  queueSessionId: string;
  status: string;
  maxWaitingQueueSize: number;
}

// Queue Status Response DTO
export class QueueStatusResponseDto {
  position: number;
  status: string;
  poolId?: string;
  canSelectNFT: boolean;
  activeSessionExpiresAt?: Date;
  currentOrderId?: string;
  queueHeartbeat?: Date;
}

// Heartbeat Request DTO
export class HeartbeatDto {
  @IsString()
  @IsNotEmpty()
  sessionToken: string;
}

// Heartbeat Response DTO
export class HeartbeatResponseDto {
  success: boolean;
  position: number;
  status: string;
  nextHeartbeat: Date;
}

import { NFTStatus } from '../models';

// NFT Details Response DTO
export class NFTDetailsResponseDto {
  tokenId: string;
  collectionAddress?: string;
  name: string;
  description?: string;
  imageUrl?: string;
  metadataUrl?: string;
  attributes?: Record<string, any>[];
  price: number;
  currency: string;
  status: NFTStatus;
  isAvailable: boolean;
  lockedBy?: string;
  lockExpiresAt?: Date;
  orderId?: string;
}

// Available NFTs Response DTO
export class AvailableNFTsResponseDto {
  nfts: NFTDetailsResponseDto[];
  totalAvailable: number;
  totalInCollection: number;
  currentlyLocked: number;
  sold: number;
}

// NFT Availability Check Response DTO
export class NFTAvailabilityResponseDto {
  nft: NFTDetailsResponseDto;
  isAvailable: boolean;
  unavailableReason?: string;
  availableAt?: Date;
}

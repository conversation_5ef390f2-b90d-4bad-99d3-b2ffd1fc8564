import { IsNotEmpty, IsString } from 'class-validator';
import { NFTStatus } from '../models';

// NFT Lock Request DTO
export class LockNFTDto {
  @IsString()
  @IsNotEmpty()
  walletAddress: string;

  @IsString()
  @IsNotEmpty()
  nftTokenId: string;
}

// NFT Lock Response DTO
export class LockNFTResponseDto {
  success: boolean;
  lockExpiresAt: Date;
  orderId: string;
  nftTokenId: string;
  lockTimeoutMinutes: number;
}

// NFT Details Response DTO
export class NFTDetailsResponseDto {
  tokenId: string;
  collectionAddress?: string;
  name: string;
  description?: string;
  imageUrl?: string;
  metadataUrl?: string;
  attributes?: Record<string, any>[];
  price: number;
  currency: string;
  status: NFTStatus;
  isAvailable: boolean;
  lockedBy?: string;
  lockExpiresAt?: Date;
  orderId?: string;
}

// Available NFTs Response DTO
export class AvailableNFTsResponseDto {
  nfts: NFTDetailsResponseDto[];
  totalAvailable: number;
  totalInCollection: number;
  currentlyLocked: number;
  sold: number;
}

// NFT Availability Check Response DTO
export class NFTAvailabilityResponseDto {
  nft: NFTDetailsResponseDto;
  isAvailable: boolean;
  unavailableReason?: string;
  availableAt?: Date;
}

import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import {
  Order,
  OrderDocument,
  OrderStatus,
  PaymentMethod,
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  NFTInventory,
  NFTInventoryDocument,
  NFTStatus,
  PaymentRecord,
  PaymentRecordDocument,
  SaleConfig,
  SaleConfigDocument,
} from '../models';
import {
  CreateOrderDto,
  OrderResponseDto,
  OrderDetailsResponseDto,
  OrderHistoryResponseDto,
  UpdateOrderStatusDto,
} from '../dto';

@Injectable()
export class OrderService {
  constructor(
    @InjectModel(Order.name)
    private orderModel: Model<OrderDocument>,
    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,
    @InjectModel(NFTInventory.name)
    private nftInventoryModel: Model<NFTInventoryDocument>,
    @InjectModel(PaymentRecord.name)
    private paymentRecordModel: Model<PaymentRecordDocument>,
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
  ) {}

  /**
   * Create a new order with automatic NFT locking
   */
  async createOrder(createOrderDto: CreateOrderDto): Promise<OrderResponseDto> {
    const { walletAddress, nftTokenIds, paymentMethod, userAgent, ipAddress } =
      createOrderDto;

    // Verify user is in active pool and can create orders (only ACTIVE status allowed)
    const queueSession = await this.queueSessionModel.findOne({
      walletAddress,
      status: QueueSessionStatus.ACTIVE,
    });

    if (!queueSession) {
      throw new BadRequestException(
        'User must be in ACTIVE status to create orders. Queue session not found or user not in active pool.',
      );
    }

    // Check if user already has an active order
    const existingOrder = await this.orderModel.findOne({
      walletAddress,
      status: {
        $in: [
          OrderStatus.CREATED,
          OrderStatus.NFT_LOCKED,
          OrderStatus.PAYMENT_PENDING,
        ],
      },
    });

    if (existingOrder) {
      throw new ConflictException('User already has an active order');
    }

    // Validate NFT token IDs array
    if (!nftTokenIds || nftTokenIds.length === 0) {
      throw new BadRequestException('At least one NFT token ID is required');
    }

    if (nftTokenIds.length > 5) {
      throw new BadRequestException('Maximum 5 NFTs allowed per order');
    }

    // Remove duplicates
    const uniqueNftTokenIds = [...new Set(nftTokenIds)];

    // Get all NFTs and verify availability
    const nfts = await this.nftInventoryModel.find({
      tokenId: { $in: uniqueNftTokenIds },
    });

    if (nfts.length !== uniqueNftTokenIds.length) {
      const foundTokenIds = nfts.map((nft) => nft.tokenId);
      const missingTokenIds = uniqueNftTokenIds.filter(
        (id) => !foundTokenIds.includes(id),
      );
      throw new NotFoundException(
        `NFTs not found: ${missingTokenIds.join(', ')}`,
      );
    }

    // Verify all NFTs are available for sale
    for (const nft of nfts) {
      if (nft.status !== NFTStatus.AVAILABLE) {
        throw new ConflictException(
          `NFT ${nft.tokenId} is not available. Current status: ${nft.status}`,
        );
      }

      // Check if NFT is already locked and lock hasn't expired
      if (nft.lockedBy && nft.lockExpiresAt && nft.lockExpiresAt > new Date()) {
        throw new ConflictException(
          `NFT ${nft.tokenId} is currently locked by another user`,
        );
      }
    }

    const saleConfig = await this.getSaleConfig();
    const lockExpiresAt = new Date(
      Date.now() + saleConfig.selectionTimeoutMinutes * 60 * 1000,
    );
    const orderId = `ord_${uuidv4().replace(/-/g, '')}`;

    // Calculate total price
    const totalPrice = nfts.reduce((sum, nft) => sum + nft.price, 0);
    const currency = nfts[0].currency; // Assume all NFTs have the same currency

    // Start transaction to lock NFTs and create order
    const session = await this.nftInventoryModel.db.startSession();

    try {
      await session.withTransaction(async () => {
        // Lock all NFTs atomically
        for (const nft of nfts) {
          const lockResult = await this.nftInventoryModel.updateOne(
            {
              tokenId: nft.tokenId,
              status: NFTStatus.AVAILABLE,
              $or: [
                { lockedBy: { $exists: false } },
                { lockExpiresAt: { $lt: new Date() } },
              ],
            },
            {
              status: NFTStatus.LOCKED,
              lockedBy: walletAddress,
              lockedAt: new Date(),
              lockExpiresAt,
              orderId,
            },
            { session },
          );

          if (lockResult.modifiedCount === 0) {
            throw new ConflictException(
              `Failed to lock NFT ${nft.tokenId} - it may have been locked by another user`,
            );
          }
        }

        // Create order with multiple NFTs
        const newOrder = new this.orderModel({
          orderId,
          walletAddress,
          queueSessionId: queueSession._id,
          nftTokenIds: uniqueNftTokenIds,
          status: OrderStatus.NFT_LOCKED,
          totalPrice,
          currency,
          createdAt: new Date(),
          nftLockedAt: new Date(),
          expiresAt: lockExpiresAt,
          paymentMethod,
          userAgent,
          ipAddress,
        });

        await newOrder.save({ session });

        // Update queue session to PAYMENT status
        await this.queueSessionModel.updateOne(
          { walletAddress },
          {
            status: QueueSessionStatus.PAYMENT,
            currentOrderId: orderId,
          },
          { session },
        );
      });
    } catch (error) {
      throw error;
    } finally {
      await session.endSession();
    }

    // Fetch the created order to return
    const createdOrder = await this.orderModel.findOne({ orderId });
    return this.mapOrderToResponse(createdOrder!);
  }

  /**
   * Get sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument> {
    const config = await this.saleConfigModel.findOne({ isActive: true });
    if (!config) {
      throw new BadRequestException('No active sale configuration found');
    }
    return config;
  }

  /**
   * Get order details by order ID
   */
  async getOrderDetails(orderId: string): Promise<OrderDetailsResponseDto> {
    const order = await this.orderModel.findOne({ orderId });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Get related NFTs
    const nfts = await this.nftInventoryModel.find({
      tokenId: { $in: order.nftTokenIds },
    });

    if (nfts.length !== order.nftTokenIds.length) {
      throw new NotFoundException('Some NFTs not found for this order');
    }

    // Get payment record if exists
    let paymentRecord = null;
    if (order.paymentRecordId) {
      paymentRecord = await this.paymentRecordModel.findById(
        order.paymentRecordId,
      );
    }

    // Get queue session
    let queueSession = null;
    if (order.queueSessionId) {
      queueSession = await this.queueSessionModel.findById(
        order.queueSessionId,
      );
    }

    const orderResponse = this.mapOrderToResponse(order);

    return {
      ...orderResponse,
      nfts: nfts.map((nft) => ({
        tokenId: nft.tokenId,
        name: nft.name,
        description: nft.description,
        imageUrl: nft.imageUrl,
        attributes: nft.attributes,
        price: nft.price,
        currency: nft.currency,
      })),
      paymentRecord: paymentRecord
        ? {
            paymentId: paymentRecord.paymentId,
            status: paymentRecord.status,
            amount: paymentRecord.amount,
            currency: paymentRecord.currency,
            externalSessionUrl: paymentRecord.externalSessionUrl,
            createdAt: paymentRecord.createdAt,
            expiresAt: paymentRecord.expiresAt,
          }
        : undefined,
      queueSession: queueSession
        ? {
            position: queueSession.position,
            status: queueSession.status,
            joinedAt: queueSession.joinedAt,
            poolId: queueSession.poolId,
          }
        : undefined,
    };
  }

  /**
   * Get order history for a wallet
   */
  async getOrderHistory(
    walletAddress: string,
  ): Promise<OrderHistoryResponseDto> {
    const orders = await this.orderModel
      .find({ walletAddress })
      .sort({ createdAt: -1 });

    const orderResponses = orders.map((order) =>
      this.mapOrderToResponse(order),
    );

    const activeOrders = orders.filter((order) =>
      [
        OrderStatus.CREATED,
        OrderStatus.NFT_LOCKED,
        OrderStatus.PAYMENT_PENDING,
        OrderStatus.PAYMENT_COMPLETED,
        OrderStatus.NFT_TRANSFER_PENDING,
      ].includes(order.status),
    ).length;

    const completedOrders = orders.filter(
      (order) => order.status === OrderStatus.NFT_TRANSFERRED,
    ).length;
    const failedOrders = orders.filter((order) =>
      [OrderStatus.FAILED, OrderStatus.EXPIRED].includes(order.status),
    ).length;

    return {
      orders: orderResponses,
      total: orders.length,
      activeOrders,
      completedOrders,
      failedOrders,
    };
  }

  /**
   * Update order status (internal use)
   */
  async updateOrderStatus(
    orderId: string,
    updateDto: UpdateOrderStatusDto,
  ): Promise<OrderResponseDto> {
    const { status, substatus, errorMessage } = updateDto;

    const updateData: any = { status };

    if (substatus) updateData.substatus = substatus;
    if (errorMessage) updateData.transferErrorMessage = errorMessage;

    // Set appropriate timestamps based on status
    switch (status) {
      case OrderStatus.PAYMENT_PENDING:
        updateData.paymentInitiatedAt = new Date();
        break;
      case OrderStatus.PAYMENT_COMPLETED:
        updateData.paymentCompletedAt = new Date();
        break;
      case OrderStatus.NFT_TRANSFER_PENDING:
        updateData.nftTransferInitiatedAt = new Date();
        break;
      case OrderStatus.NFT_TRANSFERRED:
        updateData.nftTransferCompletedAt = new Date();
        break;
    }

    const order = await this.orderModel.findOneAndUpdate(
      { orderId },
      updateData,
      { new: true },
    );

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return this.mapOrderToResponse(order);
  }

  /**
   * Expire orders that have exceeded their timeout
   */
  async expireTimedOutOrders(): Promise<number> {
    const expiredOrders = await this.orderModel.find({
      status: {
        $in: [
          OrderStatus.CREATED,
          OrderStatus.NFT_LOCKED,
          OrderStatus.PAYMENT_PENDING,
        ],
      },
      expiresAt: { $lt: new Date() },
    });

    let expiredCount = 0;

    for (const order of expiredOrders) {
      // Update order status
      await this.orderModel.updateOne(
        { _id: order._id },
        { status: OrderStatus.EXPIRED },
      );

      // Unlock all NFTs
      await this.nftInventoryModel.updateMany(
        { tokenId: { $in: order.nftTokenIds }, orderId: order.orderId },
        {
          status: NFTStatus.AVAILABLE,
          $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1, orderId: 1 },
        },
      );

      // Update queue session
      await this.queueSessionModel.updateOne(
        { walletAddress: order.walletAddress },
        {
          status: QueueSessionStatus.EXPIRED,
          $unset: { currentOrderId: 1, activeSessionExpiresAt: 1 },
        },
      );

      expiredCount++;
    }

    return expiredCount;
  }

  /**
   * Associate payment record with order
   */
  async associatePaymentRecord(
    orderId: string,
    paymentRecordId: string,
  ): Promise<void> {
    await this.orderModel.updateOne(
      { orderId },
      {
        paymentRecordId,
        status: OrderStatus.PAYMENT_PENDING,
        paymentInitiatedAt: new Date(),
      },
    );
  }

  /**
   * Mark order as payment completed
   */
  async markPaymentCompleted(
    orderId: string,
    transactionHash?: string,
  ): Promise<void> {
    const updateData: any = {
      status: OrderStatus.PAYMENT_COMPLETED,
      paymentCompletedAt: new Date(),
    };

    if (transactionHash) {
      updateData.transferTransactionHash = transactionHash;
    }

    await this.orderModel.updateOne({ orderId }, updateData);
  }

  /**
   * Mark order as NFT transferred
   */
  async markNFTTransferred(
    orderId: string,
    transactionHash: string,
  ): Promise<void> {
    await this.orderModel.updateOne(
      { orderId },
      {
        status: OrderStatus.NFT_TRANSFERRED,
        nftTransferCompletedAt: new Date(),
        transferTransactionHash: transactionHash,
      },
    );

    // Mark all NFTs as sold
    const order = await this.orderModel.findOne({ orderId });
    if (order) {
      await this.nftInventoryModel.updateMany(
        { tokenId: { $in: order.nftTokenIds } },
        { status: NFTStatus.SOLD },
      );

      // Complete queue session
      await this.queueSessionModel.updateOne(
        { walletAddress: order.walletAddress },
        { status: QueueSessionStatus.COMPLETED },
      );
    }
  }

  /**
   * Map order document to response DTO
   */
  private mapOrderToResponse(order: OrderDocument): OrderResponseDto {
    return {
      orderId: order.orderId,
      status: order.status,
      expiresAt: order.expiresAt,
      nftTokenIds: order.nftTokenIds,
      totalPrice: order.totalPrice || 0,
      currency: order.currency || 'USD',
      createdAt: order.createdAt,
      paymentMethod: order.paymentMethod,
      paymentRecordId: order.paymentRecordId?.toString(),
    };
  }
}

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import {
  SaleConfig,
  SaleConfigDocument,
  AdminAction,
  AdminActionDocument,
  AdminActionType,
  TargetEntity,
} from '../models';

export interface SaleConfigCache {
  isActive: boolean;
  isPaused: boolean;
  pauseReason?: string;
  maxWaitingQueueSize: number;
  maxActivePoolSize: number;
  activeSessionTimeoutMinutes: number; // Timeout for users in ACTIVE state before moving to PAYMENT
  paymentTimeoutMinutes: number;
  queueSessionTimeoutMinutes?: number;
  enabledPaymentMethods: string[];
  stripeTimeoutMinutes?: number;
  nowpaymentsTimeoutMinutes: number;
  nftCollectionAddress?: string;
  nftBasePrice?: number;
  nftCurrency?: string;
  maxConcurrentPayments?: number;
  maxRetryAttempts: number;
  saleStartTime?: Date;
  saleEndTime?: Date;
  configVersion: string;
  lastUpdated: Date;
}

@Injectable()
export class ConfigManagerService implements OnModuleInit {
  private readonly logger = new Logger(ConfigManagerService.name);
  private configCache: SaleConfigCache | null = null;
  private lastConfigCheck: Date = new Date(0);
  private configInterval: NodeJS.Timeout | null = null;

  constructor(
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
    @InjectModel(AdminAction.name)
    private adminActionModel: Model<AdminActionDocument>,
  ) {}

  async onModuleInit() {
    await this.loadConfiguration();

    // Start configuration checking every 30 seconds
    this.configInterval = setInterval(() => {
      this.loadConfiguration().catch((error) => {
        this.logger.error('Error in scheduled config loading:', error);
      });
    }, 30000);
  }

  /**
   * Load configuration from database
   */
  async loadConfiguration(): Promise<void> {
    try {
      const config = await this.saleConfigModel
        .findOne({ isActive: true })
        .sort({ createdAt: -1 });

      if (config) {
        const newCache: SaleConfigCache = {
          isActive: config.isActive,
          isPaused: config.isPaused,
          pauseReason: config.pauseReason,
          maxWaitingQueueSize: config.maxWaitingQueueSize,
          maxActivePoolSize: config.maxActivePoolSize,
          activeSessionTimeoutMinutes: config.activeSessionTimeoutMinutes,
          paymentTimeoutMinutes: config.paymentTimeoutMinutes,
          queueSessionTimeoutMinutes: config.queueSessionTimeoutMinutes,
          enabledPaymentMethods: config.enabledPaymentMethods,
          stripeTimeoutMinutes: config.stripeTimeoutMinutes,
          nowpaymentsTimeoutMinutes: config.nowpaymentsTimeoutMinutes,
          nftCollectionAddress: config.nftCollectionAddress,
          nftBasePrice: config.nftBasePrice,
          nftCurrency: config.nftCurrency,
          maxConcurrentPayments: config.maxConcurrentPayments,
          maxRetryAttempts: config.maxRetryAttempts,
          saleStartTime: config.saleStartTime,
          saleEndTime: config.saleEndTime,
          configVersion: config.configVersion,
          lastUpdated: config.updatedAt,
        };

        // Check if configuration changed
        const configChanged =
          !this.configCache ||
          this.configCache.configVersion !== newCache.configVersion ||
          this.configCache.lastUpdated.getTime() !==
            newCache.lastUpdated.getTime();

        if (configChanged) {
          this.logger.log(`Configuration updated: ${newCache.configVersion}`);
          this.configCache = newCache;
          this.lastConfigCheck = new Date();
        }
      } else {
        this.logger.warn('No active sale configuration found');
        this.configCache = null;
      }
    } catch (error) {
      this.logger.error('Error loading configuration:', error);
    }
  }

  /**
   * Get current configuration (from cache)
   */
  getConfig(): SaleConfigCache | null {
    return this.configCache;
  }

  /**
   * Check if sale is active
   */
  isSaleActive(): boolean {
    if (!this.configCache) return false;

    const now = new Date();
    const config = this.configCache;

    // Check basic active status
    if (!config.isActive || config.isPaused) {
      return false;
    }

    // Check time-based activation
    if (config.saleStartTime && now < config.saleStartTime) {
      return false;
    }

    if (config.saleEndTime && now > config.saleEndTime) {
      return false;
    }

    return true;
  }

  /**
   * Check if payment method is enabled
   */
  isPaymentMethodEnabled(method: string): boolean {
    if (!this.configCache) return false;
    return this.configCache.enabledPaymentMethods.includes(method);
  }

  /**
   * Get timeout for specific payment method
   */
  getPaymentTimeout(method: string): number {
    if (!this.configCache) return 5; // Default 5 minutes

    switch (method.toLowerCase()) {
      case 'stripe':
        return (
          this.configCache.stripeTimeoutMinutes ||
          this.configCache.paymentTimeoutMinutes
        );
      case 'nowpayments':
        return this.configCache.nowpaymentsTimeoutMinutes;
      default:
        return this.configCache.paymentTimeoutMinutes;
    }
  }

  /**
   * Create new configuration
   */
  async createConfig(
    configData: Partial<SaleConfig>,
    adminUser: string,
  ): Promise<SaleConfigDocument> {
    // Deactivate existing configurations
    await this.saleConfigModel.updateMany(
      { isActive: true },
      { isActive: false },
    );

    const configVersion = `v${Date.now()}`;

    const newConfig = new this.saleConfigModel({
      ...configData,
      configVersion,
      isActive: true,
      createdBy: adminUser,
    });

    await newConfig.save();

    // Log admin action
    await this.logAdminAction(
      AdminActionType.CONFIG_UPDATE,
      adminUser,
      TargetEntity.SALE,
      newConfig._id.toString(),
      { configVersion, action: 'create' },
      'Created new sale configuration',
    );

    // Force reload configuration
    await this.loadConfiguration();

    this.logger.log(
      `New configuration created: ${configVersion} by ${adminUser}`,
    );
    return newConfig;
  }

  /**
   * Update existing configuration
   */
  async updateConfig(
    updates: Partial<SaleConfig>,
    adminUser: string,
  ): Promise<SaleConfigDocument> {
    const currentConfig = await this.saleConfigModel.findOne({
      isActive: true,
    });
    if (!currentConfig) {
      throw new Error('No active configuration found to update');
    }

    // Create new version instead of updating existing
    const configVersion = `v${Date.now()}`;

    // Deactivate current config
    await this.saleConfigModel.updateOne(
      { _id: currentConfig._id },
      { isActive: false },
    );

    // Create new config with updates
    const newConfig = new this.saleConfigModel({
      ...currentConfig.toObject(),
      ...updates,
      _id: undefined, // Let MongoDB generate new ID
      configVersion,
      isActive: true,
      createdBy: adminUser,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await newConfig.save();

    // Log admin action
    await this.logAdminAction(
      AdminActionType.CONFIG_UPDATE,
      adminUser,
      TargetEntity.SALE,
      newConfig._id.toString(),
      { configVersion, updates, previousVersion: currentConfig.configVersion },
      'Updated sale configuration',
    );

    // Force reload configuration
    await this.loadConfiguration();

    this.logger.log(`Configuration updated: ${configVersion} by ${adminUser}`);
    return newConfig;
  }

  /**
   * Pause sale
   */
  async pauseSale(reason: string, adminUser: string): Promise<void> {
    await this.updateConfig({ isPaused: true, pauseReason: reason }, adminUser);

    await this.logAdminAction(
      AdminActionType.SALE_PAUSE,
      adminUser,
      TargetEntity.SALE,
      'current',
      { reason },
      `Sale paused: ${reason}`,
    );

    this.logger.log(`Sale paused by ${adminUser}: ${reason}`);
  }

  /**
   * Resume sale
   */
  async resumeSale(adminUser: string): Promise<void> {
    await this.updateConfig(
      { isPaused: false, pauseReason: undefined },
      adminUser,
    );

    await this.logAdminAction(
      AdminActionType.SALE_START,
      adminUser,
      TargetEntity.SALE,
      'current',
      {},
      'Sale resumed',
    );

    this.logger.log(`Sale resumed by ${adminUser}`);
  }

  /**
   * Stop sale
   */
  async stopSale(adminUser: string): Promise<void> {
    await this.updateConfig({ isActive: false }, adminUser);

    await this.logAdminAction(
      AdminActionType.SALE_STOP,
      adminUser,
      TargetEntity.SALE,
      'current',
      {},
      'Sale stopped',
    );

    this.logger.log(`Sale stopped by ${adminUser}`);
  }

  /**
   * Get configuration history
   */
  async getConfigHistory(limit: number = 10): Promise<SaleConfigDocument[]> {
    return await this.saleConfigModel
      .find({})
      .sort({ createdAt: -1 })
      .limit(limit);
  }

  /**
   * Get current configuration from database (bypass cache)
   */
  async getCurrentConfigFromDB(): Promise<SaleConfigDocument | null> {
    return await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
  }

  /**
   * Log admin action
   */
  private async logAdminAction(
    actionType: AdminActionType,
    adminUser: string,
    targetEntity: TargetEntity,
    targetId: string,
    actionData: any,
    reason: string,
  ): Promise<void> {
    try {
      const adminAction = new this.adminActionModel({
        actionType,
        adminUser,
        targetEntity,
        targetId,
        actionData,
        reason,
        executedAt: new Date(),
        success: true,
        affectedRecords: 1,
      });

      await adminAction.save();
    } catch (error) {
      this.logger.error('Error logging admin action:', error);
    }
  }

  /**
   * Validate configuration
   */
  validateConfig(config: Partial<SaleConfig>): string[] {
    const errors: string[] = [];

    if (config.maxWaitingQueueSize && config.maxWaitingQueueSize < 1) {
      errors.push('maxWaitingQueueSize must be at least 1');
    }

    if (config.maxActivePoolSize && config.maxActivePoolSize < 1) {
      errors.push('maxActivePoolSize must be at least 1');
    }

    if (
      config.activeSessionTimeoutMinutes &&
      config.activeSessionTimeoutMinutes < 1
    ) {
      errors.push('activeSessionTimeoutMinutes must be at least 1');
    }

    if (config.paymentTimeoutMinutes && config.paymentTimeoutMinutes < 1) {
      errors.push('paymentTimeoutMinutes must be at least 1');
    }

    if (
      config.saleStartTime &&
      config.saleEndTime &&
      config.saleStartTime >= config.saleEndTime
    ) {
      errors.push('saleStartTime must be before saleEndTime');
    }

    return errors;
  }
}

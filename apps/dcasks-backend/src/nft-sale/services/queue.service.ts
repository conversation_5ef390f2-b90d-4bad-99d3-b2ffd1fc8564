import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import {
  HeartbeatDto,
  HeartbeatResponseDto,
  JoinQueueDto,
  QueueEntryResponseDto,
  QueueStatusResponseDto,
} from '../dto';
import {
  ActivePoolTracking,
  ActivePoolTrackingDocument,
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  SaleConfig,
  SaleConfigDocument,
} from '../models';

@Injectable()
export class QueueService {
  constructor(
    private logger = new Logger(QueueService.name),

    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,

    @InjectModel(ActivePoolTracking.name)
    private activePoolTrackingModel: Model<ActivePoolTrackingDocument>,

    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
  ) {}

  /**
   * Add user to queue or return existing position
   */
  async joinQueue(
    walletAddress: string,
    joinQueueDto: JoinQueueDto,
  ): Promise<QueueEntryResponseDto> {
    const { userAgent, ipAddress } = joinQueueDto;

    // Get current sale configuration
    const saleConfig = await this.getSaleConfig();
    if (!saleConfig.isActive) {
      throw new BadRequestException('Sale is not currently active');
    }
    if (saleConfig.isPaused) {
      this.logger.warn(
        `joinQueue > Sale is paused, preventing user from joining queue`,
        {
          walletAddress,
          pauseReason: saleConfig.pauseReason,
        },
      );
      throw new BadRequestException(
        `Sale is paused: ${saleConfig.pauseReason || 'Temporarily unavailable'}`,
      );
    }

    // Check if user already has any existing session (regardless of status)
    const existingSession = await this.queueSessionModel.findOne({
      walletAddress,
    });

    if (existingSession) {
      // Return 409 Conflict - user must leave queue before joining again
      this.logger.warn(
        `joinQueue > User already has an existing queue session`,
        {
          walletAddress,
          existingSession,
        },
      );
      throw new ConflictException(
        'You already have an existing queue session. Please leave the queue first before joining again.',
      );
    }

    // Check waiting queue capacity (only count WAITING sessions)
    const currentWaitingQueueSize = await this.queueSessionModel.countDocuments(
      {
        status: QueueSessionStatus.WAITING,
      },
    );

    if (currentWaitingQueueSize >= saleConfig.maxWaitingQueueSize) {
      this.logger.warn(
        `joinQueue > Waiting queue is full, preventing user from joining`,
        {
          walletAddress,
          currentWaitingQueueSize,
          maxWaitingQueueSize: saleConfig.maxWaitingQueueSize,
        },
      );
      throw new ConflictException(
        'Waiting queue is full. Please try again later.',
      );
    }

    // Create new queue session
    const sessionToken = uuidv4();
    const newSession = new this.queueSessionModel({
      walletAddress,
      position: currentWaitingQueueSize + 1,
      status: QueueSessionStatus.WAITING,
      queueJoinedAt: new Date(),
      sessionToken,
      queueHeartbeat: new Date(),
      userAgent,
      ipAddress,
    });

    await newSession.save();

    const position = await this.calculateQueuePosition(walletAddress);

    return {
      position,
      sessionId: sessionToken,
      queueSessionId: newSession._id.toString(),
      status: newSession.status,
      maxWaitingQueueSize: saleConfig.maxWaitingQueueSize,
    };
  }

  /**
   * Get current queue status for a user
   */
  async getQueueStatus(walletAddress: string): Promise<QueueStatusResponseDto> {
    const session = await this.queueSessionModel.findOne({
      walletAddress,
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.PAYMENT,
        ],
      },
    });

    if (!session) {
      throw new NotFoundException(
        'No active queue session found for this wallet',
      );
    }

    const position = await this.calculateQueuePosition(walletAddress);

    return {
      position,
      status: session.status,
      poolId: session.poolId,
      canSelectNFT: session.status === QueueSessionStatus.ACTIVE,
      activeSessionExpiresAt: session.activeSessionExpiresAt,
      currentOrderId: session.currentOrderId,
      queueHeartbeat: session.queueHeartbeat,
    };
  }

  /**
   * Process heartbeat to keep session alive
   */
  async processHeartbeat(
    walletAddress: string,
    heartbeatDto: HeartbeatDto,
  ): Promise<HeartbeatResponseDto> {
    const { sessionToken } = heartbeatDto;

    const session = await this.queueSessionModel.findOne({
      walletAddress,
      sessionToken,
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.PAYMENT,
        ],
      },
    });

    if (!session) {
      throw new NotFoundException('Invalid session or session expired');
    }

    // Update heartbeat
    session.queueHeartbeat = new Date();
    await session.save();

    const position = await this.calculateQueuePosition(walletAddress);
    const nextHeartbeat = new Date(Date.now() + 30000); // 30 seconds from now

    return {
      success: true,
      position,
      status: session.status,
      nextHeartbeat,
    };
  }

  /**
   * Calculate current queue position for a wallet
   */
  private async calculateQueuePosition(walletAddress: string): Promise<number> {
    const session = await this.queueSessionModel.findOne({ walletAddress });
    if (!session) return 0;

    if (
      session.status === QueueSessionStatus.ACTIVE ||
      session.status === QueueSessionStatus.PAYMENT
    ) {
      return 0; // User is in active pool
    }

    // Count users ahead in queue
    const usersAhead = await this.queueSessionModel.countDocuments({
      queueJoinedAt: { $lt: session.queueJoinedAt },
      status: QueueSessionStatus.WAITING,
    });

    return usersAhead + 1;
  }

  /**
   * Get current sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument> {
    const config = await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
    if (!config) {
      throw new NotFoundException('No active sale configuration found');
    }
    return config;
  }

  /**
   * Remove user from queue (when they leave or complete purchase)
   */
  async leaveQueue(walletAddress: string): Promise<void> {
    await this.queueSessionModel.updateOne(
      { walletAddress, status: { $ne: QueueSessionStatus.COMPLETED } },
      {
        status: QueueSessionStatus.LEFT_QUEUE,
        $unset: { poolId: 1, activeSessionExpiresAt: 1 },
      },
    );
  }

  /**
   * Move user to active pool (called by pool processor)
   */
  async moveToActivePool(walletAddress: string, poolId: string): Promise<void> {
    const saleConfig = await this.getSaleConfig();
    const activeSessionExpiresAt = new Date(
      Date.now() + saleConfig.activeSessionTimeoutMinutes * 60 * 1000,
    );

    await this.queueSessionModel.updateOne(
      { walletAddress, status: QueueSessionStatus.WAITING },
      {
        status: QueueSessionStatus.ACTIVE,
        poolId,
        activeSessionExpiresAt,
      },
    );
  }

  /**
   * Mark user as in payment status (when order is created)
   */
  async markAsInPayment(walletAddress: string, orderId: string): Promise<void> {
    await this.queueSessionModel.updateOne(
      { walletAddress, status: QueueSessionStatus.ACTIVE },
      {
        status: QueueSessionStatus.PAYMENT,
        paymentStartedAt: new Date(),
        currentOrderId: orderId,
      },
    );
  }
}

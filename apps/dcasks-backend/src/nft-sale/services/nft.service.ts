import {
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import {
  NFTInventory,
  NFTInventoryDocument,
  NFTStatus,
} from '../models';
import {
  NFTDetailsResponseDto,
  AvailableNFTsResponseDto,
  NFTAvailabilityResponseDto,
  NFTQueryResponseDto,
  NFTQueryParamsDto,
} from '../dto';

@Injectable()
export class NFTService {
  constructor(
    @InjectModel(NFTInventory.name)
    private nftInventoryModel: Model<NFTInventoryDocument>,
  ) {}

  /**
   * Get available NFTs for purchase
   */
  async getAvailableNFTs(): Promise<AvailableNFTsResponseDto> {
    // Get available NFTs (not locked or lock expired)
    const availableNFTs = await this.nftInventoryModel
      .find({
        isActive: true,
        $or: [
          { status: NFTStatus.AVAILABLE },
          {
            status: NFTStatus.LOCKED,
            lockExpiresAt: { $lt: new Date() },
          },
        ],
      })
      .sort({ tokenId: 1 });

    // Get collection statistics
    const totalInCollection = await this.nftInventoryModel.countDocuments({
      isActive: true,
    });
    const currentlyLocked = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.LOCKED,
      lockExpiresAt: { $gt: new Date() },
    });
    const sold = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.SOLD,
    });

    const nfts = availableNFTs.map((nft) => this.mapNFTToResponse(nft));

    return {
      nfts,
      totalAvailable: nfts.length,
      totalInCollection,
      currentlyLocked,
      sold,
    };
  }

  /**
   * Consolidated NFT query method - handles both single NFT lookup and filtered listings
   */
  async queryNFTs(
    tokenId?: string,
    queryParams?: NFTQueryParamsDto,
  ): Promise<NFTQueryResponseDto> {
    // If tokenId is provided, return single NFT details
    if (tokenId) {
      return await this.getSingleNFTDetails(tokenId);
    }

    // Otherwise, return filtered list of NFTs
    return await this.getFilteredNFTs(queryParams || {});
  }

  /**
   * Get specific NFT details and availability
   */
  async getNFTDetails(tokenId: string): Promise<NFTAvailabilityResponseDto> {
    const result = await this.getSingleNFTDetails(tokenId);
    return {
      nft: result.nft!,
      isAvailable: result.isAvailable!,
      unavailableReason: result.unavailableReason,
      availableAt: result.availableAt,
    };
  }

  /**
   * Get single NFT details (internal method)
   */
  private async getSingleNFTDetails(tokenId: string): Promise<NFTQueryResponseDto> {
    const nft = await this.nftInventoryModel.findOne({ tokenId });

    if (!nft) {
      throw new NotFoundException('NFT not found');
    }

    const nftDetails = this.mapNFTToResponse(nft);
    let isAvailable = false;
    let unavailableReason: string | undefined;
    let availableAt: Date | undefined;

    if (!nft.isActive) {
      unavailableReason = 'NFT is not available for sale';
    } else if (nft.status === NFTStatus.SOLD) {
      unavailableReason = 'NFT has already been sold';
    } else if (nft.status === NFTStatus.RESERVED) {
      unavailableReason = 'NFT is reserved';
    } else if (nft.status === NFTStatus.UNAVAILABLE) {
      unavailableReason = 'NFT is temporarily unavailable';
    } else if (nft.status === NFTStatus.LOCKED) {
      if (nft.lockExpiresAt && nft.lockExpiresAt > new Date()) {
        unavailableReason = 'NFT is currently locked by another user';
        availableAt = nft.lockExpiresAt;
      } else {
        isAvailable = true; // Lock expired
      }
    } else if (nft.status === NFTStatus.AVAILABLE) {
      isAvailable = true;
    }

    return {
      nft: nftDetails,
      isAvailable,
      unavailableReason,
      availableAt,
    };
  }

  /**
   * Get filtered list of NFTs (internal method)
   */
  private async getFilteredNFTs(queryParams: NFTQueryParamsDto): Promise<NFTQueryResponseDto> {
    const {
      status,
      minPrice,
      maxPrice,
      limit = 50,
      offset = 0,
      isActive = true,
    } = queryParams;

    // Build query filter
    const filter: any = { isActive };

    // Status filter
    if (status) {
      if (status.toUpperCase() === 'AVAILABLE') {
        // For AVAILABLE, include both AVAILABLE status and expired locks
        filter.$or = [
          { status: NFTStatus.AVAILABLE },
          {
            status: NFTStatus.LOCKED,
            lockExpiresAt: { $lt: new Date() },
          },
        ];
      } else {
        // For other statuses, filter directly
        const nftStatus = status.toUpperCase() as NFTStatus;
        if (Object.values(NFTStatus).includes(nftStatus)) {
          filter.status = nftStatus;
        }
      }
    } else {
      // Default: show available NFTs (including expired locks)
      filter.$or = [
        { status: NFTStatus.AVAILABLE },
        {
          status: NFTStatus.LOCKED,
          lockExpiresAt: { $lt: new Date() },
        },
      ];
    }

    // Price range filter
    if (minPrice !== undefined || maxPrice !== undefined) {
      filter.price = {};
      if (minPrice !== undefined && !isNaN(minPrice) && minPrice >= 0) {
        filter.price.$gte = minPrice;
      }
      if (maxPrice !== undefined && !isNaN(maxPrice) && maxPrice >= 0) {
        filter.price.$lte = maxPrice;
      }
    }

    // Get total count for pagination
    const total = await this.nftInventoryModel.countDocuments(filter);

    // Get filtered NFTs with pagination
    const nfts = await this.nftInventoryModel
      .find(filter)
      .sort({ tokenId: 1 })
      .skip(offset)
      .limit(limit);

    // Get collection statistics
    const totalInCollection = await this.nftInventoryModel.countDocuments({
      isActive: true,
    });
    const currentlyLocked = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.LOCKED,
      lockExpiresAt: { $gt: new Date() },
    });
    const sold = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.SOLD,
    });

    const mappedNFTs = nfts.map((nft) => this.mapNFTToResponse(nft));

    return {
      nfts: mappedNFTs,
      totalAvailable: mappedNFTs.length,
      totalInCollection,
      currentlyLocked,
      sold,
      pagination: {
        limit,
        offset,
        total,
        hasMore: offset + limit < total,
      },
    };
  }

  /**
   * Unlock expired NFTs (called by cleanup job)
   */
  async unlockExpiredNFTs(): Promise<number> {
    const result = await this.nftInventoryModel.updateMany(
      {
        status: NFTStatus.LOCKED,
        lockExpiresAt: { $lt: new Date() },
      },
      {
        status: NFTStatus.AVAILABLE,
        $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1, orderId: 1 },
      },
    );

    return result.modifiedCount;
  }

  /**
   * Mark NFT as sold (called when payment is completed)
   */
  async markNFTAsSold(tokenId: string, orderId: string): Promise<void> {
    await this.nftInventoryModel.updateOne(
      { tokenId, orderId },
      {
        status: NFTStatus.SOLD,
        $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1 },
      },
    );
  }

  /**
   * Map NFT document to response DTO
   */
  private mapNFTToResponse(nft: NFTInventoryDocument): NFTDetailsResponseDto {
    return {
      tokenId: nft.tokenId,
      collectionAddress: nft.collectionAddress,
      name: nft.name,
      description: nft.description,
      imageUrl: nft.imageUrl,
      metadataUrl: nft.metadataUrl,
      attributes: nft.attributes,
      price: nft.price,
      currency: nft.currency,
      status: nft.status,
      isAvailable:
        nft.status === NFTStatus.AVAILABLE ||
        (nft.status === NFTStatus.LOCKED &&
          nft.lockExpiresAt &&
          nft.lockExpiresAt < new Date()),
      lockedBy: nft.lockedBy,
      lockExpiresAt: nft.lockExpiresAt,
      orderId: nft.orderId,
    };
  }
}

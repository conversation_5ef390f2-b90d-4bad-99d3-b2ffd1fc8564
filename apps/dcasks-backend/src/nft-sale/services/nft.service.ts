import {
  Injectable,
  BadRequestException,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import {
  NFTInventory,
  NFTInventoryDocument,
  NFTStatus,
  Order,
  OrderDocument,
  OrderStatus,
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  SaleConfig,
  SaleConfigDocument,
} from '../models';
import {
  LockNFTDto,
  LockNFTResponseDto,
  NFTDetailsResponseDto,
  AvailableNFTsResponseDto,
  NFTAvailabilityResponseDto,
} from '../dto';

@Injectable()
export class NFTService {
  constructor(
    @InjectModel(NFTInventory.name)
    private nftInventoryModel: Model<NFTInventoryDocument>,
    @InjectModel(Order.name)
    private orderModel: Model<OrderDocument>,
    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
  ) {}

  /**
   * Lock an NFT for a user (creates order)
   */
  async lockNFT(lockNFTDto: LockNFTDto): Promise<LockNFTResponseDto> {
    const { walletAddress, nftTokenId } = lockNFTDto;

    // Verify user is in active pool
    const queueSession = await this.queueSessionModel.findOne({
      walletAddress,
      status: QueueSessionStatus.ACTIVE,
    });

    if (!queueSession) {
      throw new BadRequestException(
        'User is not in active pool or queue session not found',
      );
    }

    // Check if user already has an active order
    const existingOrder = await this.orderModel.findOne({
      walletAddress,
      status: {
        $in: [
          OrderStatus.CREATED,
          OrderStatus.NFT_LOCKED,
          OrderStatus.PAYMENT_PENDING,
        ],
      },
    });

    if (existingOrder) {
      throw new ConflictException('User already has an active order');
    }

    // Get NFT and verify availability
    const nft = await this.nftInventoryModel.findOne({ tokenId: nftTokenId });
    if (!nft) {
      throw new NotFoundException('NFT not found');
    }

    if (!nft.isActive) {
      throw new BadRequestException('NFT is not available for sale');
    }

    if (nft.status !== NFTStatus.AVAILABLE) {
      throw new ConflictException(
        `NFT is not available. Current status: ${nft.status}`,
      );
    }

    // Check if NFT is already locked and lock hasn't expired
    if (nft.lockedBy && nft.lockExpiresAt && nft.lockExpiresAt > new Date()) {
      throw new ConflictException('NFT is currently locked by another user');
    }

    const saleConfig = await this.getSaleConfig();
    const lockExpiresAt = new Date(
      Date.now() + saleConfig.selectionTimeoutMinutes * 60 * 1000,
    );
    const orderId = `ord_${uuidv4().replace(/-/g, '')}`; // e.g. ord_1234567890

    // Start transaction to lock NFT and create order
    const session = await this.nftInventoryModel.db.startSession();

    try {
      await session.withTransaction(async () => {
        // Lock the NFT
        const lockResult = await this.nftInventoryModel.updateOne(
          {
            tokenId: nftTokenId,
            status: NFTStatus.AVAILABLE,
            $or: [
              { lockedBy: { $exists: false } },
              { lockExpiresAt: { $lt: new Date() } },
            ],
          },
          {
            status: NFTStatus.LOCKED,
            lockedBy: walletAddress,
            lockedAt: new Date(),
            lockExpiresAt,
            orderId,
          },
          { session },
        );

        if (lockResult.modifiedCount === 0) {
          throw new ConflictException(
            'Failed to lock NFT - it may have been locked by another user',
          );
        }

        // Create order
        const newOrder = new this.orderModel({
          orderId,
          walletAddress,
          queueSessionId: queueSession._id,
          nftTokenId,
          status: OrderStatus.NFT_LOCKED,
          nftPrice: nft.price,
          currency: nft.currency,
          createdAt: new Date(),
          nftLockedAt: new Date(),
          expiresAt: lockExpiresAt,
        });

        await newOrder.save({ session });

        // Update queue session - user remains ACTIVE while selecting NFTs
        await this.queueSessionModel.updateOne(
          { walletAddress },
          {
            status: QueueSessionStatus.ACTIVE,
            selectionStartedAt: new Date(),
            currentOrderId: orderId,
          },
          { session },
        );
      });
    } catch (error) {
      throw error;
    } finally {
      await session.endSession();
    }

    return {
      success: true,
      lockExpiresAt,
      orderId,
      nftTokenId,
      lockTimeoutMinutes: saleConfig.selectionTimeoutMinutes,
    };
  }

  /**
   * Get available NFTs for purchase
   */
  async getAvailableNFTs(): Promise<AvailableNFTsResponseDto> {
    // Get available NFTs (not locked or lock expired)
    const availableNFTs = await this.nftInventoryModel
      .find({
        isActive: true,
        $or: [
          { status: NFTStatus.AVAILABLE },
          {
            status: NFTStatus.LOCKED,
            lockExpiresAt: { $lt: new Date() },
          },
        ],
      })
      .sort({ tokenId: 1 });

    // Get collection statistics
    const totalInCollection = await this.nftInventoryModel.countDocuments({
      isActive: true,
    });
    const currentlyLocked = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.LOCKED,
      lockExpiresAt: { $gt: new Date() },
    });
    const sold = await this.nftInventoryModel.countDocuments({
      status: NFTStatus.SOLD,
    });

    const nfts = availableNFTs.map((nft) => this.mapNFTToResponse(nft));

    return {
      nfts,
      totalAvailable: nfts.length,
      totalInCollection,
      currentlyLocked,
      sold,
    };
  }

  /**
   * Get specific NFT details and availability
   */
  async getNFTDetails(tokenId: string): Promise<NFTAvailabilityResponseDto> {
    const nft = await this.nftInventoryModel.findOne({ tokenId });

    if (!nft) {
      throw new NotFoundException('NFT not found');
    }

    const nftDetails = this.mapNFTToResponse(nft);
    let isAvailable = false;
    let unavailableReason: string | undefined;
    let availableAt: Date | undefined;

    if (!nft.isActive) {
      unavailableReason = 'NFT is not available for sale';
    } else if (nft.status === NFTStatus.SOLD) {
      unavailableReason = 'NFT has already been sold';
    } else if (nft.status === NFTStatus.RESERVED) {
      unavailableReason = 'NFT is reserved';
    } else if (nft.status === NFTStatus.UNAVAILABLE) {
      unavailableReason = 'NFT is temporarily unavailable';
    } else if (nft.status === NFTStatus.LOCKED) {
      if (nft.lockExpiresAt && nft.lockExpiresAt > new Date()) {
        unavailableReason = 'NFT is currently locked by another user';
        availableAt = nft.lockExpiresAt;
      } else {
        isAvailable = true; // Lock expired
      }
    } else if (nft.status === NFTStatus.AVAILABLE) {
      isAvailable = true;
    }

    return {
      nft: nftDetails,
      isAvailable,
      unavailableReason,
      availableAt,
    };
  }

  /**
   * Unlock expired NFTs (called by cleanup job)
   */
  async unlockExpiredNFTs(): Promise<number> {
    const result = await this.nftInventoryModel.updateMany(
      {
        status: NFTStatus.LOCKED,
        lockExpiresAt: { $lt: new Date() },
      },
      {
        status: NFTStatus.AVAILABLE,
        $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1, orderId: 1 },
      },
    );

    return result.modifiedCount;
  }

  /**
   * Mark NFT as sold (called when payment is completed)
   */
  async markNFTAsSold(tokenId: string, orderId: string): Promise<void> {
    await this.nftInventoryModel.updateOne(
      { tokenId, orderId },
      {
        status: NFTStatus.SOLD,
        $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1 },
      },
    );
  }

  /**
   * Unlock NFT (when order fails or expires)
   */
  async unlockNFT(tokenId: string, orderId: string): Promise<void> {
    await this.nftInventoryModel.updateOne(
      { tokenId, orderId },
      {
        status: NFTStatus.AVAILABLE,
        $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1, orderId: 1 },
      },
    );
  }

  /**
   * Map NFT document to response DTO
   */
  private mapNFTToResponse(nft: NFTInventoryDocument): NFTDetailsResponseDto {
    return {
      tokenId: nft.tokenId,
      collectionAddress: nft.collectionAddress,
      name: nft.name,
      description: nft.description,
      imageUrl: nft.imageUrl,
      metadataUrl: nft.metadataUrl,
      attributes: nft.attributes,
      price: nft.price,
      currency: nft.currency,
      status: nft.status,
      isAvailable:
        nft.status === NFTStatus.AVAILABLE ||
        (nft.status === NFTStatus.LOCKED &&
          nft.lockExpiresAt &&
          nft.lockExpiresAt < new Date()),
      lockedBy: nft.lockedBy,
      lockExpiresAt: nft.lockExpiresAt,
      orderId: nft.orderId,
    };
  }

  /**
   * Get current sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument> {
    const config = await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
    if (!config) {
      throw new NotFoundException('No active sale configuration found');
    }
    return config;
  }
}

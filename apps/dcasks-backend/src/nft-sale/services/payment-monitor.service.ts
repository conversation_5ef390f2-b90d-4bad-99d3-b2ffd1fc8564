import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import {
  PaymentRecord,
  PaymentRecordDocument,
  PaymentStatus,
  PaymentProvider,
  Order,
  OrderDocument,
  OrderStatus,
  NFTInventory,
  NFTInventoryDocument,
  NFTStatus,
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  SaleConfig,
  SaleConfigDocument,
} from '../models';

@Injectable()
export class PaymentMonitorService {
  private readonly logger = new Logger(PaymentMonitorService.name);

  constructor(
    @InjectModel(PaymentRecord.name)
    private paymentRecordModel: Model<PaymentRecordDocument>,
    @InjectModel(Order.name)
    private orderModel: Model<OrderDocument>,
    @InjectModel(NFTInventory.name)
    private nftInventoryModel: Model<NFTInventoryDocument>,
    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
  ) {}



  /**
   * Monitor payment status
   */
  async monitorPayments(): Promise<void> {
    try {
      await this.checkPendingPayments();
      await this.expireTimedOutPayments();
      await this.retryFailedPayments();
    } catch (error) {
      this.logger.error('Error in payment monitoring:', error);
    }
  }

  /**
   * Check status of pending payments
   */
  private async checkPendingPayments(): Promise<void> {
    const pendingPayments = await this.paymentRecordModel.find({
      status: {
        $in: [
          PaymentStatus.CREATED,
          PaymentStatus.PENDING,
          PaymentStatus.PROCESSING,
        ],
      },
      expiresAt: { $gt: new Date() }, // Not expired yet
    });

    for (const payment of pendingPayments) {
      try {
        await this.checkPaymentStatus(payment);
      } catch (error) {
        this.logger.error(
          `Error checking payment ${payment.paymentId}:`,
          error,
        );
      }
    }
  }

  /**
   * Check individual payment status
   */
  private async checkPaymentStatus(
    payment: PaymentRecordDocument,
  ): Promise<void> {
    // This would integrate with actual payment providers
    // For now, we'll simulate the logic

    if (payment.paymentProvider === PaymentProvider.STRIPE) {
      await this.checkStripePayment(payment);
    } else if (payment.paymentProvider === PaymentProvider.NOWPAYMENTS) {
      await this.checkNOWPayment(payment);
    }
  }

  /**
   * Check Stripe payment status
   */
  private async checkStripePayment(
    payment: PaymentRecordDocument,
  ): Promise<void> {
    // TODO: Integrate with actual Stripe API
    // For now, simulate payment completion after some time

    const paymentAge = Date.now() - payment.createdAt.getTime();
    const shouldComplete = paymentAge > 2 * 60 * 1000; // 2 minutes for simulation

    if (shouldComplete && payment.status === PaymentStatus.PENDING) {
      await this.completePayment(payment);
    }
  }

  /**
   * Check NOWPayments status
   */
  private async checkNOWPayment(payment: PaymentRecordDocument): Promise<void> {
    // TODO: Integrate with actual NOWPayments API
    // For now, simulate payment completion after some time

    const paymentAge = Date.now() - payment.createdAt.getTime();
    const shouldComplete = paymentAge > 5 * 60 * 1000; // 5 minutes for simulation

    if (shouldComplete && payment.status === PaymentStatus.PENDING) {
      await this.completePayment(payment);
    }
  }

  /**
   * Complete a payment
   */
  private async completePayment(payment: PaymentRecordDocument): Promise<void> {
    this.logger.log(`Completing payment: ${payment.paymentId}`);

    const session = await this.paymentRecordModel.db.startSession();

    try {
      await session.withTransaction(async () => {
        // Update payment status
        await this.paymentRecordModel.updateOne(
          { _id: payment._id },
          {
            status: PaymentStatus.COMPLETED,
            completedAt: new Date(),
            webhookReceived: true,
            webhookProcessedAt: new Date(),
          },
          { session },
        );

        // Update order status
        await this.orderModel.updateOne(
          { orderId: payment.orderId },
          {
            status: OrderStatus.PAYMENT_COMPLETED,
            paymentCompletedAt: new Date(),
          },
          { session },
        );

        // Mark NFT as sold
        const order = await this.orderModel.findOne({
          orderId: payment.orderId,
        });
        if (order) {
          await this.nftInventoryModel.updateOne(
            { tokenId: order.nftTokenId },
            {
              status: NFTStatus.SOLD,
              $unset: { lockedBy: 1, lockedAt: 1, lockExpiresAt: 1 },
            },
            { session },
          );

          // Complete queue session
          await this.queueSessionModel.updateOne(
            { walletAddress: order.walletAddress },
            { status: QueueSessionStatus.COMPLETED },
            { session },
          );
        }
      });

      this.logger.log(`Payment completed successfully: ${payment.paymentId}`);
    } catch (error) {
      this.logger.error(
        `Error completing payment ${payment.paymentId}:`,
        error,
      );
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Expire timed out payments
   */
  private async expireTimedOutPayments(): Promise<void> {
    const expiredPayments = await this.paymentRecordModel.find({
      status: {
        $in: [
          PaymentStatus.CREATED,
          PaymentStatus.PENDING,
          PaymentStatus.PROCESSING,
        ],
      },
      expiresAt: { $lt: new Date() },
    });

    for (const payment of expiredPayments) {
      try {
        await this.expirePayment(payment);
      } catch (error) {
        this.logger.error(
          `Error expiring payment ${payment.paymentId}:`,
          error,
        );
      }
    }
  }

  /**
   * Expire a payment
   */
  private async expirePayment(payment: PaymentRecordDocument): Promise<void> {
    this.logger.log(`Expiring payment: ${payment.paymentId}`);

    const session = await this.paymentRecordModel.db.startSession();

    try {
      await session.withTransaction(async () => {
        // Update payment status
        await this.paymentRecordModel.updateOne(
          { _id: payment._id },
          {
            status: PaymentStatus.EXPIRED,
            failedAt: new Date(),
            failureReason: 'Payment timeout',
          },
          { session },
        );

        // Update order status
        await this.orderModel.updateOne(
          { orderId: payment.orderId },
          { status: OrderStatus.EXPIRED },
          { session },
        );

        // Unlock NFT
        const order = await this.orderModel.findOne({
          orderId: payment.orderId,
        });
        if (order) {
          await this.nftInventoryModel.updateOne(
            { tokenId: order.nftTokenId, orderId: order.orderId },
            {
              status: NFTStatus.AVAILABLE,
              $unset: {
                lockedBy: 1,
                lockedAt: 1,
                lockExpiresAt: 1,
                orderId: 1,
              },
            },
            { session },
          );

          // Update queue session
          await this.queueSessionModel.updateOne(
            { walletAddress: order.walletAddress },
            {
              status: QueueSessionStatus.EXPIRED,
              $unset: { currentOrderId: 1, selectionExpiresAt: 1 },
            },
            { session },
          );
        }
      });

      this.logger.log(`Payment expired successfully: ${payment.paymentId}`);
    } catch (error) {
      this.logger.error(`Error expiring payment ${payment.paymentId}:`, error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Retry failed payments (if configured)
   */
  private async retryFailedPayments(): Promise<void> {
    const saleConfig = await this.getSaleConfig();
    if (!saleConfig) return;

    const failedPayments = await this.paymentRecordModel.find({
      status: PaymentStatus.FAILED,
      retryCount: { $lt: saleConfig.maxRetryAttempts },
      lastRetryAt: {
        $lt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes since last retry
      },
    });

    for (const payment of failedPayments) {
      try {
        await this.retryPayment(payment);
      } catch (error) {
        this.logger.error(
          `Error retrying payment ${payment.paymentId}:`,
          error,
        );
      }
    }
  }

  /**
   * Retry a failed payment
   */
  private async retryPayment(payment: PaymentRecordDocument): Promise<void> {
    this.logger.log(`Retrying payment: ${payment.paymentId}`);

    // Update retry count and timestamp
    await this.paymentRecordModel.updateOne(
      { _id: payment._id },
      {
        $inc: { retryCount: 1 },
        lastRetryAt: new Date(),
        status: PaymentStatus.PENDING,
      },
    );

    // TODO: Implement actual payment retry logic with payment providers
  }

  /**
   * Cleanup old payment records
   */
  async cleanupOldPayments(): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

      const result = await this.paymentRecordModel.deleteMany({
        status: {
          $in: [
            PaymentStatus.EXPIRED,
            PaymentStatus.CANCELLED,
            PaymentStatus.FAILED,
          ],
        },
        createdAt: { $lt: cutoffDate },
      });

      this.logger.log(`Cleaned up ${result.deletedCount} old payment records`);
    } catch (error) {
      this.logger.error('Error cleaning up old payments:', error);
    }
  }

  /**
   * Get current sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument | null> {
    return await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
  }

  /**
   * Manual method to check specific payment
   */
  async checkSpecificPayment(paymentId: string): Promise<void> {
    const payment = await this.paymentRecordModel.findOne({ paymentId });
    if (payment) {
      await this.checkPaymentStatus(payment);
    }
  }

  /**
   * Manual method to complete payment (for admin use)
   */
  async manualCompletePayment(paymentId: string): Promise<void> {
    const payment = await this.paymentRecordModel.findOne({ paymentId });
    if (payment && payment.status !== PaymentStatus.COMPLETED) {
      await this.completePayment(payment);
    }
  }

  /**
   * Manual method to expire payment (for admin use)
   */
  async manualExpirePayment(paymentId: string): Promise<void> {
    const payment = await this.paymentRecordModel.findOne({ paymentId });
    if (payment && payment.status !== PaymentStatus.EXPIRED) {
      await this.expirePayment(payment);
    }
  }
}

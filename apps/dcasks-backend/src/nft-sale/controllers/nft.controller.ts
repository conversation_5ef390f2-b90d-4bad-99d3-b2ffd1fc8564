import { Controller, Get, Param, Query } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { NFTService } from '../services';
import { NFTQueryResponseDto, NFTQueryParamsDto } from '../dto';

@ApiTags('NFT Management')
@Controller('api/v1/collaborations')
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  @Get('nfts/:tokenId')
  @ApiOperation({
    summary: 'Get NFT details',
    description: 'Retrieve detailed information about a specific NFT',
  })
  @ApiParam({
    name: 'tokenId',
    description: 'NFT token identifier',
    example: '1001',
  })
  @ApiResponse({
    status: 200,
    description: 'NFT details retrieved successfully',
    type: NFTQueryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'NFT not found',
  })
  async getNFTDetails(
    @Param('tokenId') tokenId: string,
  ): Promise<NFTQueryResponseDto> {
    return await this.nftService.queryNFTs(tokenId);
  }

  @Get('nfts')
  @ApiOperation({
    summary: 'Get NFTs with filtering',
    description:
      'Retrieve NFTs with optional filtering by status, price range, and pagination. ' +
      'Use this endpoint to get available NFTs by calling without parameters or with status=AVAILABLE.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by NFT status (AVAILABLE, LOCKED, SOLD, etc.)',
    example: 'AVAILABLE',
    enum: ['AVAILABLE', 'LOCKED', 'SOLD', 'UNAVAILABLE'],
  })
  @ApiQuery({
    name: 'minPrice',
    required: false,
    description: 'Minimum price filter (must be >= 0)',
    type: 'number',
    example: 100,
  })
  @ApiQuery({
    name: 'maxPrice',
    required: false,
    description: 'Maximum price filter (must be >= 0)',
    type: 'number',
    example: 500,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of results to return (1-100, default: 50)',
    type: 'number',
    example: 20,
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    description: 'Number of results to skip (default: 0)',
    type: 'number',
    example: 0,
  })
  @ApiResponse({
    status: 200,
    description: 'NFTs retrieved successfully',
    type: NFTQueryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters',
  })
  async getNFTs(
    @Query() queryParams: NFTQueryParamsDto,
  ): Promise<NFTQueryResponseDto> {
    return await this.nftService.queryNFTs(undefined, queryParams);
  }
}

import { Controller, Get, Param, Query } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { NFTService } from '../services';
import { NFTQueryResponseDto } from '../dto';

@ApiTags('NFT Management')
@Controller('api/v1/collaborations')
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  @Get('nfts/:tokenId')
  @ApiOperation({
    summary: 'Get NFT details',
    description: 'Retrieve detailed information about a specific NFT',
  })
  @ApiParam({
    name: 'tokenId',
    description: 'NFT token identifier',
    example: '1001',
  })
  @ApiResponse({
    status: 200,
    description: 'NFT details retrieved successfully',
    type: NFTQueryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'NFT not found',
  })
  async getNFTDetails(
    @Param('tokenId') tokenId: string,
  ): Promise<NFTQueryResponseDto> {
    return await this.nftService.queryNFTs(tokenId);
  }

  @Get('nfts')
  @ApiOperation({
    summary: 'Get NFTs with filtering',
    description:
      'Retrieve NFTs with optional filtering by status, price range, and pagination. ' +
      'Use this endpoint to get available NFTs by calling without parameters or with status=AVAILABLE.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by NFT status (AVAILABLE, LOCKED, SOLD, etc.)',
    example: 'AVAILABLE',
  })
  @ApiQuery({
    name: 'minPrice',
    required: false,
    description: 'Minimum price filter',
    type: 'number',
  })
  @ApiQuery({
    name: 'maxPrice',
    required: false,
    description: 'Maximum price filter',
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of results to return (default: 50)',
    type: 'number',
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    description: 'Number of results to skip (default: 0)',
    type: 'number',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    description: 'Filter by active status (default: true)',
    type: 'boolean',
  })
  @ApiResponse({
    status: 200,
    description: 'NFTs retrieved successfully',
    type: NFTQueryResponseDto,
  })
  async getNFTs(
    @Query('status') status?: string,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('isActive') isActive?: boolean,
  ): Promise<NFTQueryResponseDto> {
    // Validate and sanitize numeric parameters
    const sanitizedMinPrice = minPrice !== undefined && !isNaN(minPrice) && minPrice >= 0 ? minPrice : undefined;
    const sanitizedMaxPrice = maxPrice !== undefined && !isNaN(maxPrice) && maxPrice >= 0 ? maxPrice : undefined;
    const sanitizedLimit = limit !== undefined && !isNaN(limit) && limit > 0 ? Math.min(limit, 100) : undefined;
    const sanitizedOffset = offset !== undefined && !isNaN(offset) && offset >= 0 ? offset : undefined;

    return await this.nftService.queryNFTs(undefined, {
      status,
      minPrice: sanitizedMinPrice,
      maxPrice: sanitizedMaxPrice,
      limit: sanitizedLimit,
      offset: sanitizedOffset,
      isActive,
    });
  }
}

import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { NFTService } from '../services';
import {
  NFTDetailsResponseDto,
  AvailableNFTsResponseDto,
  NFTAvailabilityResponseDto,
} from '../dto';

@ApiTags('NFT Management')
@Controller('api/v1/collaborations')
export class NFTController {
  constructor(private readonly nftService: NFTService) {}

  @Get('nfts/available')
  @ApiOperation({
    summary: 'Get available NFTs',
    description: 'Retrieve list of all available NFTs for purchase',
  })
  @ApiResponse({
    status: 200,
    description: 'Available NFTs retrieved successfully',
    type: AvailableNFTsResponseDto,
  })
  async getAvailableNFTs(): Promise<AvailableNFTsResponseDto> {
    return await this.nftService.getAvailableNFTs();
  }

  @Get('nfts/:tokenId')
  @ApiOperation({
    summary: 'Get NFT details',
    description: 'Retrieve detailed information about a specific NFT',
  })
  @ApiParam({
    name: 'tokenId',
    description: 'NFT token identifier',
    example: '1001',
  })
  @ApiResponse({
    status: 200,
    description: 'NFT details retrieved successfully',
    type: NFTAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'NFT not found',
  })
  async getNFTDetails(
    @Param('tokenId') tokenId: string,
  ): Promise<NFTAvailabilityResponseDto> {
    return await this.nftService.getNFTDetails(tokenId);
  }

  @Get('nfts')
  @ApiOperation({
    summary: 'Get NFTs with filtering',
    description: 'Retrieve NFTs with optional filtering by status, price range, and pagination',
  })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by NFT status' })
  @ApiQuery({ name: 'minPrice', required: false, description: 'Minimum price filter' })
  @ApiQuery({ name: 'maxPrice', required: false, description: 'Maximum price filter' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of results to skip' })
  @ApiResponse({
    status: 200,
    description: 'NFTs retrieved successfully',
    type: AvailableNFTsResponseDto,
  })
  async getNFTs(
    @Query('status') status?: string,
    @Query('minPrice') minPrice?: number,
    @Query('maxPrice') maxPrice?: number,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<AvailableNFTsResponseDto> {
    // For now, return all available NFTs
    // TODO: Implement filtering logic based on query parameters
    return await this.nftService.getAvailableNFTs();
  }
}

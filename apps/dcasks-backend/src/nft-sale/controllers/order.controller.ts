import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { Request } from 'express';

import { OrderService } from '../services';
import {
  CreateOrderDto,
  OrderResponseDto,
  OrderDetailsResponseDto,
  OrderHistoryResponseDto,
} from '../dto';

@ApiTags('Order Management')
@Controller('api/v1/collaborations')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post('orders')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create new order',
    description:
      'Create a new NFT purchase order with locked NFT and payment details',
  })
  @ApiBody({ type: CreateOrderDto })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
    type: OrderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid order data or NFT not available',
  })
  @ApiResponse({
    status: 409,
    description: 'NFT already locked or order conflict',
  })
  async createOrder(
    @Body() createOrderDto: CreateOrderDto,
    @Req() request: Request,
  ): Promise<OrderResponseDto> {
    // Extract additional metadata from request
    const userAgent = request.headers['user-agent'];
    const ipAddress = request.ip || request.connection.remoteAddress;

    const enrichedDto = {
      ...createOrderDto,
      userAgent,
      ipAddress,
    };

    return await this.orderService.createOrder(enrichedDto);
  }

  @Get('orders/:orderId')
  @ApiOperation({
    summary: 'Get order details',
    description: 'Retrieve detailed information about a specific order',
  })
  @ApiParam({
    name: 'orderId',
    description: 'Unique order identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Order details retrieved successfully',
    type: OrderDetailsResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  async getOrderDetails(
    @Param('orderId') orderId: string,
  ): Promise<OrderDetailsResponseDto> {
    return await this.orderService.getOrderDetails(orderId);
  }

  @Get('orders/wallet/:walletAddress')
  @ApiOperation({
    summary: 'Get order history',
    description: 'Retrieve complete order history for a wallet address',
  })
  @ApiParam({
    name: 'walletAddress',
    description: 'Wallet address to get order history for',
    example: '******************************************',
  })
  @ApiResponse({
    status: 200,
    description: 'Order history retrieved successfully',
    type: OrderHistoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'No orders found for wallet address',
  })
  async getOrderHistory(
    @Param('walletAddress') walletAddress: string,
  ): Promise<OrderHistoryResponseDto> {
    return await this.orderService.getOrderHistory(walletAddress);
  }

  @Get('orders/wallet/:walletAddress/active')
  @ApiOperation({
    summary: 'Get active orders',
    description:
      'Retrieve only active (non-completed) orders for a wallet address',
  })
  @ApiParam({
    name: 'walletAddress',
    description: 'Wallet address to get active orders for',
    example: '******************************************',
  })
  @ApiResponse({
    status: 200,
    description: 'Active orders retrieved successfully',
    type: OrderHistoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'No active orders found for wallet address',
  })
  async getActiveOrders(
    @Param('walletAddress') walletAddress: string,
  ): Promise<OrderHistoryResponseDto> {
    const history = await this.orderService.getOrderHistory(walletAddress);

    // Filter to only active orders
    const activeOrders = history.orders.filter((order) =>
      [
        'created',
        'nft_locked',
        'payment_pending',
        'payment_completed',
        'nft_transfer_pending',
      ].includes(order.status),
    );

    return {
      orders: activeOrders,
      total: activeOrders.length,
      activeOrders: activeOrders.length,
      completedOrders: 0,
      failedOrders: 0,
    };
  }
}

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from '../auth/auth.module';

// Import all schemas
import {
  Order,
  OrderSchema,
  QueueSession,
  QueueSessionSchema,
  NFTInventory,
  NFTInventorySchema,
  PaymentRecord,
  PaymentRecordSchema,
  SaleConfig,
  SaleConfigSchema,
  ActivePoolTracking,
  ActivePoolTrackingSchema,
  AdminAction,
  AdminActionSchema,
} from './models';

// Import services
import {
  QueueService,
  NFTService,
  OrderService,
  PoolProcessorService,
  PaymentMonitorService,
  ConfigManagerService,
} from './services';

// Import controllers
import { QueueController, NFTController, OrderController } from './controllers';

@Module({
  imports: [
    AuthModule,
    MongooseModule.forFeature([
      // Order management and lifecycle tracking
      { name: Order.name, schema: OrderSchema },

      // Queue management and user session tracking
      { name: QueueSession.name, schema: QueueSessionSchema },

      // NFT collection and availability management
      { name: NFTInventory.name, schema: NFTInventorySchema },

      // Payment processing and webhook tracking
      { name: PaymentRecord.name, schema: PaymentRecordSchema },

      // System configuration and sale parameters
      { name: SaleConfig.name, schema: SaleConfigSchema },

      // Active pool management and statistics
      { name: ActivePoolTracking.name, schema: ActivePoolTrackingSchema },

      // Administrative actions and audit trail
      { name: AdminAction.name, schema: AdminActionSchema },
    ]),
  ],
  controllers: [QueueController, NFTController, OrderController],
  providers: [
    QueueService,
    NFTService,
    OrderService,
    PoolProcessorService,
    PaymentMonitorService,
    ConfigManagerService,
  ],
  exports: [
    MongooseModule,
    QueueService,
    NFTService,
    OrderService,
    PoolProcessorService,
    PaymentMonitorService,
    ConfigManagerService,
  ],
})
export class NFTSaleModule {}

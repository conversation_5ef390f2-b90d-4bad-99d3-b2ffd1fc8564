import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import Graph<PERSON>JSON from 'graphql-type-json';

export enum PaymentStatus {
  CREATED = 'CREATED',
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

export enum PaymentProvider {
  STRIPE = 'STRIPE',
  NOWPAYMENTS = 'NOWPAYMENTS',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'payment_records' })
export class PaymentRecord {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  paymentId: string;

  @Field()
  @Prop({ type: String, required: true, index: true })
  orderId: string;

  @Field()
  @Prop({ type: String, required: true, index: true })
  walletAddress: string;

  @Field(() => PaymentProvider)
  @Prop({ type: String, required: true, enum: PaymentProvider })
  paymentMethod: PaymentProvider;

  @Field()
  @Prop({ type: String, required: true })
  paymentProvider: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  paymentSessionId?: string;

  @Field()
  @Prop({ type: Number, required: true })
  amount: number;

  @Field()
  @Prop({ type: String, required: true })
  currency: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  originalAmount?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  originalCurrency?: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  exchangeRate?: number;

  @Field(() => PaymentStatus)
  @Prop({
    type: String,
    required: true,
    enum: PaymentStatus,
    index: true,
    default: PaymentStatus.CREATED,
  })
  status: PaymentStatus;

  @Field({ nullable: true })
  @Prop({ type: String })
  failureReason?: string;

  @Field({ nullable: true })
  @Prop({ type: String, index: true })
  externalTransactionId?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  externalSessionUrl?: string;

  @Field()
  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Field({ nullable: true })
  @Prop({ type: Date, index: true })
  expiresAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  completedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  failedAt?: Date;

  @Field()
  @Prop({ type: Boolean, default: false })
  webhookReceived: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  webhookData?: Record<string, any>;

  @Field({ nullable: true })
  @Prop({ type: Date })
  webhookProcessedAt?: Date;

  @Field()
  @Prop({ type: Number, default: 0 })
  webhookRetryCount: number;

  @Field()
  @Prop({ type: Number, default: 0 })
  retryCount: number;

  @Field({ nullable: true })
  @Prop({ type: Date })
  lastRetryAt?: Date;

  @Field(() => [String], { nullable: true })
  @Prop({ type: [String] })
  errorMessages?: string[];

  @Field({ nullable: true })
  @Prop({ type: String })
  userAgent?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  ipAddress?: string;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type PaymentRecordDocument = PaymentRecord & Document;
export const PaymentRecordSchema = SchemaFactory.createForClass(PaymentRecord);

// Additional indexes for performance
PaymentRecordSchema.index({ orderId: 1, status: 1 });
PaymentRecordSchema.index({ walletAddress: 1, status: 1 });
PaymentRecordSchema.index({ paymentMethod: 1, status: 1 });
PaymentRecordSchema.index({ expiresAt: 1 });
PaymentRecordSchema.index({ createdAt: -1 });
PaymentRecordSchema.index({ externalTransactionId: 1 }, { sparse: true });

import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true, collection: 'sale_config' })
export class SaleConfig {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  configVersion: string;

  @Field({ nullable: true })
  @Prop({ type: Date })
  saleStartTime?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  saleEndTime?: Date;

  @Field()
  @Prop({ type: Boolean, required: true, index: true, default: false })
  isActive: boolean;

  @Field()
  @Prop({ type: Boolean, default: false })
  isPaused: boolean;

  @Field({ nullable: true })
  @Prop({ type: String })
  pauseReason?: string;

  @Field()
  @Prop({ type: Number, required: true, default: 100 })
  maxQueueSize: number;

  @Field()
  @Prop({ type: Number, required: true, default: 100 })
  maxActivePoolSize: number;

  @Field()
  @Prop({ type: Number, default: 5 })
  activeSessionTimeoutMinutes: number; // Timeout for users in ACTIVE state before moving to PAYMENT

  @Field()
  @Prop({ type: Number, default: 5 })
  paymentTimeoutMinutes: number;

  @Field({ nullable: true })
  @Prop({ type: Number })
  queueSessionTimeoutMinutes?: number;

  @Field(() => [String])
  @Prop({ type: [String], default: ['stripe', 'nowpayments'] })
  enabledPaymentMethods: string[];

  @Field({ nullable: true })
  @Prop({ type: Number })
  stripeTimeoutMinutes?: number;

  @Field({ nullable: true })
  @Prop({ type: Number })
  selectionTimeoutMinutes?: number;

  @Field()
  @Prop({ type: Number, default: 20 })
  nowpaymentsTimeoutMinutes: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  nftCollectionAddress?: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  nftBasePrice?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  nftCurrency?: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  maxConcurrentPayments?: number;

  @Field()
  @Prop({ type: Number, default: 3 })
  maxRetryAttempts: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  createdBy?: string;

  @Field()
  @Prop({ type: Date })
  createdAt: Date;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type SaleConfigDocument = SaleConfig & Document;
export const SaleConfigSchema = SchemaFactory.createForClass(SaleConfig);

// Additional indexes for performance
SaleConfigSchema.index({ isActive: 1, isPaused: 1 });
SaleConfigSchema.index({ saleStartTime: 1, saleEndTime: 1 });
SaleConfigSchema.index({ createdAt: -1 });

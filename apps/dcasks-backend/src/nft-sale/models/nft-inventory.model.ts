import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import GraphQLJSON from 'graphql-type-json';

export enum NFTStatus {
  AVAILABLE = 'AVAILABLE',
  LOCKED = 'LOCKED',
  SOLD = 'SOLD',
  UNAVAILABLE = 'UNAVAILABLE',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'nft_inventory' })
export class NFTInventory {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  tokenId: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  collectionAddress?: string;

  @Field()
  @Prop({ type: String, required: true })
  name: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  description?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  imageUrl?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  metadataUrl?: string;

  @Field(() => [GraphQLJSON], { nullable: true })
  @Prop({ type: [Object] })
  attributes?: Record<string, any>[];

  @Field()
  @Prop({ type: Number, required: true })
  price: number;

  @Field()
  @Prop({ type: String, required: true })
  currency: string;

  @Field(() => NFTStatus)
  @Prop({
    type: String,
    required: true,
    enum: NFTStatus,
    index: true,
    default: NFTStatus.AVAILABLE,
  })
  status: NFTStatus;

  @Field({ nullable: true })
  @Prop({ type: String, index: true })
  lockedBy?: string;

  @Field({ nullable: true })
  @Prop({ type: Date })
  lockedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date, index: true })
  lockExpiresAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: String })
  orderId?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  currentOwner?: string;

  @Field(() => [GraphQLJSON], { nullable: true })
  @Prop({ type: [Object] })
  transferHistory?: Record<string, any>[];

  @Field()
  @Prop({ type: Date })
  createdAt: Date;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type NFTInventoryDocument = NFTInventory & Document;
export const NFTInventorySchema = SchemaFactory.createForClass(NFTInventory);

// Additional indexes for performance
NFTInventorySchema.index({ status: 1 });
NFTInventorySchema.index({ lockedBy: 1, status: 1 });
NFTInventorySchema.index({ lockExpiresAt: 1 });
NFTInventorySchema.index({ price: 1, currency: 1 });
NFTInventorySchema.index({ orderId: 1 });

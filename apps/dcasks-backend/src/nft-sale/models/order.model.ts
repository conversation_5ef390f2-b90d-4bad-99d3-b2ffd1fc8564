import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import Graph<PERSON><PERSON><PERSON>N from 'graphql-type-json';

export enum OrderStatus {
  CREATED = 'CREATED',
  NFT_LOCKED = 'NFT_LOCKED',
  PAYMENT_PENDING = 'PAYMENT_PENDING',
  PAYMENT_COMPLETED = 'PAYMENT_COMPLETED',
  NFT_TRANSFER_PENDING = 'NFT_TRANSFER_PENDING',
  NFT_TRANSFERRED = 'NFT_TRANSFERRED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
}

export enum PaymentMethod {
  STRIPE = 'STRIPE',
  NOWPAYMENTS = 'NOWPAYMENTS',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'orders' })
export class Order {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  orderId: string;

  @Field()
  @Prop({ type: String, required: true, index: true })
  walletAddress: string;

  @Field({ nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'QueueSession' })
  queueSessionId?: Types.ObjectId;

  @Field(() => [String])
  @Prop({ type: [String], required: true })
  nftTokenIds: string[];

  @Field(() => OrderStatus)
  @Prop({
    type: String,
    required: true,
    enum: OrderStatus,
    index: true,
    default: OrderStatus.CREATED,
  })
  status: OrderStatus;

  @Field({ nullable: true })
  @Prop({ type: String })
  substatus?: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  totalPrice?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  currency?: string;

  @Field()
  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  nftLockedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentInitiatedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentCompletedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  nftTransferInitiatedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  nftTransferCompletedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date, index: true })
  expiresAt?: Date;

  @Field(() => PaymentMethod, { nullable: true })
  @Prop({ type: String, enum: PaymentMethod })
  paymentMethod?: PaymentMethod;

  @Field({ nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'PaymentRecord' })
  paymentRecordId?: Types.ObjectId;

  @Field({ nullable: true })
  @Prop({ type: String })
  transferTransactionHash?: string;

  @Field({ nullable: true })
  @Prop({ type: Number, default: 0 })
  transferRetryCount?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  transferErrorMessage?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  userAgent?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  ipAddress?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  metadata?: Record<string, any>;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type OrderDocument = Order & Document;
export const OrderSchema = SchemaFactory.createForClass(Order);

// Additional indexes for performance
OrderSchema.index({ walletAddress: 1, status: 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ expiresAt: 1 });

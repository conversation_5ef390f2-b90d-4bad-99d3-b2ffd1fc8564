import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum PoolStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  CLOSED = 'CLOSED',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'active_pool_tracking' })
export class ActivePoolTracking {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  poolId: string;

  @Field()
  @Prop({ type: Number, required: true, default: 100 })
  maxPoolSize: number;

  @Field()
  @Prop({ type: Number, required: true, default: 0 })
  currentPoolSize: number;

  @Field(() => PoolStatus)
  @Prop({
    type: String,
    required: true,
    enum: PoolStatus,
    index: true,
    default: PoolStatus.ACTIVE,
  })
  status: PoolStatus;

  @Field(() => [String])
  @Prop({ type: [String], default: [] })
  activeUsers: string[];

  @Field()
  @Prop({ type: Number, default: 0 })
  totalProcessed: number;

  @Field()
  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Field()
  @Prop({ type: Date, default: () => new Date() })
  lastUpdatedAt: Date;

  @Field()
  @Prop({ type: Number, default: 0 })
  successfulPurchases: number;

  @Field()
  @Prop({ type: Number, default: 0 })
  failedPurchases: number;

  @Field()
  @Prop({ type: Number, default: 0 })
  timeoutCount: number;

  @Field({ nullable: true })
  @Prop({ type: Number })
  averageSelectionTime?: number;

  @Field({ nullable: true })
  @Prop({ type: Number })
  averagePaymentTime?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  saleConfigVersion?: string;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type ActivePoolTrackingDocument = ActivePoolTracking & Document;
export const ActivePoolTrackingSchema =
  SchemaFactory.createForClass(ActivePoolTracking);

// Additional indexes for performance
ActivePoolTrackingSchema.index({ status: 1, currentPoolSize: 1 });
ActivePoolTrackingSchema.index({ createdAt: -1 });
ActivePoolTrackingSchema.index({ lastUpdatedAt: -1 });
ActivePoolTrackingSchema.index({ poolId: 1, status: 1 });

import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import Graph<PERSON>JSON from 'graphql-type-json';

export enum AdminActionType {
  SALE_START = 'SALE_START',
  SALE_PAUSE = 'SALE_PAUSE',
  SALE_STOP = 'SALE_STOP',
  MANUAL_NFT_TRANSFER = 'MANUAL_NFT_TRANSFER',
  QUEUE_RESET = 'QUEUE_RESET',
  ORDER_CANCEL = 'ORDER_CANCEL',
  CONFIG_UPDATE = 'CONFIG_UPDATE',
  POOL_RESET = 'POOL_RESET',
  PAYMENT_RETRY = 'PAYMENT_RETRY',
}

export enum TargetEntity {
  SALE = 'SALE',
  ORDER = 'ORDER',
  QUEUE = 'QUEUE',
  NFT = 'NFT',
  PAYMENT = 'PAYMENT',
  POOL = 'POOL',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'admin_actions' })
export class AdminAction {
  @Field()
  _id: string;

  @Field(() => AdminActionType)
  @Prop({ type: String, required: true, enum: AdminActionType, index: true })
  actionType: AdminActionType;

  @Field()
  @Prop({ type: String, required: true, index: true })
  adminUser: string;

  @Field(() => TargetEntity, { nullable: true })
  @Prop({ type: String, enum: TargetEntity })
  targetEntity?: TargetEntity;

  @Field({ nullable: true })
  @Prop({ type: String })
  targetId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  actionData?: Record<string, any>;

  @Field({ nullable: true })
  @Prop({ type: String })
  reason?: string;

  @Field()
  @Prop({ type: Date, required: true, index: true })
  executedAt: Date;

  @Field()
  @Prop({ type: Boolean, default: true })
  success: boolean;

  @Field({ nullable: true })
  @Prop({ type: String })
  errorMessage?: string;

  @Field({ nullable: true })
  @Prop({ type: Number })
  affectedRecords?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  ipAddress?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  userAgent?: string;

  @Field()
  @Prop({ type: Date })
  createdAt: Date;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type AdminActionDocument = AdminAction & Document;
export const AdminActionSchema = SchemaFactory.createForClass(AdminAction);

// Additional indexes for performance
AdminActionSchema.index({ executedAt: -1 });
AdminActionSchema.index({ actionType: 1, executedAt: -1 });
AdminActionSchema.index({ adminUser: 1, executedAt: -1 });
AdminActionSchema.index({ targetEntity: 1, targetId: 1 });
AdminActionSchema.index({ success: 1, executedAt: -1 });

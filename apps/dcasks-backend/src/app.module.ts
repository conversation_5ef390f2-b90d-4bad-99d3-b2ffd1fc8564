import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { MongooseModule } from '@nestjs/mongoose';
import { SentryGlobalFilter, SentryModule } from '@sentry/nestjs/setup';
import { GraphQLFormattedError } from 'graphql';
import GraphQLJSON from 'graphql-type-json';
import { join } from 'path';

import { AdminModule } from 'src/admin/admin.module';
import { AuthModule } from 'src/auth/auth.module';
import { CaskOwnerModule } from 'src/cask-owner/cask-owner.module';
import { CaskModule } from 'src/cask/cask.module';
import { MarketModule } from 'src/market/market.module';
import { NFTModule } from 'src/nft/nft.module';
import { ProviderModule } from 'src/provider/provider.module';
import { SystemActivityModule } from 'src/system-activity/system-activity.module';
import { UploadModule } from 'src/upload/upload.module';
import { UserModule } from 'src/user/user.module';
import { ConfigurationModule } from './configuration/configuration.module';
import { MiningSolModule } from './mining-sol/mining-sol.module';
// import { MiningModule } from './mining/mining.module';
import { NotificationModule } from './notification/notification.module';
import { HealthModule } from './health/health.module';
import { NFTSaleModule } from './nft-sale/nft-sale.module';
import { isProduction } from './utils/env';
import { APP_FILTER } from '@nestjs/core';
import { LoggerMiddleware } from './middlewares/logger.middleware';

@Module({
  imports: [
    SentryModule.forRoot(),
    ConfigModule.forRoot({ isGlobal: true, cache: true }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          uri: configService.get<string>('DATABASE_URI'),
        };
      },
    }),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        console.log('isProduction', isProduction());
        return {
          debug: !isProduction(),
          playground: !isProduction(),
          introspection: !isProduction(),
          autoSchemaFile: join(process.cwd(), 'schema.gql'),
          sortSchema: true,
          cache: 'bounded',
          uploads: false,
          resolvers: { JSON: GraphQLJSON },
          context: ({ req, res }) => {
            return { req, res };
          },
          formatError: (error: any) => {
            if (error.extensions.code == 'INTERNAL_SERVER_ERROR') {
              console.log('error', error);
              return {
                message: 'INTERNAL_SERVER_ERROR',
                statusCode: 500,
                error: 'INTERNAL_SERVER_ERROR',
                ...(isProduction ? {} : error),
              };
            }
            const graphQLFormattedError: GraphQLFormattedError = {
              message: error.message,
              ...error.extensions.response,
            };
            return graphQLFormattedError;
          },
        };
      },
    }),
    ProviderModule,
    AuthModule,
    UserModule,
    AdminModule,
    CaskModule,
    CaskOwnerModule,
    MarketModule,
    UploadModule,
    NFTModule,
    SystemActivityModule,
    ConfigurationModule,
    // MiningModule,
    MiningSolModule,
    NotificationModule,
    HealthModule,
    NFTSaleModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('/');
  }
}

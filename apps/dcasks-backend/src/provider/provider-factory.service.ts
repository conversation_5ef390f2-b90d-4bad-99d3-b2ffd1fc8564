import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ethers, providers } from 'ethers';
import { ContractFactoryService } from './contract-factory.service';

@Injectable()
export class ProviderFactoryService {
  private logger = new Logger(ProviderFactoryService.name);

  private _httpProvider: providers.JsonRpcProvider;
  private _wssProvider: providers.WebSocketProvider;
  private _ethProvider: providers.JsonRpcProvider;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => ContractFactoryService))
    private readonly contractFactory: ContractFactoryService,
  ) {
    this.initializeProviders();
  }

  /**
   * Initialize all providers based on configuration
   */
  private initializeProviders(): void {
    const HTTP_NETWORK_URL = this.configService.get<string>('NETWORK_URL');
    const WSS_NETWORK_URL = this.configService.get<string>('WSS_NETWORK_URL');
    const ETH_NETWORK_URL = this.configService.get<string>('ETH_NETWORK_URL');

    // Initialize HTTP provider
    this._httpProvider = new ethers.providers.JsonRpcProvider({
      url: HTTP_NETWORK_URL || '',
      timeout: 10000,
      allowGzip: true,
    });

    // Initialize WSS provider if URL is provided
    if (WSS_NETWORK_URL) {
      this._wssProvider = new ethers.providers.WebSocketProvider(
        WSS_NETWORK_URL || '',
      );
      this.setupWebSocketReconnection(WSS_NETWORK_URL);
    }

    // Initialize ETH provider
    this._ethProvider = new ethers.providers.JsonRpcProvider({
      url: ETH_NETWORK_URL || '',
      timeout: 10000,
    });
  }

  /**
   * Setup WebSocket reconnection handling
   */
  private setupWebSocketReconnection(wssUrl: string): void {
    if (!this._wssProvider || !this._wssProvider._websocket) {
      return;
    }

    this._wssProvider._websocket.on('error', (error: unknown) => {
      this.logger.warn('WebSocket Error - Reconnecting...', error);
      setTimeout(() => {
        this._wssProvider = new ethers.providers.WebSocketProvider(wssUrl);
        this.setupWebSocketReconnection(wssUrl);

        // Reconnect contracts after provider reconnection
        if (this.contractFactory) {
          this.contractFactory.reconnectWssContracts();
        }
      }, 5000);
    });

    this._wssProvider._websocket.on('close', () => {
      this.logger.warn('WebSocket Closed - Reconnecting...');
      setTimeout(() => {
        this._wssProvider = new ethers.providers.WebSocketProvider(wssUrl);
        this.setupWebSocketReconnection(wssUrl);

        // Reconnect contracts after provider reconnection
        if (this.contractFactory) {
          this.contractFactory.reconnectWssContracts();
        }
      }, 5000);
    });

    this._wssProvider._websocket.on('open', () => {
      this.logger.log('WebSocket Connected Successfully');
    });
  }

  /**
   * Get HTTP provider for blockchain queries
   */
  get httpProvider(): providers.JsonRpcProvider {
    return this._httpProvider;
  }

  /**
   * Get WebSocket provider for event listening
   */
  get wssProvider(): providers.WebSocketProvider {
    return this._wssProvider;
  }

  /**
   * Get ETH provider for Ethereum mainnet
   */
  get ethProvider(): providers.JsonRpcProvider {
    return this._ethProvider;
  }

  /**
   * Get the legacy provider (for backward compatibility)
   */
  get legacyProvider():
    | providers.JsonRpcProvider
    | providers.WebSocketProvider {
    return this._wssProvider || this._httpProvider;
  }

  /**
   * Check if WebSocket provider is available
   */
  hasWssProvider(): boolean {
    return !!this._wssProvider;
  }
}

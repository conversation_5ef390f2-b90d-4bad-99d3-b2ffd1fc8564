import { Injectable } from '@nestjs/common';

@Injectable()
export class ContractFactoryService {
  // Mock contract instances
  private _mockDcaskBottle = {
    address: '0xBottle',
    on: jest.fn(),
    creatorNonce: jest.fn().mockResolvedValue(0),
  };

  private _mockDcaskMarketplace = {
    address: '0xMarketplace',
    on: jest.fn(),
  };

  private _mockDcaskProfitModel = {
    address: '0xProfitModel',
    on: jest.fn(),
  };

  private _mockDcaskMembership = {
    address: '0xMembership',
    on: jest.fn(),
  };

  private _mockDcaskWholeCask = {
    address: '0xWholeCask',
    on: jest.fn(),
  };

  private _mockDcaskTulip = {
    address: '0xTulip',
    on: jest.fn(),
  };

  private _mockEthDCaskMembership = {
    address: '0xEthMembership',
    on: jest.fn(),
  };

  private _mockWokens = {
    address: '0xWokens',
  };

  private _mockMultiTokenTransfer = {
    address: '0xMultiTokenTransfer',
  };

  private _httpContracts = {
    dcaskBottle: this._mockDcaskBottle,
    dcaskMarketplace: this._mockDcaskMarketplace,
    dcaskProfitModel: this._mockDcaskProfitModel,
    dcaskMembership: this._mockDcaskMembership,
    dcaskWholeCask: this._mockDcaskWholeCask,
    dcaskTulip: this._mockDcaskTulip,
    wokens: this._mockWokens,
    multiTokenTransfer: this._mockMultiTokenTransfer,
  };

  private _wssContracts = {
    dcaskBottle: this._mockDcaskBottle,
    dcaskMarketplace: this._mockDcaskMarketplace,
    dcaskProfitModel: this._mockDcaskProfitModel,
    dcaskMembership: this._mockDcaskMembership,
    dcaskWholeCask: this._mockDcaskWholeCask,
    dcaskTulip: this._mockDcaskTulip,
  };

  get httpContracts() {
    return this._httpContracts;
  }

  get wssContracts() {
    return this._wssContracts;
  }

  get ethDCaskMembership() {
    return this._mockEthDCaskMembership;
  }

  hasWssContracts(): boolean {
    return true;
  }

  reconnectWssContracts(): void {
    // No-op in mock
  }
} 
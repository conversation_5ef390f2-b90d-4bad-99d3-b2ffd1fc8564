import { Injectable } from '@nestjs/common';
import { ethers, providers } from 'ethers';

@Injectable()
export class ProviderFactoryService {
  private _httpProvider = {
    // Basic mock of JsonRpcProvider
  } as unknown as providers.JsonRpcProvider;

  private _wssProvider = {
    _websocket: {
      on: jest.fn(),
    },
    // Basic mock of WebSocketProvider
  } as unknown as providers.WebSocketProvider;

  private _ethProvider = {
    // Basic mock of JsonRpcProvider for Ethereum
  } as unknown as providers.JsonRpcProvider;

  get httpProvider(): providers.JsonRpcProvider {
    return this._httpProvider;
  }

  get wssProvider(): providers.WebSocketProvider {
    return this._wssProvider;
  }

  get ethProvider(): providers.JsonRpcProvider {
    return this._ethProvider;
  }

  get legacyProvider(): providers.JsonRpcProvider | providers.WebSocketProvider {
    return this._wssProvider || this._httpProvider;
  }

  hasWssProvider(): boolean {
    return true;
  }
} 
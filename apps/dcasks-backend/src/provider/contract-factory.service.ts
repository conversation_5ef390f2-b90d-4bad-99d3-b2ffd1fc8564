import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import {
  DCaskBottle,
  DCaskBottle__factory,
  DCaskMarketplace,
  DCaskMarketplace__factory,
  DCaskMembership,
  DCaskMembership__factory,
  DCaskNft,
  DCaskNft__factory,
  DCaskProfitModel,
  DCaskProfitModel__factory,
  DCaskTulip,
  DCaskTulip__factory,
} from 'src/common/contracts/types';
import { MultiTokenTransfer } from 'src/common/contracts/types/MultiTokenTransfer';
import { Wokens } from 'src/common/contracts/types/Wokens';
import { MultiTokenTransfer__factory } from 'src/common/contracts/types/factories/MultiTokenTransfer__factory';
import { Wokens__factory } from 'src/common/contracts/types/factories/Wokens__factory';
import { ProviderFactoryService } from './provider-factory.service';

/**
 * Contract type with HTTP provider connection
 */
export interface HttpContracts {
  dcaskBottle: DCaskBottle;
  dcaskMarketplace: DCaskMarketplace;
  dcaskProfitModel: DCaskProfitModel;
  dcaskMembership: DCaskMembership;
  dcaskWholeCask: DCaskNft;
  dcaskTulip: DCaskTulip;
  wokens: Wokens;
  multiTokenTransfer: MultiTokenTransfer;
}

/**
 * Contract type with WebSocket provider connection
 */
export interface WssContracts {
  dcaskBottle: DCaskBottle;
  dcaskMarketplace: DCaskMarketplace;
  dcaskProfitModel: DCaskProfitModel;
  dcaskMembership: DCaskMembership;
  dcaskWholeCask: DCaskNft;
  dcaskTulip: DCaskTulip;
}

@Injectable()
export class ContractFactoryService {
  private logger = new Logger(ContractFactoryService.name);

  private _httpContracts: HttpContracts;
  private _wssContracts: WssContracts;
  private _ethDCaskMembership: DCaskMembership;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => ProviderFactoryService))
    private readonly providerFactory: ProviderFactoryService,
  ) {
    this.initializeContracts();
  }

  /**
   * Initialize all contracts
   */
  private initializeContracts(): void {
    this.initializeHttpContracts();
    this.initializeWssContracts();
    this.initializeEthContracts();
  }

  /**
   * Initialize contracts with HTTP provider
   */
  private initializeHttpContracts(): void {
    const httpProvider = this.providerFactory.httpProvider;
    const CONTRACT_BOTTLE = this.configService.get<string>('CONTRACT_BOTTLE');
    const CONTRACT_MARKETPLACE = this.configService.get<string>(
      'CONTRACT_MARKETPLACE',
    );
    const CONTRACT_PROFIT_MODEL = this.configService.get<string>(
      'CONTRACT_PROFIT_MODEL',
    );
    const CONTRACT_MEMBERSHIP = this.configService.get<string>(
      'CONTRACT_MEMBERSHIP',
    );
    const CONTRACT_WHOLE_CASK = this.configService.get<string>(
      'CONTRACT_WHOLE_CASK',
    );
    const CONTRACT_TULIP = this.configService.get<string>('CONTRACT_TULIP');
    const CONTRACT_WOKENS = this.configService.get<string>('CONTRACT_WOKENS');
    const CONTRACT_MULTI_TOKEN_TRANSFER = this.configService.get<string>(
      'CONTRACT_MULTI_TOKEN_TRANSFER',
    );

    this._httpContracts = {
      dcaskBottle: DCaskBottle__factory.connect(
        CONTRACT_BOTTLE || '',
        httpProvider,
      ),
      dcaskMarketplace: DCaskMarketplace__factory.connect(
        CONTRACT_MARKETPLACE || '',
        httpProvider,
      ),
      dcaskProfitModel: DCaskProfitModel__factory.connect(
        CONTRACT_PROFIT_MODEL || '',
        httpProvider,
      ),
      dcaskMembership: DCaskMembership__factory.connect(
        CONTRACT_MEMBERSHIP || '',
        httpProvider,
      ),
      dcaskWholeCask: DCaskNft__factory.connect(
        CONTRACT_WHOLE_CASK || '',
        httpProvider,
      ),
      dcaskTulip: DCaskTulip__factory.connect(
        CONTRACT_TULIP || '',
        httpProvider,
      ),
      wokens: Wokens__factory.connect(CONTRACT_WOKENS || '', httpProvider),
      multiTokenTransfer: MultiTokenTransfer__factory.connect(
        CONTRACT_MULTI_TOKEN_TRANSFER || '',
        httpProvider,
      ),
    };
  }

  /**
   * Initialize contracts with WebSocket provider (if available)
   */
  private initializeWssContracts(): void {
    const wssProvider = this.providerFactory.wssProvider;

    if (!wssProvider) {
      this.logger.warn(
        'WebSocket provider not available. WSS contracts will not be initialized.',
      );
      return;
    }

    const CONTRACT_BOTTLE = this.configService.get<string>('CONTRACT_BOTTLE');
    const CONTRACT_MARKETPLACE = this.configService.get<string>(
      'CONTRACT_MARKETPLACE',
    );
    const CONTRACT_PROFIT_MODEL = this.configService.get<string>(
      'CONTRACT_PROFIT_MODEL',
    );
    const CONTRACT_MEMBERSHIP = this.configService.get<string>(
      'CONTRACT_MEMBERSHIP',
    );
    const CONTRACT_WHOLE_CASK = this.configService.get<string>(
      'CONTRACT_WHOLE_CASK',
    );
    const CONTRACT_TULIP = this.configService.get<string>('CONTRACT_TULIP');

    this._wssContracts = {
      dcaskBottle: DCaskBottle__factory.connect(
        CONTRACT_BOTTLE || '',
        wssProvider,
      ),
      dcaskMarketplace: DCaskMarketplace__factory.connect(
        CONTRACT_MARKETPLACE || '',
        wssProvider,
      ),
      dcaskProfitModel: DCaskProfitModel__factory.connect(
        CONTRACT_PROFIT_MODEL || '',
        wssProvider,
      ),
      dcaskMembership: DCaskMembership__factory.connect(
        CONTRACT_MEMBERSHIP || '',
        wssProvider,
      ),
      dcaskWholeCask: DCaskNft__factory.connect(
        CONTRACT_WHOLE_CASK || '',
        wssProvider,
      ),
      dcaskTulip: DCaskTulip__factory.connect(
        CONTRACT_TULIP || '',
        wssProvider,
      ),
    };
  }

  /**
   * Initialize Ethereum mainnet contracts
   */
  private initializeEthContracts(): void {
    const ethProvider = this.providerFactory.ethProvider;
    const ETH_CONTRACT_MEMBERSHIP = this.configService.get<string>(
      'ETH_CONTRACT_MEMBERSHIP',
    );

    this._ethDCaskMembership = DCaskMembership__factory.connect(
      ETH_CONTRACT_MEMBERSHIP || '',
      ethProvider,
    );
  }

  /**
   * Reconnect all WebSocket contracts
   * Should be called after WebSocket provider reconnection
   */
  public reconnectWssContracts(): void {
    this.logger.log('Reconnecting WSS contracts after provider reconnection');
    this.initializeWssContracts();
  }

  /**
   * Get HTTP provider connected contracts
   */
  get httpContracts(): HttpContracts {
    return this._httpContracts;
  }

  /**
   * Get WebSocket provider connected contracts
   */
  get wssContracts(): WssContracts | null {
    return this._wssContracts || null;
  }

  /**
   * Get Ethereum mainnet membership contract
   */
  get ethDCaskMembership(): DCaskMembership {
    return this._ethDCaskMembership;
  }

  /**
   * Check if WebSocket contracts are available
   */
  hasWssContracts(): boolean {
    return !!this._wssContracts;
  }
}

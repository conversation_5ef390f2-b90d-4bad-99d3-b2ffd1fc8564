#!/usr/bin/env ts-node

import { DatabaseUtils } from './db-utils';
import * as readline from 'readline';

async function askForConfirmation(message: string): Promise<boolean> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function cleanupDatabase(force: boolean = false) {
  const dbUtils = new DatabaseUtils();

  try {
    console.log('🧹 Starting database cleanup for SIT environment...\n');

    // Initialize database connection
    await dbUtils.initialize();

    // Safety confirmation (unless forced)
    if (!force) {
      console.log(
        '⚠️  WARNING: This will permanently delete ALL data in the SIT database!',
      );
      console.log('   This action cannot be undone.');
      console.log('   Collections that will be dropped:');
      console.log('   • sale_config');
      console.log('   • active_pool_tracking');
      console.log('   • nft_inventory');
      console.log('   • queue_sessions');
      console.log('   • orders');
      console.log('   • payment_records');
      console.log('   • admin_actions');
      console.log('   • Any other collections in the database\n');

      const confirmed = await askForConfirmation(
        'Are you sure you want to proceed?',
      );

      if (!confirmed) {
        console.log('❌ Database cleanup cancelled by user');
        return;
      }
    }

    console.log('\n🗑️  Proceeding with database cleanup...');

    // Drop all collections
    await dbUtils.dropAllCollections();

    // Verify cleanup
    const db = dbUtils.getDb();
    const collections = await db.listCollections().toArray();

    if (collections.length === 0) {
      console.log('\n✅ Database cleanup completed successfully!');
      console.log('   All collections have been removed');
      console.log('   Database is now empty and ready for fresh seeding');
    } else {
      console.log('\n⚠️  Warning: Some collections may still exist:');
      collections.forEach((col) => console.log(`   • ${col.name}`));
    }
  } catch (error) {
    console.error('❌ Database cleanup failed:', error.message);
    process.exit(1);
  } finally {
    await dbUtils.cleanup();
  }
}

// Handle command line execution
if (require.main === module) {
  // Check for --force flag
  const force = process.argv.includes('--force');

  cleanupDatabase(force).catch((error) => {
    console.error('❌ Cleanup script failed:', error);
    process.exit(1);
  });
}

export { cleanupDatabase };

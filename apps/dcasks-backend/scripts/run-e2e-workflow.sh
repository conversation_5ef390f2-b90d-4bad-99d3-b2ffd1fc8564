#!/bin/bash

# E2E Test Workflow Script
# Usage: ./scripts/run-e2e-workflow.sh [options]
# Options:
#   --skip-seed     Skip database seeding
#   --skip-cleanup  Skip database cleanup
#   --no-server     Don't start/stop server (assume it's already running)

set -e # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default options
SKIP_SEED=false
SKIP_CLEANUP=false
NO_SERVER=false
SERVER_PID=""

# Parse command line arguments
for arg in "$@"; do
  case $arg in
  --skip-seed)
    SKIP_SEED=true
    shift
    ;;
  --skip-cleanup)
    SKIP_CLEANUP=true
    shift
    ;;
  --no-server)
    NO_SERVER=true
    shift
    ;;
  *)
    echo "Unknown option: $arg"
    echo "Usage: $0 [--skip-seed] [--skip-cleanup] [--no-server]"
    exit 1
    ;;
  esac
done

# Function to print colored output
print_step() {
  echo -e "${BLUE}🚀 $1${NC}"
}

print_success() {
  echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
  echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
  echo -e "${RED}❌ $1${NC}"
}

# Function to check if server is running
check_server() {
  if curl -s http://localhost:9000/health >/dev/null 2>&1; then
    return 0
  else
    return 1
  fi
}

# Function to start server
start_server() {
  if [ "$NO_SERVER" = true ]; then
    print_warning "Skipping server start (--no-server flag)"
    return 0
  fi

  print_step "Step 1: Starting SIT server..."

  if check_server; then
    print_success "SIT server is already running"
    return 0
  fi

  # Start server in background
  NODE_ENV=sit yarn start:dev >/tmp/sit-server.log 2>&1 &
  SERVER_PID=$!

  # Wait for server to start
  print_step "Step 2: Waiting for server to be ready..."
  for i in {1..30}; do
    if check_server; then
      print_success "Server is ready"
      return 0
    fi
    sleep 1
  done

  print_error "Server failed to start within 30 seconds"
  cat /tmp/sit-server.log
  exit 1
}

# Function to stop server
stop_server() {
  if [ "$NO_SERVER" = true ] || [ -z "$SERVER_PID" ]; then
    return 0
  fi

  print_step "Stopping SIT server..."
  kill $SERVER_PID 2>/dev/null || true
  wait $SERVER_PID 2>/dev/null || true
  print_success "SIT server stopped"
}

# Function to seed database
seed_database() {
  if [ "$SKIP_SEED" = true ]; then
    print_warning "Skipping database seeding"
    return 0
  fi

  print_step "Step 3: Seeding database..."
  NODE_ENV=sit yarn db:seed
  print_success "Database seeding completed"
}

# Function to run tests
run_tests() {
  print_step "Step 4: Running E2E tests..."
  NODE_ENV=sit yarn test:e2e
  print_success "E2E tests completed"
}

# Function to cleanup database
cleanup_database() {
  if [ "$SKIP_CLEANUP" = true ]; then
    print_warning "Skipping database cleanup"
    return 0
  fi

  print_step "Step 5: Cleaning up database..."
  NODE_ENV=sit yarn db:cleanup:force
  print_success "Database cleanup completed"
}

# Cleanup function for script exit
cleanup_on_exit() {
  local exit_code=$?
  if [ $exit_code -ne 0 ]; then
    print_error "E2E Test Workflow failed with exit code $exit_code"
  fi
  stop_server
  exit $exit_code
}

# Set trap for cleanup
trap cleanup_on_exit EXIT

# Main workflow
main() {
  print_step "🚀 Starting E2E Test Workflow..."
  echo ""

  # Verify environment
  if [ "$NODE_ENV" != "sit" ] && [ "$NODE_ENV" != "test" ]; then
    export NODE_ENV=sit
    print_warning "Setting NODE_ENV=sit for safety"
  fi

  # Execute workflow steps
  start_server
  seed_database
  run_tests
  cleanup_database

  echo ""
  print_success "🎉 E2E Test Workflow completed successfully!"
}

# Run main function
main

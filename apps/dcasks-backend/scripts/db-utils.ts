import { MongoClient, Db } from 'mongodb';
import * as dotenv from 'dotenv';

export class DatabaseUtils {
  private client: MongoClient;
  private db: Db;

  async initialize(): Promise<void> {
    // Load environment variables
    dotenv.config();

    // Safety check: Only allow SIT environment
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv !== 'sit' && nodeEnv !== 'test') {
      throw new Error(
        `❌ Database operations only allowed in SIT/TEST environment. Current: ${nodeEnv}`,
      );
    }

    // Get database connection
    const mongoUri = process.env.DATABASE_URI;
    if (!mongoUri) {
      throw new Error('❌ DATABASE_URI not found in environment variables');
    }

    console.log(`🔗 Connecting to database in ${nodeEnv} environment...`);
    this.client = new MongoClient(mongoUri);
    await this.client.connect();

    // Extract database name from URI or use default
    const dbName = this.extractDbName(mongoUri) || 'dcasks-sit';
    this.db = this.client.db(dbName);

    console.log(`✅ Connected to database: ${dbName}`);
  }

  private extractDbName(uri: string): string | null {
    try {
      const url = new URL(
        uri
          .replace('mongodb://', 'http://')
          .replace('mongodb+srv://', 'https://'),
      );
      return url.pathname.substring(1).split('?')[0] || null;
    } catch {
      return null;
    }
  }

  async cleanup(): Promise<void> {
    if (this.client) {
      await this.client.close();
      console.log('🔌 Database connection closed');
    }
  }

  getDb(): Db {
    if (!this.db) {
      throw new Error('❌ Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  async dropAllCollections(): Promise<void> {
    const collections = await this.db.listCollections().toArray();

    if (collections.length === 0) {
      console.log('📭 No collections found to drop');
      return;
    }

    console.log(`🗑️  Dropping ${collections.length} collections...`);

    for (const collection of collections) {
      await this.db.collection(collection.name).drop();
      console.log(`   ✅ Dropped: ${collection.name}`);
    }

    console.log('🧹 All collections dropped successfully');
  }

  async seedCollection(collectionName: string, data: any[]): Promise<void> {
    if (data.length === 0) {
      console.log(`⏭️  Skipping ${collectionName} - no data provided`);
      return;
    }

    const collection = this.db.collection(collectionName);

    // Clear existing data
    await collection.deleteMany({});

    // Insert new data
    const result = await collection.insertMany(data);
    console.log(
      `✅ Seeded ${collectionName}: ${result.insertedCount} documents`,
    );
  }

  // New method: Use Mongoose schemas to automatically create all indexes
  async ensureMongooseIndexes(): Promise<void> {
    console.log('🔍 Creating indexes from Mongoose schemas...');

    try {
      // Import all schema definitions
      const {
        QueueSessionSchema,
        OrderSchema,
        NFTInventorySchema,
        PaymentRecordSchema,
        SaleConfigSchema,
        ActivePoolTrackingSchema,
        AdminActionSchema,
      } = await import('../src/nft-sale/models');

      // Schema to collection name mapping
      const schemaCollectionMap = [
        {
          schema: QueueSessionSchema,
          collection: 'queue_sessions',
          name: 'QueueSession',
        },
        { schema: OrderSchema, collection: 'orders', name: 'Order' },
        {
          schema: NFTInventorySchema,
          collection: 'nft_inventory',
          name: 'NFTInventory',
        },
        {
          schema: PaymentRecordSchema,
          collection: 'payment_records',
          name: 'PaymentRecord',
        },
        {
          schema: SaleConfigSchema,
          collection: 'sale_config',
          name: 'SaleConfig',
        },
        {
          schema: ActivePoolTrackingSchema,
          collection: 'active_pool_tracking',
          name: 'ActivePoolTracking',
        },
        {
          schema: AdminActionSchema,
          collection: 'admin_actions',
          name: 'AdminAction',
        },
      ];

      let totalIndexesCreated = 0;

      for (const {
        schema,
        collection: collectionName,
        name,
      } of schemaCollectionMap) {
        try {
          console.log(`   📋 Processing indexes for ${name}...`);

          // Get the collection
          const collection = this.db.collection(collectionName);

          // Extract index definitions from schema
          const indexes = schema.indexes();
          let createdCount = 0;

          for (const indexDef of indexes) {
            try {
              const [fields, options = {}] = indexDef as [any, any];
              await collection.createIndex(fields, options);
              createdCount++;
            } catch (error: any) {
              if (
                !error.message.includes('already exists') &&
                !error.message.includes('same name')
              ) {
                console.log(
                  `     ⚠️  Index creation warning: ${error.message}`,
                );
              }
              // Skip existing indexes
            }
          }

          // Get final index count
          const allIndexes = await collection.listIndexes().toArray();
          console.log(
            `   ✅ ${name}: ${createdCount} new indexes created, ${allIndexes.length} total`,
          );
          totalIndexesCreated += createdCount;
        } catch (error) {
          console.log(`   ❌ ${name}: ${error.message}`);
        }
      }

      console.log(`🎯 Total new indexes created: ${totalIndexesCreated}`);
    } catch (error) {
      console.error('❌ Failed to ensure Mongoose indexes:', error.message);
    }
  }

  async verifySeededData(): Promise<void> {
    console.log('\n📊 Verifying seeded data...');

    const collections = [
      'sale_config',
      'active_pool_tracking',
      'nft_inventory',
      'queue_sessions',
      'orders',
      'payment_records',
      'admin_actions',
    ];

    for (const collectionName of collections) {
      const count = await this.db.collection(collectionName).countDocuments();
      const indexes = await this.db
        .collection(collectionName)
        .listIndexes()
        .toArray();
      console.log(
        `   ${collectionName}: ${count} documents, ${indexes.length} indexes`,
      );
    }
  }
}

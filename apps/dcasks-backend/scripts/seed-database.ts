#!/usr/bin/env ts-node

import { DatabaseUtils } from './db-utils';
import { TestDataFactory } from './test-data-factory';

async function seedDatabase() {
  const dbUtils = new DatabaseUtils();

  try {
    console.log('🌱 Starting database seeding for SIT environment...\n');

    // Initialize database connection
    await dbUtils.initialize();

    // Generate test data
    console.log('📦 Generating test data...');
    const testData = TestDataFactory.generateCompleteTestData();

    // Seed each collection
    console.log('\n🚀 Seeding collections...');

    // 1. Sale Config (Critical - must be first)
    await dbUtils.seedCollection('sale_config', testData.sale_config);

    // 2. Active Pool Tracking (Critical - required for queue)
    await dbUtils.seedCollection(
      'active_pool_tracking',
      testData.active_pool_tracking,
    );

    // 3. NFT Inventory (Required for NFT selection)
    await dbUtils.seedCollection('nft_inventory', testData.nft_inventory);

    // 4. Sample Queue Sessions (Optional - for testing existing queue state)
    await dbUtils.seedCollection('queue_sessions', testData.queue_sessions);

    // 5. Sample Orders (Optional - for testing order functionality)
    await dbUtils.seedCollection('orders', testData.orders);

    // 6. Sample Payment Records (Optional - for testing payment functionality)
    await dbUtils.seedCollection('payment_records', testData.payment_records);

    // 7. Sample Admin Actions (Optional - for testing admin functionality)
    await dbUtils.seedCollection('admin_actions', testData.admin_actions);

    // Create all indexes from Mongoose schemas automatically
    await dbUtils.ensureMongooseIndexes();

    // Verify seeded data
    await dbUtils.verifySeededData();

    console.log('\n✅ Database seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • Sale configuration: Active and ready');
    console.log('   • Active pool: Ready for queue management');
    console.log(
      `   • NFT inventory: ${testData.nft_inventory.length} NFTs available`,
    );
    console.log(
      `   • Sample queue sessions: ${testData.queue_sessions.length} users`,
    );
    console.log(`   • Sample orders: ${testData.orders.length} orders`);
    console.log(
      `   • Sample payments: ${testData.payment_records.length} payment records`,
    );
    console.log(
      `   • Sample admin actions: ${testData.admin_actions.length} actions`,
    );

    console.log('\n🎯 Ready for E2E testing!');
    console.log('   Run: yarn test:e2e:real');
  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
    process.exit(1);
  } finally {
    await dbUtils.cleanup();
  }
}

// Handle command line execution
if (require.main === module) {
  seedDatabase().catch((error) => {
    console.error('❌ Seeding script failed:', error);
    process.exit(1);
  });
}

export { seedDatabase };

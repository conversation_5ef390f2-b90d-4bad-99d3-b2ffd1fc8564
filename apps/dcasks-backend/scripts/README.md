# Database Scripts for E2E Testing

## Overview

This directory contains comprehensive database management scripts for the SIT (System Integration Testing) environment, providing automated seeding, cleanup, and complete E2E test workflows.

## Scripts Overview

### Core Database Utilities

#### `db-utils.ts`
- **Purpose**: Core database connection and operation utilities
- **Features**:
  - Environment safety checks (SIT/TEST only)
  - MongoDB connection management
  - Collection operations (drop, seed, verify)
  - Automatic database name extraction from URI

#### `test-data-factory.ts`
- **Purpose**: Generate comprehensive test data for all collections
- **Features**:
  - Consistent test data generation
  - Realistic NFT inventory with varying rarities
  - Sample queue sessions, orders, and payments
  - Configurable data volumes

### Database Operations

#### `seed-database.ts`
- **Purpose**: Seed database with complete test dataset
- **Usage**: `yarn db:seed`
- **Features**:
  - Seeds all 7 required collections
  - Verifies seeded data integrity
  - Provides detailed seeding summary
  - Environment safety checks

#### `cleanup-database.ts`
- **Purpose**: Clean database by dropping all collections
- **Usage**: `yarn db:cleanup` or `yarn db:cleanup:force`
- **Features**:
  - Interactive confirmation (unless forced)
  - Complete collection removal
  - Environment safety checks
  - Verification of cleanup completion

### Test Workflow Automation

#### `run-e2e-tests.ts`
- **Purpose**: Complete automated E2E test workflow
- **Usage**: `yarn test:e2e:full`
- **Features**:
  - Automatic server management
  - Database seeding and cleanup
  - Test execution with proper sequencing
  - Error handling and cleanup

## Data Structure

### Seeded Collections

1. **sale_config** (1 document)
   - Active sale configuration
   - Queue and pool size limits
   - Timeout configurations

2. **active_pool_tracking** (1 document)
   - Main pool tracking
   - Ready for queue management

3. **nft_inventory** (50 documents)
   - Test NFTs with varying rarities
   - Metadata and pricing information
   - Available for selection

4. **queue_sessions** (5 documents)
   - Sample users in queue
   - Different join times and positions

5. **orders** (3 documents)
   - Sample orders in various states
   - Different payment methods

6. **payment_records** (2 documents)
   - Sample payment records
   - Stripe and NOWPayments examples

7. **admin_actions** (3 documents)
   - Sample administrative actions
   - Different action types

## Safety Features

### Environment Protection
- **Strict Environment Checking**: Only allows SIT/TEST environments
- **URI Validation**: Verifies database connection strings
- **Confirmation Prompts**: Interactive confirmation for destructive operations

### Error Handling
- **Connection Management**: Proper connection cleanup
- **Operation Verification**: Confirms successful operations
- **Graceful Failures**: Detailed error messages and proper exit codes

## Usage Examples

### Quick Start
```bash
# Complete workflow (recommended)
yarn test:e2e:full
```

### Manual Operations
```bash
# Seed database
yarn db:seed

# Run tests
yarn test:e2e:real

# Cleanup database
yarn db:cleanup
```

### Advanced Usage
```bash
# Skip cleanup for debugging
yarn test:e2e:full:no-cleanup

# Force cleanup without confirmation
yarn db:cleanup:force

# Custom test pattern
yarn test:e2e:full --pattern="*queue*.e2e.spec.ts"
```

## Configuration

### Environment Variables
```bash
NODE_ENV=sit                                    # Required: Environment identifier
DATABASE_URI=mongodb://localhost:27017/dcasks-sit  # Required: Database connection
JWT_SECRET=your-jwt-secret                      # Required: JWT signing secret
TEST_SERVER_URL=http://localhost:9000          # Optional: Override server URL
```

### Package.json Scripts
```json
{
  "scripts": {
    "start:sit": "NODE_ENV=sit nest start --watch",
    "db:seed": "ts-node scripts/seed-database.ts",
    "db:cleanup": "ts-node scripts/cleanup-database.ts",
    "db:cleanup:force": "ts-node scripts/cleanup-database.ts --force",
    "test:e2e:full": "ts-node scripts/run-e2e-tests.ts",
    "test:e2e:full:no-cleanup": "ts-node scripts/run-e2e-tests.ts --skip-cleanup",
    "test:e2e:full:no-seed": "ts-node scripts/run-e2e-tests.ts --skip-seed"
  }
}
```

## Troubleshooting

### Common Issues

1. **Environment Error**
   ```
   ❌ Database operations only allowed in SIT/TEST environment
   ```
   **Solution**: Set `NODE_ENV=sit` or `NODE_ENV=test`

2. **Connection Failed**
   ```
   ❌ DATABASE_URI not found in environment variables
   ```
   **Solution**: Set proper `DATABASE_URI` in environment

3. **Server Not Ready**
   ```
   ❌ Server failed to start within 30 seconds
   ```
   **Solution**: Check server logs, ensure port 9000 is available

### Debug Mode
```bash
# Enable verbose logging
DEBUG=true yarn test:e2e:full
```

## Development

### Adding New Test Data
1. Update `test-data-factory.ts` with new data generators
2. Add collection seeding in `seed-database.ts`
3. Update verification in `db-utils.ts`

### Extending Workflows
1. Modify `run-e2e-tests.ts` for custom workflows
2. Add new command line options
3. Update package.json scripts

## Best Practices

1. **Always use SIT environment** for testing
2. **Run complete workflow** for reliable results
3. **Clean up after testing** to maintain consistency
4. **Verify seeded data** before running tests
5. **Use force cleanup** only in automated environments

# Pool Processor E2E Tests

## Overview

This directory contains End-to-End tests for the Pool Processor background job functionality, organized by individual User Stories as specified in the E2E implementation plan.

## Test Structure

### Implemented Tests

#### ✅ US-Q001: Join Sale Queue
- **File**: `us-q001-join-sale-queue.e2e.spec.ts`
- **Test Cases**: 8 comprehensive test cases
- **Coverage**: Queue joining, position assignment, duplicate prevention, capacity testing, timeout configuration, validation, and heartbeat management

#### 🚧 Pending Implementation
- US-Q002: Monitor Queue Position
- US-Q003: Enter Active Pool
- US-Q004: Handle Waiting User Heartbeat Timeout
- US-Q005: Experience Queue Position Recalculation
- US-Q006: Handle Active Pool Heartbeat Timeout
- US-Q007: Experience Queue Advancement After Timeout Cleanup

## Helper Functions

### Core Infrastructure
- **`helpers/heartbeat-management.ts`**: Heartbeat timestamp utilities and user creation functions
- **`helpers/queue-position-helpers.ts`**: Queue position verification and integrity checking
- **`helpers/pool-processor-test-helpers.ts`**: Pool processor execution and validation utilities
- **`helpers/test-data-generators.ts`**: Test data generation and JWT token creation
- **`helpers/index.ts`**: Centralized exports for all helper functions

## Running Tests

### Individual User Story Tests

```bash
# Run US-Q001 tests specifically
yarn test:e2e:pool-processor:us-q001

# Run all Pool Processor tests
yarn test:e2e:pool-processor

# Run with watch mode
yarn test:e2e:pool-processor:watch
```

### Prerequisites

1. **SIT Environment Setup**
   - Ensure SIT server is running on port 9000
   - Database should be seeded with basic test data
   - Environment variables properly configured

2. **Database State**
   - Tests use real MongoDB connections
   - Each test cleans up after itself
   - Database seeding/cleanup utilities are used

### Test Environment

- **Framework**: Vitest with 30-second timeout
- **Environment**: SIT (System Integration Testing)
- **Database**: Real MongoDB with seeding/cleanup
- **Server**: Real dcasks-backend server on port 9000

## Test Data Management

### Seeding
- Tests use `seedPoolProcessorTestData()` for scenario-specific data
- Basic queue data, large queue data, and empty queue scenarios supported
- Automatic cleanup between tests

### Cleanup
- Database utilities handle cleanup after each test
- No cross-test contamination
- Proper session and queue state reset

## Success Criteria

### US-Q001 Implementation Status
- ✅ All 8 test cases implemented
- ✅ Proper timeout constant validation
- ✅ Queue integrity checking
- ✅ Database seeding and cleanup
- ✅ JWT authentication testing
- ✅ Error scenario validation
- ✅ Heartbeat timestamp verification

### Quality Metrics
- Tests pass consistently in SIT environment
- Proper error handling and logging
- Database state isolation between tests
- Comprehensive assertion coverage

## Next Steps

1. **Await Approval**: US-Q001 implementation complete, waiting for approval
2. **US-Q002 Implementation**: Monitor Queue Position tests
3. **US-Q003 Implementation**: Enter Active Pool tests
4. **Timeout Testing**: US-Q004, US-Q006 heartbeat timeout scenarios
5. **Advanced Features**: US-Q005, US-Q007 queue management tests

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure SIT environment is properly configured
2. **Server Availability**: Verify dcasks-backend server is running on port 9000
3. **JWT Secrets**: Check JWT_SECRET environment variable matches server configuration
4. **Test Isolation**: Database cleanup issues may cause test failures

### Debug Mode

```bash
# Run with verbose output
DEBUG=true yarn test:e2e:pool-processor:us-q001

# Run single test with detailed logging
yarn test:e2e:pool-processor:us-q001 --reporter=verbose
```

import { describe, it, expect, beforeAll, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import {
  generateWalletAddress,
  createJwtToken,
  seedPoolProcessorTestData,
  verifyUserStatus,
  checkQueueIntegrity,
  verifyTimeoutConstants,
  WAITING_USER_HEARTBEAT_TIMEOUT_MS,
  DatabaseUtils,
} from './helpers';
import { QueueSessionStatus } from '../../../src/nft-sale/models';

describe('US-Q001: Join Sale Queue', () => {
  const baseURL = process.env.TEST_SERVER_URL || 'http://localhost:9000';
  let dbUtils: DatabaseUtils;

  beforeAll(async () => {
    // Verify we're in test environment
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv !== 'sit' && nodeEnv !== 'test') {
      console.warn(`⚠️  Warning: Running tests in ${nodeEnv} environment`);
    }

    // Verify timeout constants match Pool Processor service
    expect(verifyTimeoutConstants()).toBe(true);

    console.log(`🎯 Running US-Q001 tests against: ${baseURL}`);
  });

  beforeEach(async () => {
    // Initialize database utilities
    dbUtils = new DatabaseUtils();
    await dbUtils.initialize();

    // Seed basic test data for queue operations
    await seedPoolProcessorTestData('basic-queue');
  });

  afterEach(async () => {
    // Clean up database after each test
    if (dbUtils) {
      await dbUtils.cleanup();
    }
  });

  describe('Test Case 1: Queue session creation with proper timeout', () => {
    it('Given user with valid wallet connection, when user joins queue, then queue session is created with proper timeout', async () => {
      // Arrange
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Act
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Assert
      expect(response.body).toMatchObject({
        position: expect.any(Number),
        sessionId: expect.any(String),
        queueSessionId: expect.any(String),
        status: 'WAITING',
        maxWaitingQueueSize: expect.any(Number),
      });

      // Verify session was created in database with proper heartbeat timeout configuration
      const userInWaitingStatus = await verifyUserStatus(
        walletAddress,
        QueueSessionStatus.WAITING,
      );
      expect(userInWaitingStatus).toBe(true);

      // Verify heartbeat timestamp is set for timeout monitoring
      const db = dbUtils.getDb();
      const session = await db
        .collection('queue_sessions')
        .findOne({ walletAddress });
      expect(session).toBeTruthy();
      expect(session.queueHeartbeat).toBeTruthy();
      expect(session.queueHeartbeat).toBeInstanceOf(Date);

      console.log(
        '✅ Queue session created with proper timeout configuration:',
        {
          position: response.body.position,
          status: response.body.status,
          heartbeatSet: !!session.queueHeartbeat,
        },
      );
    });
  });

  describe('Test Case 2: Sequential position assignment', () => {
    it('Given user joins queue successfully, when queue position is assigned, then user receives sequential position number', async () => {
      // Arrange
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Get current queue size to predict next position
      const db = dbUtils.getDb();
      const currentQueueSize = await db
        .collection('queue_sessions')
        .countDocuments({
          status: { $in: ['WAITING', 'ACTIVE', 'PAYMENT'] },
        });
      const expectedPosition = currentQueueSize + 1;

      // Act
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Assert
      expect(response.body.position).toBe(expectedPosition);

      // Verify FIFO order is maintained
      const queueIntegrity = await checkQueueIntegrity();
      expect(queueIntegrity.isValid).toBe(true);

      console.log('✅ Sequential position assignment verified:', {
        expectedPosition,
        actualPosition: response.body.position,
        queueIntegrity: queueIntegrity.isValid,
      });
    });
  });

  describe('Test Case 3: Duplicate join prevention', () => {
    it('Given user attempts to join queue with same wallet address twice, when second join attempt is made, then 409 Conflict is returned', async () => {
      // Arrange
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Act - First join
      const firstResponse = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Act - Second join attempt should fail with 409 Conflict
      const secondResponse = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(409);

      // Assert
      expect(secondResponse.body.message).toContain('existing queue session');
      expect(secondResponse.body.statusCode).toBe(409);

      // Verify only one session exists
      const db = dbUtils.getDb();
      const sessions = await db
        .collection('queue_sessions')
        .find({ walletAddress })
        .toArray();
      expect(sessions).toHaveLength(1); // Only one session should exist

      console.log('✅ Duplicate join prevention working with 409 Conflict:', {
        firstPosition: firstResponse.body.position,
        conflictMessage: secondResponse.body.message,
        sessionCount: sessions.length,
      });
    });
  });

  describe('Test Case 4: Unlimited queue capacity', () => {
    it('Given queue has 1000 users already, when new user joins queue, then user is added successfully', async () => {
      // Arrange
      await seedPoolProcessorTestData('large-queue'); // Seeds 1000 users

      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Act
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Assert
      expect(response.body.position).toBe(1001); // Should be position 1001
      expect(response.body.status).toBe('WAITING');

      // Verify no capacity restrictions
      const userAdded = await verifyUserStatus(
        walletAddress,
        QueueSessionStatus.WAITING,
      );
      expect(userAdded).toBe(true);

      console.log('✅ Unlimited queue capacity verified:', {
        position: response.body.position,
        status: response.body.status,
      });
    });
  });

  describe('Test Case 5: Heartbeat timeout configuration', () => {
    it('Given user joins queue, when session is created, then heartbeat timeout is set to WAITING_USER_HEARTBEAT_TIMEOUT_MS', async () => {
      // Arrange
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Act
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Assert
      expect(response.body.status).toBe('WAITING');

      // Verify session is configured with 60-second heartbeat timeout for waiting status
      const db = dbUtils.getDb();
      const session = await db
        .collection('queue_sessions')
        .findOne({ walletAddress });
      expect(session).toBeTruthy();
      expect(session.queueHeartbeat).toBeTruthy();

      // Verify the heartbeat timestamp is recent (within last few seconds)
      const heartbeatAge = Date.now() - session.queueHeartbeat.getTime();
      expect(heartbeatAge).toBeLessThan(5000); // Should be very recent

      // Verify timeout constant is correctly set
      expect(WAITING_USER_HEARTBEAT_TIMEOUT_MS).toBe(60000); // 60 seconds

      console.log('✅ Heartbeat timeout configuration verified:', {
        status: response.body.status,
        heartbeatAge: heartbeatAge,
        timeoutConstant: WAITING_USER_HEARTBEAT_TIMEOUT_MS,
      });
    });
  });

  describe('Test Case 6: Invalid wallet signature rejection', () => {
    it('Given user with invalid wallet signature, when user attempts to join queue, then join is rejected', async () => {
      // Arrange - Use invalid JWT token
      const invalidToken = 'invalid-jwt-token';

      // Act & Assert
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${invalidToken}`)
        .send({})
        .expect(403); // Should be rejected with 403 Forbidden

      console.log('✅ Invalid wallet signature rejection verified');
    });

    it('Given user with no authorization header, when user attempts to join queue, then join is rejected', async () => {
      // Act & Assert
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .send({})
        .expect(403); // Should be rejected with 403 Forbidden

      console.log('✅ Missing authorization rejection verified');
    });
  });

  describe('Test Case 7: Sale pause prevention', () => {
    it('Given sale is paused, when user attempts to join queue, then join is prevented', async () => {
      // Arrange - Pause the sale
      const db = dbUtils.getDb();
      await db.collection('sale_config').updateOne(
        {},
        {
          $set: {
            isPaused: true,
            pauseReason: 'Testing pause functionality',
          },
        },
      );

      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // Act & Assert
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(400); // Should be rejected with 400 Bad Request

      // Verify error message indicates sale is paused
      expect(response.body.message).toContain('paused');

      // Verify no queue session was created
      const session = await db
        .collection('queue_sessions')
        .findOne({ walletAddress });
      expect(session).toBeNull();

      console.log('✅ Sale pause prevention verified:', {
        errorMessage: response.body.message,
      });

      // Clean up - Unpause the sale for other tests
      await db
        .collection('sale_config')
        .updateOne({}, { $set: { isPaused: false, pauseReason: null } });
    });
  });

  describe('Test Case 8: Initial heartbeat timestamp recording', () => {
    it('Given user joins queue successfully, when initial heartbeat is set, then timestamp is recorded for timeout monitoring', async () => {
      // Arrange
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);
      const beforeJoin = new Date();

      // Act
      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      const afterJoin = new Date();

      // Assert
      expect(response.body.status).toBe('WAITING');

      // Verify initial heartbeat timestamp is properly set for Pool Processor monitoring
      const db = dbUtils.getDb();
      const session = await db
        .collection('queue_sessions')
        .findOne({ walletAddress });
      expect(session).toBeTruthy();
      expect(session.queueHeartbeat).toBeTruthy();
      expect(session.queueHeartbeat).toBeInstanceOf(Date);

      // Verify heartbeat timestamp is within the join timeframe
      expect(session.queueHeartbeat.getTime()).toBeGreaterThanOrEqual(
        beforeJoin.getTime(),
      );
      expect(session.queueHeartbeat.getTime()).toBeLessThanOrEqual(
        afterJoin.getTime(),
      );

      // Verify session is eligible for WAITING_USER_HEARTBEAT_TIMEOUT_MS monitoring
      const heartbeatAge = Date.now() - session.queueHeartbeat.getTime();
      expect(heartbeatAge).toBeLessThan(WAITING_USER_HEARTBEAT_TIMEOUT_MS);

      console.log('✅ Initial heartbeat timestamp recording verified:', {
        heartbeatSet: !!session.queueHeartbeat,
        heartbeatAge: heartbeatAge,
        eligibleForMonitoring: heartbeatAge < WAITING_USER_HEARTBEAT_TIMEOUT_MS,
      });
    });
  });
});

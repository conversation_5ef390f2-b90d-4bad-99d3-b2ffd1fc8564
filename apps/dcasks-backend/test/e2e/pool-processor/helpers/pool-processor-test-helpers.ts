import request from 'supertest';
import { SchemaTypes } from 'mongoose';
import { DatabaseUtils } from '../../../../scripts/db-utils';
import { QueueSessionStatus } from '../../../../src/nft-sale/models';

const baseURL = process.env.TEST_SERVER_URL || 'http://localhost:9000';

export interface PoolStatistics {
  currentPoolSize: number;
  maxPoolSize: number;
  totalProcessed: number;
  queueSize: number;
}

export interface PoolProcessorExpectation {
  walletAddress: string;
  expectedStatus: QueueSessionStatus;
}

/**
 * Trigger Pool Processor execution manually for testing
 * Note: This requires a test endpoint to be implemented in the backend
 */
export async function triggerPoolProcessor(): Promise<void> {
  try {
    // For now, we'll simulate pool processor execution by waiting
    // In a real implementation, this would call a test endpoint
    // await request(baseURL)
    //   .post('/api/v1/test/trigger-pool-processor')
    //   .set('Authorization', `Bearer ${getAdminTestToken()}`)
    //   .expect(200);

    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 1000));
  } catch (error) {
    console.warn('⚠️ Pool processor trigger not implemented, using simulation');
  }
}

/**
 * Get pool statistics from the API
 */
export async function getPoolStatistics(): Promise<PoolStatistics> {
  try {
    const response = await request(baseURL)
      .get('/api/v1/collaborations/pool/statistics')
      .expect(200);

    return response.body;
  } catch (error) {
    // Fallback to database query if API endpoint doesn't exist
    return await getPoolStatisticsFromDatabase();
  }
}

/**
 * Get pool statistics directly from database
 */
export async function getPoolStatisticsFromDatabase(): Promise<PoolStatistics> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();

  try {
    const db = dbUtils.getDb();

    const [activeCount, waitingCount, poolTracking] = await Promise.all([
      db.collection('queue_sessions').countDocuments({
        status: { $in: ['ACTIVE', 'PAYMENT'] },
      }),
      db.collection('queue_sessions').countDocuments({ status: 'WAITING' }),
      db.collection('active_pool_tracking').findOne({}),
    ]);

    return {
      currentPoolSize: activeCount,
      maxPoolSize: poolTracking?.maxPoolSize || 10,
      totalProcessed: poolTracking?.totalProcessed || 0,
      queueSize: waitingCount,
    };
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Verify pool capacity constraints
 */
export async function verifyPoolCapacity(
  expectedCapacity: number,
): Promise<boolean> {
  const stats = await getPoolStatistics();
  return stats.currentPoolSize <= expectedCapacity;
}

/**
 * Verify user status in database
 */
export async function verifyUserStatus(
  walletAddress: string,
  expectedStatus: QueueSessionStatus,
): Promise<boolean> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();

  try {
    const db = dbUtils.getDb();
    const session = await db
      .collection('queue_sessions')
      .findOne({ walletAddress });

    return session?.status === expectedStatus;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Verify NFT lock status
 */
export async function verifyNFTLockStatus(
  nftId: string,
  expectedStatus: string,
): Promise<boolean> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();

  try {
    const db = dbUtils.getDb();

    const nft = await db
      .collection('nft_inventory')
      .findOne({ _id: new SchemaTypes.ObjectId(nftId) });

    return nft?.status === expectedStatus;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Verify timeout constant application
 */
export async function verifyTimeoutConstantApplication(
  timeoutType: 'WAITING' | 'ACTIVE' | 'CLEANUP',
  expectedThreshold: number,
): Promise<boolean> {
  const timeoutConstants = {
    WAITING: 60000, // 1 minute
    ACTIVE: 300000, // 5 minutes
    CLEANUP: 1200000, // 20 minutes
  };

  return timeoutConstants[timeoutType] === expectedThreshold;
}

/**
 * Validate boundary conditions for timeout testing
 */
export async function validateBoundaryConditions(
  timeoutType: string,
  boundaryValues: number[],
): Promise<Array<{ value: number; result: boolean }>> {
  const results = [];

  for (const value of boundaryValues) {
    // This would test actual timeout behavior
    // For now, we'll simulate the validation
    const result = await verifyTimeoutConstantApplication(
      timeoutType as 'WAITING' | 'ACTIVE' | 'CLEANUP',
      value,
    );

    results.push({ value, result });
  }

  return results;
}

/**
 * Get admin test token for privileged operations
 */
export function getAdminTestToken(): string {
  // This should be implemented based on your admin token generation logic
  return 'admin-test-token';
}

/**
 * Verify Pool Processor results against expectations
 */
export async function verifyPoolProcessorResults(
  expectedChanges: PoolProcessorExpectation[],
): Promise<boolean> {
  for (const expectation of expectedChanges) {
    const isCorrect = await verifyUserStatus(
      expectation.walletAddress,
      expectation.expectedStatus,
    );
    if (!isCorrect) {
      console.error(
        `❌ Expected ${expectation.walletAddress} to have status ${expectation.expectedStatus}`,
      );
      return false;
    }
  }

  return true;
}

/**
 * Wait for Pool Processor natural execution cycle
 */
export async function waitForPoolProcessorCycle(): Promise<void> {
  // Pool processor runs every 30 seconds, wait a bit longer to ensure execution
  await new Promise((resolve) => setTimeout(resolve, 35000));
}

/**
 * Seed specific test data for Pool Processor scenarios
 */
export async function seedPoolProcessorTestData(
  scenario: string,
): Promise<void> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();

  try {
    const db = dbUtils.getDb();

    // Clear existing queue sessions
    await db.collection('queue_sessions').deleteMany({});

    // Seed scenario-specific data based on test requirements
    switch (scenario) {
      case 'empty-queue':
        // No additional seeding needed
        break;

      case 'basic-queue':
        await seedBasicQueueData(db);
        break;

      case 'large-queue':
        await seedLargeQueueData(db);
        break;

      case 'full-waiting-queue':
        await seedFullWaitingQueueData(db);
        break;

      default:
        console.warn(`Unknown scenario: ${scenario}`);
    }
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Seed basic queue data for testing
 */
async function seedBasicQueueData(db: any): Promise<void> {
  const sessions = [
    {
      walletAddress: '******************************************',
      status: 'WAITING',
      position: 1,
      queueJoinedAt: new Date(Date.now() - 60000),
      queueHeartbeat: new Date(Date.now() - 30000),
      sessionToken: 'test-token-1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      walletAddress: '******************************************',
      status: 'WAITING',
      position: 2,
      queueJoinedAt: new Date(Date.now() - 50000),
      queueHeartbeat: new Date(Date.now() - 25000),
      sessionToken: 'test-token-2',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  await db.collection('queue_sessions').insertMany(sessions);
}

/**
 * Seed large queue data for performance testing
 */
async function seedLargeQueueData(db: any): Promise<void> {
  const sessions = [];

  for (let i = 1; i <= 1000; i++) {
    sessions.push({
      walletAddress: `0x${i.toString().padStart(40, '0')}`,
      status: 'WAITING',
      position: i,
      queueJoinedAt: new Date(Date.now() - i * 1000),
      queueHeartbeat: new Date(Date.now() - 30000),
      sessionToken: `test-token-${i}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  await db.collection('queue_sessions').insertMany(sessions);
}

/**
 * Seed full waiting queue data (100 WAITING users at maxWaitingQueueSize limit)
 */
async function seedFullWaitingQueueData(db: any): Promise<void> {
  const sessions = [];

  // Create exactly 100 WAITING users to fill the maxWaitingQueueSize limit
  for (let i = 1; i <= 100; i++) {
    sessions.push({
      walletAddress: `0x${i.toString().padStart(40, '0')}`,
      status: 'WAITING',
      position: i,
      queueJoinedAt: new Date(Date.now() - i * 1000),
      queueHeartbeat: new Date(Date.now() - 30000),
      sessionToken: `test-token-${i}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  await db.collection('queue_sessions').insertMany(sessions);
}

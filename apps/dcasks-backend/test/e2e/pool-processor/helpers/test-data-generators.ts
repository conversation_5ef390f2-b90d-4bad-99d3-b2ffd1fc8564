import { JwtService } from '@nestjs/jwt';
import { SYSTEM_ROLES } from '../../../../src/common/enum';
import { QueueSessionStatus } from '../../../../src/nft-sale/models';

/**
 * Generate test wallet address
 */
export function generateWalletAddress(): string {
  return `0x${Math.random().toString(16).substring(2, 42)}`;
}

/**
 * Generate multiple unique wallet addresses
 */
export function generateWalletAddresses(count: number): string[] {
  const addresses = [];
  for (let i = 0; i < count; i++) {
    addresses.push(generateWalletAddress());
  }
  return addresses;
}

/**
 * Create JWT token for testing
 */
export function createJwtToken(walletAddress: string): string {
  const jwtService = new JwtService({
    secret: process.env.JWT_SECRET || 'djW1mJFQ2B',
    signOptions: { expiresIn: '1h' },
  });

  return jwtService.sign({
    publicAddress: walletAddress,
    version: '1.0.0',
    role: SYSTEM_ROLES.USER_ROLE,
    email: `${walletAddress}@test.com`,
  });
}

/**
 * Create admin JWT token for testing
 */
export function createAdminJwtToken(): string {
  const jwtService = new JwtService({
    secret: process.env.JWT_SECRET || 'djW1mJFQ2B',
    signOptions: { expiresIn: '1h' },
  });

  return jwtService.sign({
    publicAddress: '0xadmin000000000000000000000000000000000000',
    version: '1.0.0',
    role: SYSTEM_ROLES.ADMIN_ROLE,
    email: '<EMAIL>',
  });
}

/**
 * Generate test session token
 */
export function generateSessionToken(walletAddress: string): string {
  return `test-session-${walletAddress}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
}

/**
 * Generate test data for timeout scenarios
 */
export interface TimeoutScenarioData {
  walletAddress: string;
  status: QueueSessionStatus;
  position: number;
  heartbeatSecondsAgo: number;
  expectedAfterProcessing: QueueSessionStatus;
}

/**
 * Generate waiting user timeout scenarios
 */
export function generateWaitingTimeoutScenarios(): TimeoutScenarioData[] {
  return [
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 1,
      heartbeatSecondsAgo: 30, // Valid (< 60s)
      expectedAfterProcessing: QueueSessionStatus.WAITING
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 2,
      heartbeatSecondsAgo: 45, // Valid (< 60s)
      expectedAfterProcessing: QueueSessionStatus.WAITING
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 3,
      heartbeatSecondsAgo: 59, // Valid (< 60s)
      expectedAfterProcessing: QueueSessionStatus.WAITING
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 4,
      heartbeatSecondsAgo: 60, // Boundary (= 60s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 5,
      heartbeatSecondsAgo: 61, // Expired (> 60s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 6,
      heartbeatSecondsAgo: 90, // Expired (> 60s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.WAITING,
      position: 7,
      heartbeatSecondsAgo: 120, // Expired (> 60s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    }
  ];
}

/**
 * Generate active user timeout scenarios
 */
export function generateActiveTimeoutScenarios(): TimeoutScenarioData[] {
  return [
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0, // Active users don't have queue positions
      heartbeatSecondsAgo: 120, // Valid (< 300s)
      expectedAfterProcessing: QueueSessionStatus.ACTIVE
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0,
      heartbeatSecondsAgo: 240, // Valid (< 300s)
      expectedAfterProcessing: QueueSessionStatus.ACTIVE
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0,
      heartbeatSecondsAgo: 299, // Valid (< 300s)
      expectedAfterProcessing: QueueSessionStatus.ACTIVE
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0,
      heartbeatSecondsAgo: 300, // Boundary (= 300s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0,
      heartbeatSecondsAgo: 301, // Expired (> 300s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    },
    {
      walletAddress: generateWalletAddress(),
      status: QueueSessionStatus.ACTIVE,
      position: 0,
      heartbeatSecondsAgo: 360, // Expired (> 300s)
      expectedAfterProcessing: QueueSessionStatus.EXPIRED
    }
  ];
}

/**
 * Generate queue position recalculation scenarios
 */
export interface QueuePositionScenario {
  initialUsers: Array<{walletAddress: string, position: number, heartbeatSecondsAgo: number}>;
  expectedRemovals: string[];
  expectedFinalPositions: Array<{walletAddress: string, position: number}>;
}

export function generateQueuePositionScenarios(): QueuePositionScenario[] {
  return [
    {
      // Single removal scenario
      initialUsers: [
        { walletAddress: generateWalletAddress(), position: 1, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 2, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 3, heartbeatSecondsAgo: 70 }, // Expired
        { walletAddress: generateWalletAddress(), position: 4, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 5, heartbeatSecondsAgo: 30 }, // Valid
      ],
      expectedRemovals: [], // Will be filled with expired user
      expectedFinalPositions: [] // Will be calculated
    },
    {
      // Multiple non-consecutive removals
      initialUsers: [
        { walletAddress: generateWalletAddress(), position: 1, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 2, heartbeatSecondsAgo: 70 }, // Expired
        { walletAddress: generateWalletAddress(), position: 3, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 4, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 5, heartbeatSecondsAgo: 80 }, // Expired
        { walletAddress: generateWalletAddress(), position: 6, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 7, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 8, heartbeatSecondsAgo: 90 }, // Expired
        { walletAddress: generateWalletAddress(), position: 9, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 10, heartbeatSecondsAgo: 30 }, // Valid
      ],
      expectedRemovals: [], // Will be filled with expired users
      expectedFinalPositions: [] // Will be calculated
    },
    {
      // Head removals (first few positions)
      initialUsers: [
        { walletAddress: generateWalletAddress(), position: 1, heartbeatSecondsAgo: 70 }, // Expired
        { walletAddress: generateWalletAddress(), position: 2, heartbeatSecondsAgo: 80 }, // Expired
        { walletAddress: generateWalletAddress(), position: 3, heartbeatSecondsAgo: 90 }, // Expired
        { walletAddress: generateWalletAddress(), position: 4, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 5, heartbeatSecondsAgo: 30 }, // Valid
      ],
      expectedRemovals: [], // Will be filled with expired users
      expectedFinalPositions: [] // Will be calculated
    },
    {
      // Tail removals (last few positions)
      initialUsers: [
        { walletAddress: generateWalletAddress(), position: 1, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 2, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 3, heartbeatSecondsAgo: 30 }, // Valid
        { walletAddress: generateWalletAddress(), position: 4, heartbeatSecondsAgo: 70 }, // Expired
        { walletAddress: generateWalletAddress(), position: 5, heartbeatSecondsAgo: 80 }, // Expired
      ],
      expectedRemovals: [], // Will be filled with expired users
      expectedFinalPositions: [] // Will be calculated
    }
  ];
}

/**
 * Generate large dataset for performance testing
 */
export function generateLargeQueueDataset(size: number): Array<{walletAddress: string, position: number, heartbeatSecondsAgo: number}> {
  const users = [];
  
  for (let i = 1; i <= size; i++) {
    // Mix of valid and expired users for realistic testing
    const heartbeatSecondsAgo = Math.random() < 0.8 ? 
      Math.floor(Math.random() * 50) + 10 : // 80% valid users (10-60 seconds ago)
      Math.floor(Math.random() * 60) + 70;   // 20% expired users (70-130 seconds ago)
    
    users.push({
      walletAddress: generateWalletAddress(),
      position: i,
      heartbeatSecondsAgo
    });
  }
  
  return users;
}

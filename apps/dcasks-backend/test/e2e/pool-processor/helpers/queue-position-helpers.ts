import { DatabaseUtils } from '../../../../scripts/db-utils';

export interface QueuePositionMapping {
  walletAddress: string;
  position: number;
  status: string;
}

export interface QueueIntegrityResult {
  isValid: boolean;
  errors: string[];
  totalUsers: number;
  duplicatePositions: number[];
  missingPositions: number[];
}

/**
 * Verify queue positions match expected mapping
 */
export async function verifyQueuePositions(expectedPositions: Array<{walletAddress: string, position: number}>): Promise<boolean> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    
    for (const expected of expectedPositions) {
      const session = await db.collection('queue_sessions').findOne({ 
        walletAddress: expected.walletAddress 
      });
      
      if (!session || session.position !== expected.position) {
        console.error(`❌ Position mismatch for ${expected.walletAddress}: expected ${expected.position}, got ${session?.position}`);
        return false;
      }
    }
    
    return true;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Check queue integrity - no duplicate positions, sequential order
 */
export async function checkQueueIntegrity(): Promise<QueueIntegrityResult> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    const sessions = await db.collection('queue_sessions')
      .find({ status: 'WAITING' })
      .sort({ position: 1 })
      .toArray();
    
    const result: QueueIntegrityResult = {
      isValid: true,
      errors: [],
      totalUsers: sessions.length,
      duplicatePositions: [],
      missingPositions: []
    };
    
    if (sessions.length === 0) {
      return result; // Empty queue is valid
    }
    
    // Check for duplicate positions
    const positionCounts = new Map<number, number>();
    sessions.forEach(session => {
      const count = positionCounts.get(session.position) || 0;
      positionCounts.set(session.position, count + 1);
    });
    
    positionCounts.forEach((count, position) => {
      if (count > 1) {
        result.duplicatePositions.push(position);
        result.isValid = false;
        result.errors.push(`Duplicate position ${position} found ${count} times`);
      }
    });
    
    // Check for missing positions (should be sequential starting from 1)
    const positions = sessions.map(s => s.position).sort((a, b) => a - b);
    for (let i = 1; i <= positions.length; i++) {
      if (!positions.includes(i)) {
        result.missingPositions.push(i);
        result.isValid = false;
        result.errors.push(`Missing position ${i} in sequence`);
      }
    }
    
    // Check if positions start from 1
    if (positions.length > 0 && positions[0] !== 1) {
      result.isValid = false;
      result.errors.push(`Queue positions should start from 1, but starts from ${positions[0]}`);
    }
    
    return result;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Validate queue after user removals
 */
export async function validateQueueAfterRemovals(removedUsers: string[], remainingUsers: string[]): Promise<boolean> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    
    // Verify removed users are no longer in WAITING status
    for (const walletAddress of removedUsers) {
      const session = await db.collection('queue_sessions').findOne({ walletAddress });
      if (session && session.status === 'WAITING') {
        console.error(`❌ User ${walletAddress} should be removed but still in WAITING status`);
        return false;
      }
    }
    
    // Verify remaining users have sequential positions
    const remainingSessions = await db.collection('queue_sessions')
      .find({ 
        walletAddress: { $in: remainingUsers },
        status: 'WAITING'
      })
      .sort({ position: 1 })
      .toArray();
    
    for (let i = 0; i < remainingSessions.length; i++) {
      const expectedPosition = i + 1;
      if (remainingSessions[i].position !== expectedPosition) {
        console.error(`❌ Position mismatch after removal: expected ${expectedPosition}, got ${remainingSessions[i].position}`);
        return false;
      }
    }
    
    return true;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Get current queue position mapping
 */
export async function getQueuePositionMapping(): Promise<Map<string, number>> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    const sessions = await db.collection('queue_sessions')
      .find({ status: 'WAITING' })
      .sort({ position: 1 })
      .toArray();
    
    const mapping = new Map<string, number>();
    sessions.forEach(session => {
      mapping.set(session.walletAddress, session.position);
    });
    
    return mapping;
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Create queue with specific positions for testing
 */
export async function createQueueWithPositions(users: Array<{walletAddress: string, position: number, heartbeatSecondsAgo: number}>): Promise<void> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    
    // Clear existing queue sessions
    await db.collection('queue_sessions').deleteMany({});
    
    // Create sessions with specified positions
    const sessions = users.map(user => ({
      walletAddress: user.walletAddress,
      status: 'WAITING',
      position: user.position,
      queueJoinedAt: new Date(Date.now() - (user.position * 10000)), // Stagger join times
      queueHeartbeat: new Date(Date.now() - (user.heartbeatSecondsAgo * 1000)),
      sessionToken: `test-token-${user.walletAddress}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));
    
    await db.collection('queue_sessions').insertMany(sessions);
  } finally {
    await dbUtils.cleanup();
  }
}

/**
 * Remove specific users from queue (for testing)
 */
export async function removeUsersFromQueue(walletAddresses: string[]): Promise<void> {
  const dbUtils = new DatabaseUtils();
  await dbUtils.initialize();
  
  try {
    const db = dbUtils.getDb();
    
    // Mark users as EXPIRED
    await db.collection('queue_sessions').updateMany(
      { walletAddress: { $in: walletAddresses } },
      { $set: { status: 'EXPIRED' } }
    );
  } finally {
    await dbUtils.cleanup();
  }
}

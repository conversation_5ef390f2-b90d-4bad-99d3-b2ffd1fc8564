import { QueueSessionStatus } from '../../../../src/nft-sale/models';
import { generateSessionToken } from './test-data-generators';

// Timeout constants matching Pool Processor service
export const WAITING_USER_HEARTBEAT_TIMEOUT_MS = 60 * 1000; // 1 minute
export const ACTIVE_USER_HEARTBEAT_TIMEOUT_MS = 5 * 60 * 1000; // 5 minutes
export const CLEANUP_HEARTBEAT_TIMEOUT_MS = 20 * 60 * 1000; // 20 minutes

export interface QueueSessionData {
  walletAddress: string;
  status: QueueSessionStatus;
  position: number;
  queueJoinedAt: Date;
  queueHeartbeat?: Date;
  sessionToken: string;
  activeSessionExpiresAt?: Date;
  poolId?: string;
}

/**
 * Set heartbeat timestamp for waiting users relative to timeout constant
 */
export function setWaitingUserHeartbeat(secondsAgo: number): Date {
  return new Date(Date.now() - secondsAgo * 1000);
}

/**
 * Set heartbeat timestamp for active users relative to timeout constant
 */
export function setActiveUserHeartbeat(secondsAgo: number): Date {
  return new Date(Date.now() - secondsAgo * 1000);
}

/**
 * Set heartbeat timestamp for cleanup scenarios
 */
export function setCleanupHeartbeat(secondsAgo: number): Date {
  return new Date(Date.now() - secondsAgo * 1000);
}

/**
 * Create a waiting user with expired heartbeat (> 60 seconds ago)
 */
export function createExpiredWaitingUser(
  walletAddress: string,
  position: number = 1,
): QueueSessionData {
  return {
    walletAddress,
    status: QueueSessionStatus.WAITING,
    position,
    queueJoinedAt: new Date(Date.now() - 120000), // 2 minutes ago
    queueHeartbeat: new Date(Date.now() - 70000), // 70 seconds ago (expired)
    sessionToken: generateSessionToken(walletAddress),
  };
}

/**
 * Create a waiting user with valid heartbeat (< 60 seconds ago)
 */
export function createValidWaitingUser(
  walletAddress: string,
  position: number = 1,
): QueueSessionData {
  return {
    walletAddress,
    status: QueueSessionStatus.WAITING,
    position,
    queueJoinedAt: new Date(Date.now() - 30000), // 30 seconds ago
    queueHeartbeat: new Date(Date.now() - 30000), // 30 seconds ago (valid)
    sessionToken: generateSessionToken(walletAddress),
  };
}

/**
 * Create an active user with expired heartbeat (> 5 minutes ago)
 */
export function createExpiredActiveUser(
  walletAddress: string,
): QueueSessionData {
  return {
    walletAddress,
    status: QueueSessionStatus.ACTIVE,
    position: 0, // Active users don't have queue positions
    queueJoinedAt: new Date(Date.now() - 600000), // 10 minutes ago
    queueHeartbeat: new Date(Date.now() - 360000), // 6 minutes ago (expired)
    sessionToken: generateSessionToken(walletAddress),
    activeSessionExpiresAt: new Date(Date.now() + 300000), // 5 minutes from now
    poolId: 'test-pool-id',
  };
}

/**
 * Create an active user with valid heartbeat (< 5 minutes ago)
 */
export function createValidActiveUser(walletAddress: string): QueueSessionData {
  return {
    walletAddress,
    status: QueueSessionStatus.ACTIVE,
    position: 0, // Active users don't have queue positions
    queueJoinedAt: new Date(Date.now() - 300000), // 5 minutes ago
    queueHeartbeat: new Date(Date.now() - 120000), // 2 minutes ago (valid)
    sessionToken: generateSessionToken(walletAddress),
    activeSessionExpiresAt: new Date(Date.now() + 300000), // 5 minutes from now
    poolId: 'test-pool-id',
  };
}

/**
 * Create a waiting user at exact boundary condition
 */
export function createBoundaryWaitingUser(
  walletAddress: string,
  exactlyAtThreshold: boolean,
  position: number = 1,
): QueueSessionData {
  const heartbeatTime = exactlyAtThreshold
    ? Date.now() - WAITING_USER_HEARTBEAT_TIMEOUT_MS // Exactly 60 seconds
    : Date.now() - WAITING_USER_HEARTBEAT_TIMEOUT_MS - 1000; // 61 seconds (expired)

  return {
    walletAddress,
    status: QueueSessionStatus.WAITING,
    position,
    queueJoinedAt: new Date(Date.now() - 120000),
    queueHeartbeat: new Date(heartbeatTime),
    sessionToken: generateSessionToken(walletAddress),
  };
}

/**
 * Create an active user at exact boundary condition
 */
export function createBoundaryActiveUser(
  walletAddress: string,
  exactlyAtThreshold: boolean,
): QueueSessionData {
  const heartbeatTime = exactlyAtThreshold
    ? Date.now() - ACTIVE_USER_HEARTBEAT_TIMEOUT_MS // Exactly 5 minutes
    : Date.now() - ACTIVE_USER_HEARTBEAT_TIMEOUT_MS - 1000; // 5 minutes + 1 second (expired)

  return {
    walletAddress,
    status: QueueSessionStatus.ACTIVE,
    position: 0,
    queueJoinedAt: new Date(Date.now() - 600000),
    queueHeartbeat: new Date(heartbeatTime),
    sessionToken: generateSessionToken(walletAddress),
    activeSessionExpiresAt: new Date(Date.now() + 300000),
    poolId: 'test-pool-id',
  };
}

/**
 * Verify timeout constant values match Pool Processor service
 */
export function verifyTimeoutConstants(): boolean {
  return (
    WAITING_USER_HEARTBEAT_TIMEOUT_MS === 60000 &&
    ACTIVE_USER_HEARTBEAT_TIMEOUT_MS === 300000 &&
    CLEANUP_HEARTBEAT_TIMEOUT_MS === 1200000
  );
}

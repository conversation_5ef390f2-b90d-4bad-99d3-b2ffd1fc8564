import { describe, it, expect, beforeAll, beforeEach } from 'vitest';
import { JwtService } from '@nestjs/jwt';
import request from 'supertest';
import { SYSTEM_ROLES } from 'src/common/enum';

describe('US-Q001: Join <PERSON> (Real Server)', () => {
  let jwtService: JwtService;
  const baseURL = process.env.TEST_SERVER_URL || 'http://localhost:9000'; // SIT server URL

  beforeAll(async () => {
    // Verify we're in test environment
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv !== 'sit' && nodeEnv !== 'test') {
      console.warn(`⚠️  Warning: Running tests in ${nodeEnv} environment`);
    }

    // Create a minimal JWT service for token generation
    jwtService = new JwtService({
      secret: process.env.JWT_SECRET || 'djW1mJFQ2B', // Use your actual JWT secret
      signOptions: { expiresIn: '1h' },
    });

    console.log(`🎯 Running tests against: ${baseURL}`);
  });

  beforeEach(async () => {
    // Optional: Clear test data from real database before each test
    // You can implement this if needed
  });

  // Helper function to create JWT token for testing
  function createJwtToken(walletAddress: string): string {
    return jwtService.sign({
      publicAddress: walletAddress,
      version: '1.0.0',
      role: SYSTEM_ROLES.USER_ROLE,
      email: `${walletAddress}@test.com`,
    });
  }

  // Helper function to generate test wallet address
  function generateWalletAddress(): string {
    return `0x${Math.random().toString(16).substring(2, 42)}`;
  }

  describe('Acceptance Criteria: User can connect their crypto wallet to the landing page', () => {
    it('should accept valid JWT token and join queue', async () => {
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      const response = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      expect(response.body).toMatchObject({
        position: expect.any(Number),
        estimatedWaitTime: expect.any(Number),
        sessionId: expect.any(String),
        queueSessionId: expect.any(String),
        status: 'WAITING',
        maxQueueSize: expect.any(Number),
      });

      console.log('✅ Queue join successful:', response.body);
    });

    it('should reject requests without JWT token', async () => {
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .send({})
        .expect(403); // NestJS returns 403 for missing auth
    });

    it('should reject requests with invalid JWT token', async () => {
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', 'Bearer invalid-token')
        .send({})
        .expect(403); // NestJS returns 403 for invalid auth
    });
  });

  describe('Acceptance Criteria: User receives queue position and estimated wait time', () => {
    it('should return position and estimated wait time', async () => {
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // First join the queue
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Then check status
      const statusResponse = await request(baseURL)
        .get('/api/v1/collaborations/queue/status')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(statusResponse.body).toHaveProperty('position');
      expect(statusResponse.body).toHaveProperty('status');
      expect(typeof statusResponse.body.position).toBe('number');
      expect(statusResponse.body.position).toBeGreaterThan(0);

      console.log('✅ Queue status check successful:', statusResponse.body);
    });
  });

  describe('Acceptance Criteria: User cannot join queue twice with same wallet address', () => {
    it('should return existing session when user tries to join again', async () => {
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // First join
      const firstResponse = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Second join attempt
      const secondResponse = await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Should return same position and session
      expect(secondResponse.body.position).toBe(firstResponse.body.position);
      expect(secondResponse.body.queueSessionId).toBe(
        firstResponse.body.queueSessionId,
      );

      console.log('✅ Duplicate join prevention working:', {
        first: firstResponse.body.position,
        second: secondResponse.body.position,
      });
    });
  });

  describe('Acceptance Criteria: User can leave the queue', () => {
    it('should allow user to leave the queue', async () => {
      const walletAddress = generateWalletAddress();
      const token = createJwtToken(walletAddress);

      // First join the queue
      await request(baseURL)
        .post('/api/v1/collaborations/queue')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      // Then leave the queue
      const leaveResponse = await request(baseURL)
        .post('/api/v1/collaborations/queue/leave')
        .set('Authorization', `Bearer ${token}`)
        .send({})
        .expect(200);

      expect(leaveResponse.body).toMatchObject({
        success: true,
        message: expect.any(String),
      });

      console.log('✅ Queue leave successful:', leaveResponse.body);
    });
  });
});

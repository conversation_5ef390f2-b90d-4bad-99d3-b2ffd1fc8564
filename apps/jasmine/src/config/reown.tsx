import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'
import { AppKitNetwork, arbitrum, arbitrumSepolia, defineChain } from '@reown/appkit/networks'
import { cookieStorage, createStorage } from '@wagmi/core'

// Get projectId from https://cloud.reown.com
export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID
export const networkId = process.env.NEXT_PUBLIC_NETWORK_ID

if (!projectId || !networkId) {
  throw new Error('Project ID or Network ID is not defined')
}

// Define the custom network
const dcaskNetwork = defineChain({
  id: 412346,
  caipNetworkId: 'eip155:412346',
  chainNamespace: 'eip155',
  name: 'DCasks Network',
  nativeCurrency: {
    decimals: 18,
    name: 'DEther',
    symbol: 'DETH',
  },
  rpcUrls: {
    default: {
      http: ['https://layer2-rpc.dcasks.co'],
    },
  },
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://blockscout.dcasks.co' },
  },
  contracts: {
    // Add the contracts here
  },
})

export const networkConfig = {
  412_346: dcaskNetwork, // Development & Local Network
  421_614: arbitrumSepolia, // Staging Network
  42_161: arbitrum, // Production Network
}

export const networks: [AppKitNetwork, ...AppKitNetwork[]] = [networkConfig[networkId]]

//Set up the Wagmi Adapter (Config)
export const wagmiAdapter = new WagmiAdapter({
  storage: createStorage({
    storage: cookieStorage,
  }),
  ssr: true,
  projectId,
  networks,
})

export const config = wagmiAdapter.wagmiConfig

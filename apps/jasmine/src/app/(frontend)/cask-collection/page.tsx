import NftDetailClient from '@/pages/nft-detail/NftDetail.client'
import { Suspense } from 'react'

// Configure this page to be dynamically rendered
export const fetchCache = 'force-no-store'

export default function NftDetailPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">Loading NFT details...</div>
      }
    >
      <NftDetailClient />
    </Suspense>
  )
}

import React from 'react'

import { HeaderThemeProvider } from './HeaderTheme'
import { ThemeProvider } from './Theme'

import ReduxProvider from '@/redux/ReduxProvider'
import { headers } from 'next/headers'
import ReactQueryProvider from './ReactQuery'
import ReownProvider from './ReownProvider'
import GraphqlProvider from './Urql'

export const Providers: React.FC<{
  children: React.ReactNode
}> = async ({ children }) => {
  const headersObj = await headers()
  const cookies = headersObj.get('cookie')
  return (
    <ReduxProvider>
      <ReownProvider cookies={cookies}>
        <ReactQueryProvider>
          <GraphqlProvider>
            <ThemeProvider>
              <HeaderThemeProvider>{children}</HeaderThemeProvider>
            </ThemeProvider>
          </GraphqlProvider>
        </ReactQueryProvider>
      </ReownProvider>
    </ReduxProvider>
  )
}

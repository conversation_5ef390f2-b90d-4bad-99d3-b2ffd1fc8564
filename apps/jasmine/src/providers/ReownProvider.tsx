'use client'

import { networks, projectId, wagmiAdapter } from '@/config/reown'
import { createAppKit } from '@reown/appkit/react'
import { type ReactNode } from 'react'
import { cookieToInitialState, WagmiProvider, type Config } from 'wagmi'

if (!projectId) {
  throw new Error('Project ID is not defined')
}

// Set up metadata
const metadata = {
  name: 'dcasks-jasmine',
  description: 'DCasks Whiskey Marketplace',
  url: 'https://dcasks.com', // origin must match your domain & subdomain
  icons: ['https://avatars.githubusercontent.com/u/179229932'],
}

// Create the modal
const modal = createAppKit({
  adapters: [wagmiAdapter],
  projectId,
  networks: networks,
  defaultNetwork: networks[0],
  metadata: metadata,
  features: {
    analytics: true, // Optional - defaults to your Cloud configuration,
    swaps: false,
    email: false,
    socials: false,
    allWallets: false,
    send: false,
    receive: false,
    onramp: false,
  },
  themeVariables: {
    '--w3m-accent': '#DE6A29',
    '--w3m-border-radius-master': '1px',
  },
})

function ReownProvider({ children, cookies }: { children: ReactNode; cookies: string | null }) {
  const initialState = cookieToInitialState(wagmiAdapter.wagmiConfig as Config, cookies)

  return (
    <WagmiProvider config={wagmiAdapter.wagmiConfig as Config} initialState={initialState}>
      {children}
    </WagmiProvider>
  )
}

export default ReownProvider

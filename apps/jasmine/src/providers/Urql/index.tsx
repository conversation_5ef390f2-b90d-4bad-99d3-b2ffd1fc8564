'use client'
import { useMemo } from 'react'
import { Provider, createClient, cacheExchange, fetchExchange, ssrExchange } from 'urql'

export default function GraphqlProvider({ children }: { readonly children: React.ReactNode }) {
  // Setup SSR exchange for compatibility with Next.js
  const ssr = ssrExchange({
    isClient: typeof window !== 'undefined'
  })

  // Create the client with proper configuration
  const client = useMemo(() => {
    return createClient({
      url: `${process.env.NEXT_PUBLIC_SERVER_URL}/graphql`,
      exchanges: [cacheExchange, ssr, fetchExchange],
      suspense: false, // Disable suspense to prevent SSR issues
    })
  }, [])

  return <Provider value={client}>{children}</Provider>
}

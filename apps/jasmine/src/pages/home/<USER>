'use client'

import dynamic from 'next/dynamic'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Suspense } from 'react'

// Dynamically import the components that use GraphQL with SSR disabled
const FeatureCaskCarousel = dynamic(() => import('@/components/FeatureCaskCarousel'), {
  ssr: false,
})

const MarketplaceCarousel = dynamic(() => import('@/components/MarketplaceCarousel'), {
  ssr: false,
})

const WholeCaskCarousel = dynamic(() => import('@/components/WholeCaskCarousel'), {
  ssr: false,
})

const HomeClient = () => {
  return (
    <div className="mx-auto">
      <Suspense fallback={<></>}>
        <FeatureCaskCarousel />
      </Suspense>
      <div className="container my-8 flex flex-col gap-4">
        <Tabs defaultValue="marketplace" className="w-full">
          <div className="mb-6 flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
              <TabsTrigger value="trending">Trending</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="marketplace" className="mt-0">
            <Suspense fallback={<></>}>
              <MarketplaceCarousel />
            </Suspense>
          </TabsContent>
          <TabsContent value="trending" className="mt-0">
            <Suspense fallback={<></>}>
              <MarketplaceCarousel />
            </Suspense>
          </TabsContent>
        </Tabs>
        <div>
          <p className="mb-4 text-2xl font-bold">Whole Casks</p>
          <Suspense fallback={<></>}>
            <WholeCaskCarousel />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

export default HomeClient

'use client'

import { Search } from 'lucide-react'
import dynamic from 'next/dynamic'
import { useSearchParams } from 'next/navigation'
import { Suspense, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useCaskDetail } from '@/hooks/nft-detail/useCaskDetail'
import { useNftCollection } from '@/hooks/nft-detail/useNftCollection'

// Dynamically import components with no SSR
const CollectionHeader = dynamic(() => import('@/components/NftDetail/CollectionHeader'), {
  ssr: false,
})
const NftGrid = dynamic(() => import('@/components/NftDetail/NftGrid'), {
  ssr: false,
})
const FilterSidebar = dynamic(() => import('@/components/NftDetail/FilterSidebar'), {
  ssr: false,
})
const FilterModal = dynamic(() => import('@/components/NftDetail/FilterModal'), {
  ssr: false,
})
const ActivityTable = dynamic(() => import('@/components/NftDetail/ActivityTable'), {
  ssr: false,
})
const NftFooter = dynamic(() => import('@/components/NftDetail/NftFooter'), {
  ssr: false,
})

const NftDetailClient = () => {
  const searchParams = useSearchParams()
  const projectId = searchParams?.get('projectId') || '1'
  const type = (searchParams?.get('type') as 'regular' | 'whole') || 'regular'

  // Fetch NFT data
  const {
    nfts,
    activities,
    isLoading: nftsLoading,
    stats,
  } = useNftCollection({
    projectId,
    sort: 'recent',
  })

  // Fetch cask details
  const { cask, isLoading: caskLoading } = useCaskDetail({
    projectId,
    type,
  })

  // State for filter modal
  const [filterModalOpen, setFilterModalOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<any>({})

  const handleApplyFilters = (filters: any) => {
    setActiveFilters(filters)
    console.log('Applied filters:', filters)
    // Here you would update your data fetching with the new filters
  }

  if (!projectId) {
    return (
      <div className="container mx-auto py-12 text-center">
        <h1 className="text-2xl font-bold">Project ID is required</h1>
        <p className="mt-4">Please provide a valid project ID to view NFT details</p>
      </div>
    )
  }

  return (
    <div>
      {/* Filter Modal */}
      <FilterModal
        open={filterModalOpen}
        onOpenChange={setFilterModalOpen}
        onApplyFilters={handleApplyFilters}
      />

      <Suspense
        fallback={
          <div className="w-full">
            <div className="h-48 w-full animate-pulse bg-gray-800 md:h-64 lg:h-80"></div>
            <div className="container mx-auto px-4">
              <div className="relative -mt-16 mb-4 flex flex-col items-start gap-4 md:flex-row md:items-end">
                <div className="relative z-10 h-32 w-32 animate-pulse rounded-xl bg-gray-700"></div>
                <div className="mt-4 flex-1 md:mt-0">
                  <div className="mb-2 h-8 w-64 animate-pulse bg-gray-700"></div>
                  <div className="h-4 w-40 animate-pulse bg-gray-700"></div>
                </div>
              </div>
            </div>
          </div>
        }
      >
        <CollectionHeader cask={cask} stats={stats} isLoading={caskLoading} />
      </Suspense>

      <div className="container mx-auto py-4">
        <Tabs defaultValue="items" className="w-full">
          {/* Tab navigation - styled to match OpenSea UI */}
          <div className="mb-4 rounded-lg">
            <div className="scrollbar-hide flex overflow-x-auto">
              <TabsList
                defaultValue="items"
                className="flex w-full justify-start bg-transparent p-0"
              >
                <TabsTrigger
                  value="mint"
                  className="rounded-none px-4 pb-2 text-sm font-medium text-gray-500 data-[state=active]:border-b-2 data-[state=active]:border-foreground data-[state=active]:bg-transparent data-[state=active]:text-foreground dark:text-gray-400"
                >
                  Mint
                </TabsTrigger>
                <TabsTrigger
                  value="items"
                  className="rounded-none px-4 pb-2 text-sm font-medium text-gray-500 data-[state=active]:border-b-2 data-[state=active]:border-foreground data-[state=active]:bg-transparent data-[state=active]:text-foreground dark:text-gray-400"
                >
                  Items
                </TabsTrigger>

                <TabsTrigger
                  value="traits"
                  className="rounded-none px-4 pb-2 text-sm font-medium text-gray-500 data-[state=active]:border-b-2 data-[state=active]:border-foreground data-[state=active]:bg-transparent data-[state=active]:text-foreground dark:text-gray-400"
                >
                  Traits
                </TabsTrigger>
                <TabsTrigger
                  value="activity"
                  className="rounded-none px-4 pb-2 text-sm font-medium text-gray-500 data-[state=active]:border-b-2 data-[state=active]:border-foreground data-[state=active]:bg-transparent data-[state=active]:text-foreground dark:text-gray-400"
                >
                  Activity
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Search and filter controls */}
            <div className="mt-2 flex items-center justify-between dark:border-gray-800">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                  <Input
                    placeholder="Search by item or trait"
                    className="h-10 w-full rounded-md border-muted-foreground bg-transparent py-2 pl-10 pr-2 dark:border-muted-foreground"
                  />
                </div>
              </div>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 rounded-md"
                  id="filter-button"
                  onClick={() => setFilterModalOpen(true)}
                >
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4 4h16v4l-6 6v8l-4-2v-6l-6-6V4z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Button>
                <Button variant="ghost" size="icon" className="h-10 w-10 rounded-md">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3 6h18M10 12h11M3 12h4M3 18h18M10 6h11M3 6h4"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </div>

          <TabsContent value="items" className="mt-0">
            <div className="flex flex-col gap-6 lg:flex-row">
              <div className="hidden w-64 shrink-0 lg:block">
                <FilterSidebar onFilterChange={handleApplyFilters} />
              </div>
              <div className="flex-1">
                <NftGrid nfts={nfts} isLoading={nftsLoading} image={cask?.img} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="mt-0">
            <ActivityTable activities={activities} />
          </TabsContent>

          <TabsContent value="traits" className="mt-0">
            <div className="py-12 text-center">
              <h3 className="mb-2 text-xl font-medium">Traits Coming Soon</h3>
              <p className="text-gray-400">This feature is currently under development</p>
            </div>
          </TabsContent>

          <TabsContent value="mint" className="mt-0">
            <div className="py-12 text-center">
              <h3 className="mb-2 text-xl font-medium">Mint Coming Soon</h3>
              <p className="text-gray-400">This feature is currently under development</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <NftFooter />
    </div>
  )
}

export default NftDetailClient

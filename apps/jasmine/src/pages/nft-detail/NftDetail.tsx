import { Suspense } from 'react'
import dynamic from 'next/dynamic'

// Import client component with SSR disabled
const NftDetailClient = dynamic(() => import('./NftDetail.client'), {
  ssr: false,
})

const NftDetail = () => {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">Loading NFT details...</div>
      }
    >
      <NftDetailClient />
    </Suspense>
  )
}

// Prevent prerender at build time
export const getStaticProps = () => {
  return {
    props: {},
  }
}

export default NftDetail

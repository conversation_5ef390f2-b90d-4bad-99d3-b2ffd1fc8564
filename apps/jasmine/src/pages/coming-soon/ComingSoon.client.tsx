'use client'

import ConnectButton from '@/components/ConnectButton'
import { LogoBG } from '@/components/Logo/LogoBG'
import { useAppKitAccount } from '@reown/appkit/react'
import { isTMA, retrieveLaunchParams, RetrieveLPResult } from '@telegram-apps/sdk'
import { useCallback, useEffect, useState } from 'react'

async function createSubscription(data: { publicAddress: string; chatId: string; payload: any }) {
  const SERVER_URL = process.env.NEXT_PUBLIC_SERVER_URL
  if (!SERVER_URL) {
    throw new Error('SERVER_URL is not defined')
  }

  const res = await fetch(`${SERVER_URL}/notifications/subscriptions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })

  const json = await res.json()

  if (!res.ok || res.status >= 400) {
    console.log(`Error: ${json.message}`)
    throw new Error('Network response was not ok')
  }

  return json
}

const ComingSoonClient = () => {
  const [tgLaunchParams, setTgLaunchParams] = useState<RetrieveLPResult | null>(null)
  const { address, isConnected } = useAppKitAccount()

  const handleConnect = useCallback(async () => {
    console.log('Start handleConnect')
    if (!address || !tgLaunchParams) {
      return
    }
    console.log('handleConnect', address, tgLaunchParams.tgWebAppData!.user!.id)

    createSubscription({
      publicAddress: address?.toLowerCase(),
      chatId: tgLaunchParams.tgWebAppData!.user!.id.toString(),
      payload: tgLaunchParams.tgWebAppData?.user,
    })
  }, [address, tgLaunchParams])

  useEffect(() => {
    if (isTMA()) {
      const t = retrieveLaunchParams()
      setTgLaunchParams(t)
    }
  }, [])

  useEffect(() => {
    if (isConnected && tgLaunchParams) {
      handleConnect()
    }
  }, [isConnected, tgLaunchParams, handleConnect])

  return (
    <div className="container relative mt-2 min-h-[400px] w-full py-4">
      <div className="relative z-10 w-full font-frauces">
        <h3 className="mx-auto max-w-[60%] text-center text-3xl leading-9">
          The <span className="italic text-[#DE6A29]">Preferred</span> Whisky Trading Partner
        </h3>
        <h3 className="bg-gradient-to-b from-[#DE6A29] to-[#964a1e] bg-clip-text text-center text-xl text-transparent">
          Coming Soon
        </h3>
        {tgLaunchParams ? (
          <div className="space-y-4">
            <div className="flex justify-center pt-4">
              <ConnectButton />
            </div>

            {!isConnected ? (
              <p className="mx-auto max-w-[75%] text-center text-sm">
                {'Pair your Telegram now with exclusive rewards, and seamless notifications!'}
              </p>
            ) : (
              <p className="mx-auto max-w-[75%] text-center text-sm">
                {`Connect wallet successfully!`}
              </p>
            )}
          </div>
        ) : null}
      </div>

      <LogoBG className="absolute left-[50%] right-0 top-0 z-0 w-full max-w-[500px] translate-x-[-50%] px-14 opacity-30" />
    </div>
  )
}

export default ComingSoonClient

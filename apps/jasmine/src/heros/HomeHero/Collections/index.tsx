'use client'
import { But<PERSON> } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useMemo } from 'react'
import CollectionTableCustom from './CollectionsTableCustom'

const Collections = () => {
  const tabsData = useMemo(
    () => [
      { value: 'top_collection', label: 'Top collections', content: <CollectionTableCustom /> },
      { value: 'owned', label: 'Owned', content: <CollectionTableCustom /> },
      { value: 'watchlist', label: 'Watchlist', content: <CollectionTableCustom /> },
    ],
    [],
  )
  return (
    <div className="container flex items-center gap-5 pt-11">
      <Tabs defaultValue="top_collection" className="w-full">
        <div className="flex items-center gap-5">
          <TabsList className="bg-[#2D2D2D] text-[#BEBEBE]">
            {tabsData.map((tab) => (
              <TabsTrigger key={tab.value} value={tab.value}>
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <Button className={'rounded-xl bg-[#2D2D2D] text-white hover:text-black'}>See all</Button>
        </div>
        {tabsData.map(({ value, content }) => (
          <TabsContent key={value} value={value}>
            {content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}

export default Collections

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const collections = [
  {
    id: 1,
    image: '/assets/images/image_4.webp',
    name: 'Glassgow - 2018',
    nfts: 0,
    price: 0,
    volume: 37,
  },
  {
    id: 2,
    image: '/assets/images/image.webp',
    name: 'Glassgow - 2015',
    nfts: 0,
    price: 0,
    volume: 37,
  },
  {
    id: 3,
    image: '/assets/images/image_1.webp',
    name: 'Glassgow - 2013',
    nfts: 0,
    price: 0,
    volume: 37,
  },
  {
    id: 4,
    image: '/assets/images/image_1.webp',
    name: 'Glassgow - 2019',
    nfts: 0,
    price: 0,
    volume: 37,
  },
]

const CollectionTableCustom = () => {
  return (
    <Table>
      <TableHeader className="[&_tr]:border-b-[#C3C3C3]">
        <TableRow className="h-10 hover:bg-inherit">
          <TableHead>#</TableHead>
          <TableHead>Collection</TableHead>
          <TableHead>Volume</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {collections.map((collection) => (
          <TableRow key={collection.id} className="hover:bg-[#1D1D1D]">
            <TableCell>{collection.id}</TableCell>
            <TableCell>
              <div className="flex items-center gap-4">
                <img
                  src={collection.image}
                  alt={collection.name}
                  className="h-12 w-12 rounded-md"
                />
                <div>
                  <p className="font-medium text-white">{collection.name}</p>
                  <p className="text-sm text-gray-400">{collection.nfts} NFTs on Sale</p>
                  <p className="text-sm text-gray-400">
                    Form <span className="text-[#DE6A29]">{collection.price}</span> USD
                  </p>
                </div>
              </div>
            </TableCell>
            <TableCell className="text-white">{collection.volume} NFT</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export default CollectionTableCustom

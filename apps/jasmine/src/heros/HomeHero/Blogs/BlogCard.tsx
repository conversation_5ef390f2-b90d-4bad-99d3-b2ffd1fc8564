'use client'
import Image from 'next/image'
import React from 'react'

interface BlogCardProps {
  image: string
  title: string
  description: string
}

const BlogCard: React.FC<BlogCardProps> = ({ image, title, description }) => {
  return (
    <div className="flex flex-col rounded-2xl bg-[#151513] shadow-[0px_2px_6px_0px_#DE6A290D] md:min-h-96">
      <div className="h-full grow p-1">
        <Image
          src={image}
          alt={title}
          width={246}
          height={122}
          className="max-h-36 w-full rounded-lg object-cover md:max-h-[350px]"
        />
      </div>
      <div className="px-4 pb-7 pt-2">
        <h3 className="mb-2 font-frauces text-lg text-white">{title}</h3>
        <p className="text-sm text-gray-400">{description}</p>
      </div>
    </div>
  )
}

export default BlogCard

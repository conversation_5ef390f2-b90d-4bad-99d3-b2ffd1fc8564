'use client'
import Swiper<PERSON>rapper from '@/components/SwiperCarousel'
import { ArrowUpRight } from 'lucide-react'
import React from 'react'
import BlogCard from './BlogCard'

const MOCK_BLOGS = [
  {
    image: '/assets/images/image_blog_1.webp', // URL ảnh thay thế
    title: 'The Developing Whisky Market',
    description: 'Whisky trading booms as NFTs and blockchain open doors for all investors.',
  },
  {
    image: '/assets/images/image_blog_2.webp',
    title: 'Whisky as an Investment',
    description: 'Global whisky demand is fueled by sophistication, scarcity, and lasting value.',
  },
  {
    image: '/assets/images/image_blog_1.webp',
    title: 'Understanding Whisky Regions',
    description: 'Each whisky region brings unique flavors and characteristics to your glass.',
  },
]

interface Props {}

const BlogSection = (props: Props) => {
  return (
    <div className="bg-[#1D1D1D] pb-9">
      <div className="container mx-auto flex items-center justify-between py-4">
        <p className="font-frauces text-sm tracking-wide text-white">GAIN MORE INTEREST</p>
        <div className="flex items-center text-xs/[1]">
          <p>Learn more</p> <ArrowUpRight size={16} />
        </div>
      </div>
      <SwiperWrapper
        items={MOCK_BLOGS}
        renderItem={(blog) => (
          <BlogCard image={blog.image} title={blog.title} description={blog.description} />
        )}
        spaceBetween={5}
        slidesPerView={1}
        style={{
          paddingLeft: '20px',
          paddingRight: '20px',
        }}
        autoplay
      />
    </div>
  )
}

export default BlogSection

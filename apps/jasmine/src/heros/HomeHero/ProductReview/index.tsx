'use client'
import { LogoBG } from '@/components/Logo/LogoBG'
import SwiperWrapper from '@/components/SwiperCarousel'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import ProductViewCard from './ProductViewCard'

const MOCK_PRODUCT_CARD = [
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018',
  },
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018 2',
  },
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018 3',
  },
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018 4',
  },
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018 5',
  },
  {
    count: 0,
    price: 0,
    name: 'Glassgow - 2018 6',
  },
]

interface Props {}

const ProductReview = (props: Props) => {
  return (
    <div className="relative w-full">
      <div className="relative z-10 mb-6 flex w-full flex-col items-center pt-3 text-center">
        <p className="font-frauces text-sm">
          The <span className="text-[#DE6A29]">Preferred</span> Whisky Trading Partner
        </p>
        <div className="mt-1 h-px w-1/3 bg-[#6F6F6F]"></div>
      </div>
      <div>
        {/* {MOCK_PRODUCT_CARD.map((product) => (
          <ProductViewCard key={product.name} {...product} />
        ))} */}
      </div>

      <SwiperWrapper
        items={MOCK_PRODUCT_CARD}
        renderItem={(product) => <ProductViewCard {...product} />}
        spaceBetween={10}
        slidesPerView={1}
        className="min-h-[333px]"
        style={{
          paddingLeft: '20px',
          paddingRight: '20px',
        }}
        autoplay
      />
      <LogoBG className="absolute left-0 right-0 top-0 z-0 w-full px-14" />
    </div>
  )
}

export default ProductReview

import Image from 'next/image'

interface Props {
  count: number
  price: number
  name: string
}

const ProductViewCard = ({ count, price, name }: Props) => {
  return (
    <div className="relative flex flex-col items-center justify-center">
      <Image
        height={333}
        width={333}
        src={'/assets/images/product-1.webp'}
        alt="product"
        className="relative z-10 h-auto w-full"
      />
      <div className="absolute bottom-0 left-0 z-30 space-y-1 pb-6 pl-4">
        <p className="font-frauces text-base font-semibold tracking-wide">{name}</p>
        <p className="text-xs">{count} NFTs on Sale</p>
        <p className="text-xs">
          From <span className="text-[#DE6A29]">{price}</span> USD
        </p>
      </div>
      <Image
        height={333}
        width={333}
        src={'/assets/frames/bg-frame.webp'}
        alt="product"
        className="absolute left-0 right-0 top-0 z-10 h-auto w-full"
      />
    </div>
  )
}

export default ProductViewCard

import React from 'react'

import type { Page } from '@/payload-types'

import RichText from '@/components/RichText'

type LowImpactHeroType =
  | {
      children?: React.ReactNode
      richText?: never
    }
  | (Omit<Page['hero'], 'richText'> & {
      children?: never
      richText?: Page['hero']['richText']
    })

export const LowImpactHero: React.FC<LowImpactHeroType> = ({ children, richText }) => {
  return (
    <div className="container">
      <div className="max-w-[48rem]">
        {children || (richText && <RichText data={richText} enableGutter={false} />)}
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Possimus rerum quidem sit non
          sapiente vel vero, quis dolor eligendi accusamus minima blanditiis distinctio architecto,
          incidunt eaque quam ducimus nobis soluta.
        </p>
      </div>
    </div>
  )
}

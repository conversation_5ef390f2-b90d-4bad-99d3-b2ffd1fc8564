import { gql } from 'urql'

export const GET_NFTS_BY_PROJECT_ID = gql`
  query getAllNftForProjectId($where: JSON) {
    allNftProjects(where: $where) {
      projectId
      tokenId
      owner
      inMarket
      lastPrice
      lastTrade
    }
  }
`

export const GET_NFTS_WITH_PROJECT = gql`
  query getNftsWithProject($limit: Int = 10, $skip: Int = 0, $sort: JSON, $where: JSON) {
    nfts(limit: $limit, skip: $skip, sort: $sort, where: $where) {
      inMarket
      owner
      projectId
      tokenId
      lastPrice
      lastTrade
      project {
        name
        img
        cover
        ays
      }
    }
  }
`

export const COUNT_NFTS = gql`
  query countNFTs($where: JSON) {
    countNFTs(where: $where) {
      count
    }
  }
`

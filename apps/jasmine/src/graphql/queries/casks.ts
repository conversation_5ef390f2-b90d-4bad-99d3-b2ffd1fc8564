import { gql } from 'urql'

export const GET_MY_CASKS = gql`
  query getCasks($limit: Int = 10, $skip: Int = 0, $sort: JSON, $where: JSON) {
    casks(limit: $limit, skip: $skip, sort: $sort, where: $where) {
      creator
      cover
      name
      desc
      img
      info
      distillery
      commissionInfo
      projectId
      tokenId
      nftOnSaleAmount
      minPrice
      maxPrice
      region
      nfts
      maxAmount
      targetAmount
      nftOnSale
      nftOnAuction
      nftAmount
      updatedAt
      ays
      targetBottlingDate
    }
  }
`

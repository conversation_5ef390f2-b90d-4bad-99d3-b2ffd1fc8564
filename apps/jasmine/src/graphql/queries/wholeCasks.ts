import { gql } from 'urql'

export const GET_WHOLE_CASKS = gql`
  query getWholeCasks($limit: Int = 10, $skip: Int = 0, $sort: JSON, $where: JSON) {
    wholeCasks(limit: $limit, skip: $skip, sort: $sort, where: $where) {
      creator
      cover
      name
      desc
      img
      info
      distillery
      commissionInfo
      projectId
      tokenId
      region
      maxAmount
      targetAmount
      lastPrice
      updatedAt
      ays
      targetBottlingDate
      type
    }
  }
`

export const COUNT_WHOLE_CASKS = gql`
  query countWholeCasks($where: JSON) {
    countWholeCasks(where: $where) {
      count
    }
  }
`

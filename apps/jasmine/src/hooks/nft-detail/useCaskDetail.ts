import { useQuery } from 'urql'
import { getCasks, getCasksVariables } from '@/../schemaTypes'
import { getWholeCasks, getWholeCasksVariables } from '@/../schemaTypes'
import { CombinedError } from 'urql'

// GraphQL queries
const GET_MY_CASKS = `
  query getCasks($where: JSON) {
    casks(where: $where) {
      name
      desc
      img
      cover
      creator
      distillery
      region
      ays
      targetBottlingDate
      minPrice
      maxPrice
      nftAmount
      nftOnSaleAmount
      projectId
    }
  }
`

const GET_WHOLE_CASKS = `
  query getWholeCasks($where: JSON) {
    wholeCasks(where: $where) {
      name
      desc
      img
      cover
      creator
      distillery
      region
      ays
      targetBottlingDate
      minPrice
      maxPrice
      nftAmount
      nftOnSaleAmount
      projectId
    }
  }
`

interface UseCaskDetailProps {
  projectId?: string
  type?: 'regular' | 'whole'
}

export const useCaskDetail = ({ projectId, type = 'regular' }: UseCaskDetailProps) => {
  // Check if we're running on the server during SSR
  const isServerSideRendering = typeof window === 'undefined'

  // Fetch regular cask details
  const [regularCaskResult] = useQuery<getCasks, getCasksVariables>({
    query: GET_MY_CASKS,
    variables: {
      where: { projectId }
    },
    // Pause the query during SSR or if conditions not met
    pause: isServerSideRendering || !projectId || type !== 'regular'
  })

  // Fetch whole cask details
  const [wholeCaskResult] = useQuery<getWholeCasks, getWholeCasksVariables>({
    query: GET_WHOLE_CASKS,
    variables: {
      where: { projectId }
    },
    // Pause the query during SSR or if conditions not met
    pause: isServerSideRendering || !projectId || type !== 'whole'
  })

  // Determine which result to use based on the type
  const result = type === 'regular' ? regularCaskResult : wholeCaskResult
  const { data, fetching, error } = result

  // Extract the cask data
  // Handle optional chaining and potential undefined values
  const cask = type === 'regular'
    ? (data as getCasks)?.casks?.[0] || null
    : (data as getWholeCasks)?.wholeCasks?.[0] || null

  return {
    cask,
    isLoading: fetching,
    error: error as CombinedError | undefined
  }
}

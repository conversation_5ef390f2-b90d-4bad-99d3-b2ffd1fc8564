import { useQuery } from 'urql'
import { getAllNftForProjectId, getAllNftForProjectIdVariables } from '@/../schemaTypes'
import { CombinedError } from 'urql'

// GraphQL query for NFTs
const GET_NFTS_BY_PROJECT_ID = `
  query getAllNftForProjectId($where: JSON) {
    allNftProjects(where: $where) {
      projectId
      tokenId
      owner
      inMarket
      lastPrice
      lastTrade
    }
  }
`

export interface Activity {
  id: string
  eventType: 'Sale' | 'Listing' | 'Offer' | 'Mint' | 'Transfer'
  tokenId: string
  price: number
  from: string
  to: string
  timestamp: number
}

interface UseNftCollectionProps {
  projectId?: string
  sort?: 'recent' | 'price_asc' | 'price_desc' | 'tokenId_asc' | 'tokenId_desc'
  limit?: number
  filter?: {
    onlyOnSale?: boolean
    priceRange?: [number, number]
    attributes?: { [key: string]: string[] }
  }
}

export const useNftCollection = ({ projectId, sort = 'recent', filter }: UseNftCollectionProps) => {
  // Check if we're running on the server during SSR
  const isServerSideRendering = typeof window === 'undefined'

  // Construct where clause based on filters
  const whereClause: any = { projectId }

  if (filter?.onlyOnSale) {
    whereClause.onSale = true
  }

  if (filter?.priceRange) {
    whereClause.price_gte = filter.priceRange[0]
    whereClause.price_lte = filter.priceRange[1]
  }

  // Fetch NFTs for the project
  const [nftsResult] = useQuery<getAllNftForProjectId, getAllNftForProjectIdVariables>({
    query: GET_NFTS_BY_PROJECT_ID,
    variables: {
      where: whereClause,
    },
    // Pause the query during SSR or if no projectId
    pause: isServerSideRendering || !projectId,
  })

  const { data, fetching, error } = nftsResult
  const nfts = data?.allNftProjects || []

  // Sort NFTs based on the sort parameter
  const sortedNfts = [...nfts].sort((a, b) => {
    switch (sort) {
      case 'price_asc':
        return (a.lastPrice || 0) - (b.lastPrice || 0)
      case 'price_desc':
        return (b.lastPrice || 0) - (a.lastPrice || 0)
      case 'tokenId_asc':
        return a.tokenId - b.tokenId
      case 'tokenId_desc':
        return b.tokenId - a.tokenId
      case 'recent':
      default:
        return 0 // Default to the order returned by the API
    }
  })

  // Generate mock activity data
  const activities: Activity[] = Array.from({ length: 20 }, (_, i) => {
    const eventTypes: Activity['eventType'][] = ['Sale', 'Listing', 'Offer', 'Mint', 'Transfer']
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    const tokenId = Math.floor(Math.random() * 1000).toString()
    const price = parseFloat((Math.random() * 0.1 + 0.04).toFixed(4))
    const timestamp = Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000) // Random time in the last 30 days

    return {
      id: `activity-${i}`,
      eventType,
      tokenId,
      price,
      from: `0x${Math.random().toString(16).substring(2, 14)}`,
      to: `0x${Math.random().toString(16).substring(2, 14)}`,
      timestamp,
    }
  })

  // Mock collection stats
  const stats = {
    floorPrice: 0.0449,
    volume24h: 7.14,
    totalVolume: 28.56,
    owners: 230,
    items: nfts.length || 634,
    listed: Math.floor((nfts.length || 634) * 0.15),
    totalSales: 78,
  }

  return {
    nfts: sortedNfts,
    isLoading: fetching,
    error: error as CombinedError | undefined,
    stats,
    activities,
  }
}

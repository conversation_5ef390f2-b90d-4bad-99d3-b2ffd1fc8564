'use client'
import { GET_MY_CASKS } from '@/graphql/queries/casks'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import type { getCasks, getCasks_casks, getCasksVariables } from 'schemaTypes'
import { useQuery } from 'urql'
import EmblaCarousel from '../Carousel/EmblaCarousel'
import MarketplaceCard from '../MarketplaceCard'

export default function MarketplaceCarousel() {
  const router = useRouter()
  const [marketplaceCasks, setMarketplaceCasks] = useState<getCasks_casks[]>([])
  const [result] = useQuery<getCasks, getCasksVariables>({
    query: GET_MY_CASKS,
    // variables: {
    //   where: { projectId: '1' },
    // },
  })

  useEffect(() => {
    if (result?.data?.casks) {
      setMarketplaceCasks(result.data.casks)
    }
  }, [result.data])

  return (
    <>
      {marketplaceCasks.length === 0 && (
        <div className="my-4 rounded-lg bg-gray-50 p-10 text-center text-gray-500">
          <p>No marketplace casks available at the moment.</p>
        </div>
      )}
      {marketplaceCasks.length > 0 && (
        <EmblaCarousel
          slides={marketplaceCasks.map((marketplaceCask) => (
            <div
              key={marketplaceCask.tokenId}
              className="w-full px-2"
            >
              <MarketplaceCard
                data={marketplaceCask}
                onButtonClick={() => router.push(`/cask-collection?projectId=${marketplaceCask.projectId}&type=regular`)}
              />
            </div>
          ))}
          options={{
            align: 'center',
            slidesToScroll: 1,
          }}
          showArrowButton={false}
          showNavigationButton={false}
          slideSize="basis-5/6"
        />
      )}
    </>
  )
}

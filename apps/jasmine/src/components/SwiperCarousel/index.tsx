'use client'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import type { SwiperOptions } from 'swiper/types'

interface SwiperWrapperProps<T> extends SwiperOptions {
  items: T[]
  renderItem: (item: T) => React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const SwiperWrapper = <T,>({
  items,
  renderItem,
  className = '',
  style,
  ...props
}: SwiperWrapperProps<T>) => {
  return (
    <Swiper className={className} style={style} {...props}>
      {items.map((item, index) => (
        <SwiperSlide key={index}>{renderItem(item)}</SwiperSlide>
      ))}
    </Swiper>
  )
}

export default SwiperWrapper

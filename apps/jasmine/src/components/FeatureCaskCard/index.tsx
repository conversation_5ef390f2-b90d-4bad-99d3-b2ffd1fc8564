import Image from 'next/image'
import type { getCasks_casks } from 'schemaTypes'

interface Props {
  data: getCasks_casks
  onButtonClick?: () => void
}

const FeatureCaskCard = ({ data, onButtonClick }: Props) => {
  return (
    <div className="relative mb-4 aspect-square overflow-hidden">
      {/* Image container with full height and gradient overlay */}
      <div className="absolute inset-0">
        <Image
          src={data.cover}
          alt={`${data.name} banner`}
          fill
          className="object-cover"
          priority
        />
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent from-[10%] to-[80%] to-stone-900/95"></div>
      </div>

      {/* Content positioned at the bottom of the card */}
      <div className="absolute bottom-0 left-0 right-0 z-10 p-6 text-white md:p-8">
        <div className="flex flex-col gap-2 md:gap-4">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-bold text-primary-foreground">{data.name}</h1>
          </div>

          <p className="text-sm text-muted-foreground">
            By {`${data.creator.slice(0, 5)}...${data.creator.slice(-5)}`}
          </p>

          <div className="flex gap-2 rounded-lg border border-muted-foreground/60 bg-muted-foreground/30 p-2 backdrop-blur-sm">
            <div className="flex grow flex-col border-r border-muted-foreground/60 pr-2">
              <span className="text-xs font-thin text-primary-foreground">Highest price</span>
              <span className="text-lg font-semibold text-primary-foreground">
                ${data.maxPrice.toLocaleString()}
              </span>
            </div>
            <div className="flex flex-col border-r border-muted-foreground/60 pr-2">
              <span className="text-xs font-thin text-primary-foreground">On sale</span>
              <span className="text-lg font-semibold text-primary-foreground">
                {data.nftOnSaleAmount} NFTs
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-xs font-thin text-primary-foreground">Total</span>
              <span className="text-lg font-semibold text-primary-foreground">
                {data.nftAmount} NFTs
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FeatureCaskCard

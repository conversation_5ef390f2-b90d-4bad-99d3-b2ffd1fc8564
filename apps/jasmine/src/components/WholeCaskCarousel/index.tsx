'use client'
import { GET_WHOLE_CASKS } from '@/graphql/queries/wholeCasks'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useQuery } from 'urql'
import EmblaCarousel from '../Carousel/EmblaCarousel'
import WholeCaskCard from '../WholeCaskCard'
import { getWholeCasks, getWholeCasks_wholeCasks, getWholeCasksVariables } from '@/../schemaTypes'

export default function WholeCaskCarousel() {
  const router = useRouter()
  const [wholeCasks, setWholeCasks] = useState<getWholeCasks_wholeCasks[]>([])
  const [result] = useQuery<getWholeCasks, getWholeCasksVariables>({
    query: GET_WHOLE_CASKS,
    variables: {
      where: { isHidden: false },
    },
  })

  useEffect(() => {
    if (result?.data?.wholeCasks) {
      setWholeCasks(result.data.wholeCasks)
    }
  }, [result.data])

  return (
    <>
      {wholeCasks.length === 0 && (
        <div className="my-4 rounded-lg bg-gray-50 p-10 text-center text-gray-500">
          <p>No marketplace casks available at the moment.</p>
        </div>
      )}
      {wholeCasks.length > 0 && (
        <EmblaCarousel
          slides={wholeCasks.map((wholeCask) => (
            <div key={wholeCask.tokenId} className="w-full px-2">
              <WholeCaskCard 
                data={wholeCask} 
                onButtonClick={() => router.push(`/nft-detail?projectId=${wholeCask.projectId}&type=whole`)}
              />
            </div>
          ))}
          options={{
            align: 'center',
            slidesToScroll: 1,
          }}
          showArrowButton={false}
          showNavigationButton={false}
          slideSize="basis-5/6"
        />
      )}
    </>
  )
}

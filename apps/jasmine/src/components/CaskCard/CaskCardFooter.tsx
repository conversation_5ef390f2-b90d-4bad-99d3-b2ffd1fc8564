import React from 'react'
import { BaseCaskData, CardFooterProps } from './types'

export function CaskCardFooter<T extends BaseCaskData>({
  data,
  subTitle,
  renderPrice,
}: CardFooterProps<T>) {
  return (
    <div className="absolute bottom-0 left-0 right-0 p-4 pt-0">
      <div className="flex flex-col">
        <h3 className="text-xl font-bold text-primary">{data.name}</h3>
        <div className="flex items-center justify-between">
          <div>
            {/* {subTitle && <p className="text-xs text-muted-foreground">{subTitle}</p>} */}
            <div className="text-lg font-semibold">{renderPrice(data)}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { BaseCaskData, HoverDetailProps } from './types'
import { calculateAge, formatDate } from './utils'

export function DefaultCaskDetails<T extends BaseCaskData>({ data }: { data: T }) {
  return (
    <>
      <div className="mt-3 space-y-1">
        <p className="translate-x-1/2 transform text-sm text-muted-foreground opacity-0 transition-all delay-75 duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
          Age Young Spirit
        </p>
        <p className="translate-x-1/2 transform text-lg font-semibold opacity-0 transition-all delay-100 duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
          {formatDate(data.ays)}
        </p>
      </div>
      <div className="mt-3 space-y-1">
        <p className="translate-x-1/2 transform text-sm text-muted-foreground opacity-0 transition-all delay-150 duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
          Current Age of Whisky
        </p>
        <p className="translate-x-1/2 transform text-xl font-semibold opacity-0 transition-all delay-200 duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
          {calculateAge(data.ays)} years
        </p>
      </div>
    </>
  )
}

export function CaskHoverDetail<T extends BaseCaskData>({
  data,
  renderDetails,
  renderPrice,
  buttonText = 'See NFTs',
  buttonProps,
  onButtonClick,
}: HoverDetailProps<T>) {
  return (
    <div className="absolute bottom-0 left-0 right-0 top-0 z-10 overflow-hidden bg-background p-4 opacity-0 transition-all duration-300 group-hover:opacity-100">
      <div className="flex h-full flex-col justify-between">
        <div className="flex flex-col">
          <h3 className="translate-x-1/2 transform text-xl font-bold text-primary opacity-0 transition-all duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
            {data.name}
          </h3>

          {/* Details section - either custom or default */}
          {renderDetails ? renderDetails(data) : <DefaultCaskDetails data={data} />}
        </div>

        {/* Price section and button */}
        <div className="mt-5 flex items-center justify-between">
          {renderPrice && (
            <div>{renderPrice(data)}</div>
          )}
          <Button
            variant="default"
            className="delay-250 translate-y-full transform rounded-full bg-primary opacity-0 transition-all duration-300 ease-out group-hover:translate-y-0 group-hover:opacity-100"
            onClick={onButtonClick}
            {...buttonProps}
          >
            {buttonText}
          </Button>
        </div>
      </div>
    </div>
  )
}

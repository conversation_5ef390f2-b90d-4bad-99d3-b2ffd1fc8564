import React from 'react'
import Image from 'next/image'
import { cn } from '@/utilities/cn'
import { BaseCaskData, CaskCardProps } from './types'
import { CaskHoverDetail } from './CaskHoverDetail'
import { CaskCardFooter } from './CaskCardFooter'

export function CaskCard<T extends BaseCaskData>({
  data,
  className,
  cardType,
  renderDetails,
  renderPrice = () => null,
  buttonText = 'See NFTs',
  buttonProps,
  onButtonClick,
}: CaskCardProps<T>) {
  return (
    <div className={cn(
      "hover:primary group relative overflow-hidden rounded-xl border border-muted-foreground/50 shadow-md transition-all hover:border-primary hover:shadow-lg",
      className
    )}>
      {/* Hover state (detailed info) */}
      <CaskHoverDetail
        data={data}
        renderDetails={renderDetails}
        renderPrice={renderPrice}
        buttonText={buttonText}
        buttonProps={buttonProps}
        onButtonClick={onButtonClick}
      />

      {/* Image container with padding */}
      <div className="relative">
        <div className="aspect-square overflow-hidden rounded-xl bg-muted/10">
          <Image
            src={data.cover}
            alt={data.name}
            width={300}
            height={300}
            className="h-full w-full object-cover transition-transform"
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-b from-black/10 to-black/90"></div>
      </div>

      {/* Non-hover state (minimal info) */}
      <CaskCardFooter
        data={data}
        subTitle={cardType}
        renderPrice={renderPrice}
      />
    </div>
  )
}

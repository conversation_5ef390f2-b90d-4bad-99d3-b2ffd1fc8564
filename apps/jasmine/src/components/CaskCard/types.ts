import { ComponentProps, ReactNode } from 'react'
import { ButtonProps } from '../ui/button'

export interface BaseCaskData {
  name: string
  cover: string
  ays: any // Date or string representation
}

export interface HoverDetailProps<T extends BaseCaskData> {
  data: T
  renderDetails?: (data: T) => ReactNode
  renderPrice?: (data: T) => ReactNode
  buttonText?: string
  buttonProps?: Partial<ButtonProps>
  onButtonClick?: () => void
}

export interface CardFooterProps<T extends BaseCaskData> {
  data: T
  subTitle?: string
  renderPrice: (data: T) => ReactNode
}

export interface CaskCardProps<T extends BaseCaskData> {
  data: T
  className?: string
  cardType?: string // E.g., "Whole Cask NFT" or "Regular NFT"
  renderDetails?: (data: T) => ReactNode
  renderPrice?: (data: T) => ReactNode
  buttonText?: string
  buttonProps?: Partial<ButtonProps>
  onButtonClick?: () => void
}

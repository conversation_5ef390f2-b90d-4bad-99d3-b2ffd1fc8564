import React from 'react'
import { useRouter } from 'next/navigation'
import { getCasks_casks } from '@/../schemaTypes'
import { CaskCard } from '@/components/CaskCard'

interface Props {
  data: getCasks_casks
  onButtonClick?: () => void
}

const MarketplaceCard = ({ data, onButtonClick }: Props) => {
  const router = useRouter()

  // Render price display for marketplace card
  const renderPrice = (caskData: getCasks_casks) => {
    return (
      <>
        <p className="transform text-xs text-muted-foreground">
          {caskData.nftOnSaleAmount} NFTs on Sale
        </p>
        <p className="transform text-lg font-semibold text-white dark:text-white">
          from {caskData.minPrice} USDT
        </p>
      </>
    )
  }

  // Handle button click to navigate to NFT detail page
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick()
    } else {
      router.push(`/nft-detail?projectId=${data.projectId}&type=regular`)
    }
  }

  return (
    <CaskCard
      data={data}
      cardType={`${data.nftOnSaleAmount} NFTs on Sale`}
      renderPrice={renderPrice}
      onButtonClick={handleButtonClick}
    />
  )
}

export default MarketplaceCard

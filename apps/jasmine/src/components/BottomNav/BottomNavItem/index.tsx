'use client'

import { cn } from '@/utilities/cn'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface NavItemProps {
  path: string
  label: string
  Icon: React.ComponentType
  buttonClass?: string
  labelClass?: string
}

const BottomNavItem = ({ path, label, Icon, buttonClass, labelClass }: NavItemProps) => {
  const pathname = usePathname()

  return (
    <Link
      className={cn(
        'flex flex-col items-center gap-2 text-[#C3C3C3]',
        pathname === path && 'text-[#DE6A29]',
        buttonClass,
      )}
      href={path}
    >
      <Icon />
      <p className={cn('text-xs font-normal', labelClass)}>{label}</p>
    </Link>
  )
}

export default BottomNavItem

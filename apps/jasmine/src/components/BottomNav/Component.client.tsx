'use client'
import { cn } from '@/utilities/cn'
import { Bell, Home, User, Wine } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Fragment } from 'react'

interface Props {}

const pagesNoNeedBottomNav = ['coming-soon']

const NAVIGATION_DATA = [
  {
    icon: Home,
    label: 'Home',
    path: '/',
  },
  {
    icon: Wine,
    label: 'Marketplace',
    path: '/marketplace',
  },
  {
    icon: Bell,
    label: 'Notification',
    path: '/notification',
  },
  {
    icon: User,
    label: 'Profile',
    path: '/profile',
  },
]

const BottomNavClient = (props: Props) => {
  const pathname = usePathname()

  const isPagesNoNeedBottomNav = Boolean(
    pagesNoNeedBottomNav.includes((pathname ? pathname.split('/') : [])[1]),
  )

  return (
    <Fragment>{isPagesNoNeedBottomNav ? null : <BottomNavWrapper pathname={pathname} />}</Fragment>
  )
}

export default BottomNavClient

const BottomNavWrapper = ({ pathname }: { pathname: string | null }) => {
  return (
    <div className="fixed bottom-12 z-50 w-full px-4">
      <div className="mx-auto flex max-w-96 justify-between rounded-xl border border-[white]/10 bg-white/10 px-8 py-3 backdrop-blur-md">
        {NAVIGATION_DATA.map((navItem) => (
          <Link
            key={navItem.path}
            className={cn(
              'flex flex-col items-center gap-2 text-[#C3C3C3]',
              pathname === navItem.path && 'text-[#DE6A29]',
            )}
            href={navItem.path}
          >
            <navItem.icon />
            <p className="text-xs font-normal">{navItem.label}</p>
          </Link>
        ))}
      </div>
    </div>
  )
}

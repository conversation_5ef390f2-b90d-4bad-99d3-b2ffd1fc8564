import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import React, { useCallback, useEffect, useState } from 'react'
import { NextButton, PrevButton, usePrevNextButtons } from './EmblaCarouselArrowButton'
import { DotButton, useDotButton } from './EmblaCarouselDotButton'
import { cn } from '@/utilities/cn'

type PropType = {
  slides: React.ReactNode[]
  options?: EmblaOptionsType
  showArrowButton?: boolean
  showNavigationButton?: boolean
  slideSize?: string
}

const EmblaCarousel: React.FC<PropType> = (props) => {
  const {
    slides,
    options = { loop: true, align: 'center' },
    showArrowButton = true,
    showNavigationButton = true,
    slideSize = 'basis-full',
  } = props

  const [emblaRef, emblaApi] = useEmblaCarousel(options)

  const { prevBtnDisabled, nextBtnDisabled, onPrevButtonClick, onNextButtonClick } =
    usePrevNextButtons(emblaApi)

  const { selectedIndex, scrollSnaps, onDotButtonClick } = useDotButton(emblaApi)

  return (
    <div className="relative">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex">
          {slides.map((slide, index) => (
            <div key={index} className={cn('min-w-0 flex-none', slideSize)}>
              {slide}
            </div>
          ))}
        </div>
      </div>

      <div>
        {showArrowButton && (
          <>
            <PrevButton
              disabled={prevBtnDisabled}
              onClick={onPrevButtonClick}
              className="absolute left-0 top-1/2 h-full -translate-y-1/2 transition ease-in-out hover:bg-gradient-to-l hover:from-transparent hover:to-gray-800/50"
            />
            <NextButton
              disabled={nextBtnDisabled}
              onClick={onNextButtonClick}
              className="absolute right-0 top-1/2 h-full -translate-y-1/2 transition ease-in-out hover:bg-gradient-to-r hover:from-transparent hover:to-gray-800/50"
            />
          </>
        )}

        {showNavigationButton && (
          <div className="flex justify-center gap-2">
            {scrollSnaps.map((_, index) => (
              <DotButton
                key={index}
                onClick={() => onDotButtonClick(index)}
                className={`h-1 w-10 rounded-sm ${index === selectedIndex ? 'bg-primary' : 'bg-muted'}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default EmblaCarousel

import React from 'react'

interface Props {}

const NftFooter = (props: Props) => {
  return (
    <div className="fixed bottom-0 z-10 flex w-full flex-col justify-center gap-4 bg-background p-4 shadow-[rgba(0,0,0,0.35)_0px_-5px_15px] md:flex-row md:items-center">
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1 rounded-md bg-muted-foreground">
          <button className="px-5 py-2 text-sm text-primary-foreground">−</button>
          <input
            type="number"
            className="w-6 border-none bg-transparent text-center text-primary-foreground"
            value="0"
            readOnly
          />
          <button className="px-5 py-2 text-sm text-primary-foreground">+</button>
        </div>
        <span className="mx-2 text-sm text-gray-400">Max per item</span>
        <input
          type="number"
          className="w-12 border-none bg-transparent text-center"
          value="0"
          readOnly
        />
        <span className="mx-2 text-sm">ETH</span>
      </div>
      <div className="flex justify-between gap-2">
        <button className="grow rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground">
          Buy floor
        </button>
        <button className="rounded-md border  bg-muted-foreground px-6 py-2 text-sm font-medium text-primary-foreground">
          Make collection offer
        </button>
      </div>
    </div>
  )
}

export default NftFooter

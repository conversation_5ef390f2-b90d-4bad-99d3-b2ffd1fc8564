'use client'

import React from 'react'
import { getAllNftForProjectId_allNftProjects } from '@/../schemaTypes'

// NFT Card component for displaying individual NFTs
const NftCard: React.FC<{ nft: getAllNftForProjectId_allNftProjects; image?: string }> = ({
  nft,
  image,
}) => {

  return (
    <div className="overflow-hidden rounded-lg border border-secondary bg-secondary transition-colors hover:border-gray-500">
      <div className="relative aspect-square overflow-hidden">
        <img
          src={image || 'https://picsum.photos/300/300'}
          alt={`NFT #${nft.tokenId}`}
          className="h-full w-full object-cover"
        />
      </div>
      <div className="p-3">
        <div className="mb-1 flex items-center justify-between">
          <p className="text-sm font-medium">{`NFT #${nft.tokenId}`}</p>
          <span className="text-xs text-gray-400">#{nft.tokenId}</span>
        </div>
        <div className="flex items-center justify-between">
          <p className="text-xs text-gray-400">Price</p>
          <p className="text-sm font-medium">
            {typeof nft.lastPrice === 'number' ? `${nft.lastPrice.toFixed(4)} ETH` : '0.0449 ETH'}
          </p>
        </div>
      </div>
    </div>
  )
}

interface NftGridProps {
  nfts: getAllNftForProjectId_allNftProjects[]
  isLoading?: boolean
  image?: string
}

const NftGrid: React.FC<NftGridProps> = ({ nfts, isLoading, image }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="animate-pulse overflow-hidden rounded-lg bg-gray-800">
            <div className="aspect-square bg-gray-700"></div>
            <div className="p-3">
              <div className="mb-2 h-4 rounded bg-gray-700"></div>
              <div className="h-3 w-2/3 rounded bg-gray-700"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <input type="checkbox" className="mr-2 h-4 w-4 rounded border-gray-700 bg-gray-800" />
          <p className="text-sm text-gray-400">{nfts.length} ITEMS</p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4">
        {nfts.map((nft) => (
          <NftCard key={`${nft.projectId}-${nft.tokenId}`} nft={nft} image={image} />
        ))}
      </div>


    </div>
  )
}

export default NftGrid

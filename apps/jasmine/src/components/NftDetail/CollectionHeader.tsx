'use client'

import dayjs from 'dayjs'
import { Check, Diamond, Globe, Info, Layers, MoreHorizontal, ShieldCheck, X } from 'lucide-react'
import Image from 'next/image'

import { getCasks_casks, getWholeCasks_wholeCasks } from '@/../schemaTypes'
import { Skeleton } from '@/components/ui/skeleton'
import NavIcon from '../NavIcon'

interface CollectionStats {
  floorPrice: number
  totalVolume: number
  items: number
  owners: number
  listed: number
  totalSales: number
}

interface CollectionHeaderProps {
  cask?: getCasks_casks | getWholeCasks_wholeCasks | null
  stats: CollectionStats
  isLoading: boolean
}

const CollectionHeader = ({ cask, stats, isLoading }: CollectionHeaderProps) => {
  if (isLoading) {
    return <CollectionHeaderSkeleton />
  }

  if (!cask) {
    return (
      <div className="w-full bg-gradient-to-r from-gray-900 to-gray-800 py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-2xl font-bold text-white">Collection not found</h1>
        </div>
      </div>
    )
  }

  return (
    <div className="relative aspect-square overflow-hidden">
      {/* Image container with full height and gradient overlay */}
      <div className="absolute inset-0">
        <Image
          src={cask.cover}
          alt={`${cask.name} banner`}
          fill
          className="object-cover"
          priority
        />
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent from-[10%] to-stone-900/95 to-[80%]"></div>
      </div>

      {/* Content positioned at the bottom of the card */}
      <div className="absolute bottom-0 left-0 right-0 z-10 p-6 text-white md:p-8">
        <div className="flex items-center gap-4">
          <Image
            src={cask.cover || '/assets/placeholder-banner.jpg'}
            alt={cask.name}
            width={60}
            height={60}
            className="rounded border border-muted-foreground/60 shadow-lg"
            priority
          />
          <div className="flex flex-col gap-2 md:gap-4">
            <div className="flex items-center gap-2">
              <h1 className="flex items-center gap-2 text-xl font-bold text-primary-foreground">
                {cask.name} <ShieldCheck size={20} />
              </h1>
            </div>

            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <NavIcon href="/info" icon={<Info size={20} />} />
              <NavIcon href="/documents" icon={<Layers size={20} />} />
              <NavIcon href="/globe" icon={<Globe size={20} />} />
              <NavIcon href="/x" icon={<X size={20} />} />
              <NavIcon href="/more" icon={<MoreHorizontal size={20} />} />
            </div>
          </div>
        </div>
        <div className="mt-4 flex items-center gap-2 overflow-x-auto">
          <div className="flex items-center rounded-md border border-muted-foreground/80 bg-muted-foreground/40 px-1 py-0.5">
            <span className="mr-2 whitespace-nowrap font-mono text-xs text-white">
              BY {cask.creator?.substring(0, 6) + '...'}
            </span>
            <Check className="h-3 w-3 rounded-full bg-blue-500 p-0.5 text-white" />
          </div>

          <div className="flex items-center rounded-md border border-muted-foreground/80 bg-muted-foreground/40 px-1 py-0.5">
            <Diamond className="mr-1.5 h-3 w-3 text-white" />
            <span className="font-mono text-xs text-white">ARBITRUM</span>
          </div>

          <div className="flex items-center rounded-md border border-muted-foreground/80 bg-muted-foreground/40 px-1 py-0.5">
            <Layers className="mr-1.5 h-3 w-3 text-white" />
            <span className="font-mono text-xs text-white">500</span>
          </div>

          <div className="flex items-center rounded-md border border-muted-foreground/80 bg-muted-foreground/40 px-1 py-0.5">
            <span className="font-mono text-xs text-white">M</span>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-3 gap-2 md:grid-cols-4 lg:grid-cols-6">
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">DISTILLERY</p>
            <p className="text-md font-bold">{cask?.distillery}</p>
          </div>
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">REGION</p>
            <p className="text-md font-bold">{cask?.region}</p>
          </div>
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">YEAR</p>
            <p className="text-md font-bold">{dayjs(cask?.targetBottlingDate).format('YYYY')}</p>
          </div>
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">CURRENT AGE</p>
            <p className="text-md font-bold">
              {dayjs(cask?.targetBottlingDate as string).diff(dayjs(), 'year')}
            </p>
          </div>
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">APPROX BOTTLES</p>
            <p className="text-md font-bold">{cask.targetAmount}</p>
          </div>
          <div className="flex flex-col">
            <p className="text-xs text-gray-400">CREATOR ADDRESS</p>
            <p className="text-md font-bold">
              {cask?.creator?.substring(0, 6) +
                '...' +
                cask?.creator?.substring(cask?.creator?.length - 4)}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

const CollectionHeaderSkeleton = () => {
  return (
    <div className="w-full">
      {/* Banner Image Skeleton */}
      <Skeleton className="h-48 w-full md:h-64 lg:h-80" />

      {/* Collection Logo & Title Skeleton */}
      <div className="container mx-auto px-4">
        <div className="relative -mt-16 mb-4 flex flex-col items-start gap-4 md:flex-row md:items-end">
          <Skeleton className="relative z-10 h-32 w-32 rounded-xl" />

          <div className="mt-4 flex-1 md:mt-0">
            <Skeleton className="mb-2 h-8 w-64" />
            <Skeleton className="h-4 w-40" />
          </div>
        </div>

        {/* Collection Stats Skeleton */}
        <div className="grid grid-cols-2 gap-4 border-b border-gray-700 py-6 md:grid-cols-3 lg:grid-cols-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="flex flex-col">
              <Skeleton className="mb-2 h-4 w-20" />
              <Skeleton className="h-6 w-16" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export { CollectionHeaderSkeleton }
export default CollectionHeader

'use client'

import React from 'react'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import { Activity } from '@/hooks/nft-detail/useNftCollection'

// Initialize dayjs plugins
dayjs.extend(relativeTime)

interface ActivityTableProps {
  activities: Activity[]
}

// Component to display event badges with appropriate styling
const EventBadge: React.FC<{ type: string }> = ({ type }) => {
  const getEventColor = () => {
    switch (type) {
      case 'Sale':
        return 'bg-green-500'
      case 'Listing':
        return 'bg-blue-500'
      case 'Offer':
        return 'bg-purple-500'
      case 'Mint':
        return 'bg-yellow-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <span className={`rounded-md px-2 py-1 text-xs font-medium text-white ${getEventColor()}`}>
      {type}
    </span>
  )
}

// Helper function to format timestamps
const formatTimeAgo = (timestamp: number) => {
  return dayjs(timestamp).fromNow()
}

const ActivityTable: React.FC<ActivityTableProps> = ({ activities }) => {
  return (
    <div className="overflow-hidden">
      <div className="grid grid-cols-3 gap-2 border-b border-gray-700 px-2 py-3 text-xs font-medium text-gray-400 md:hidden">
        <div>EVENT</div>
        <div>ITEM</div>
        <div className="text-right">PRICE</div>
      </div>

      {activities.map((activity) => (
        <div
          key={activity.id}
          className="grid cursor-pointer grid-cols-3 gap-2 border-b border-gray-700 px-2 py-3 transition-colors hover:bg-gray-800 md:grid-cols-12 md:gap-4 md:px-4"
        >
          <div className="flex items-center md:col-span-2">
            <EventBadge type={activity.eventType} />
          </div>
          <div className="flex items-center gap-2 md:col-span-5">
            <div className="relative h-8 w-8 overflow-hidden rounded bg-gray-700 md:h-10 md:w-10">
              <img
                src={`https://picsum.photos/seed/${activity.tokenId}/200/200`}
                alt={`PlayerZ #${activity.tokenId}`}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="hidden md:block">
              <p className="font-medium text-white">PlayerZero x Meebits</p>
              <p className="text-sm text-gray-400">#{activity.tokenId}</p>
            </div>
            <div className="md:hidden">
              <p className="text-xs text-gray-400">PlayerZ...</p>
            </div>
          </div>
          <div className="text-right text-sm font-medium md:col-span-2 md:text-base">
            {activity.eventType === 'Sale'
              ? `${activity.price.toFixed(4)} ETH`
              : `${activity.price.toFixed(4)} ETH`}
          </div>
          <div className="hidden text-right text-gray-400 md:col-span-3 md:block">
            {formatTimeAgo(activity.timestamp)}
          </div>
        </div>
      ))}

      <div className="mt-6 flex justify-center md:hidden">
        <button className="rounded-lg border border-gray-700 bg-gray-800 px-4 py-2 text-sm">
          Load more
        </button>
      </div>
    </div>
  )
}

export default ActivityTable

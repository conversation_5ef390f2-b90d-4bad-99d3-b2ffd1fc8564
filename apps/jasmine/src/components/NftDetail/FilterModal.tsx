'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ChevronDown, ChevronUp, X } from 'lucide-react'

interface FilterModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onApplyFilters: (filters: any) => void
}

const FilterModal: React.FC<FilterModalProps> = ({ open, onOpenChange, onApplyFilters }) => {
  // Filter states
  const [statusExpanded, setStatusExpanded] = useState(true)
  const [rarityExpanded, setRarityExpanded] = useState(false)
  const [priceExpanded, setPriceExpanded] = useState(false)
  const [marketplacesExpanded, setMarketplacesExpanded] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'listed'>('all')

  const handleApplyFilters = () => {
    onApplyFilters({
      status: selectedStatus,
      // Add other filter values here
    })
    onOpenChange(false)
  }

  const handleClearAll = () => {
    setSelectedStatus('all')
    // Reset other filters here
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="fixed bottom-0 left-0 right-0 top-auto h-auto max-h-[90vh] translate-x-0 translate-y-0 overflow-auto rounded-b-none rounded-t-xl border-t p-0 shadow-lg sm:mx-auto sm:max-w-md">
        <DialogHeader className="sticky top-0 z-10 flex flex-row items-center justify-between border-b border-border bg-background p-4">
          <DialogTitle className="text-lg font-semibold">Filters</DialogTitle>
          <DialogClose className="h-6 w-6 rounded-full">
            <X className="h-4 w-4" />
          </DialogClose>
        </DialogHeader>

        <div className="p-4">
          {/* Sort dropdown */}
          <div className="mb-4">
            <Select defaultValue="price_low_high">
              <SelectTrigger className="w-full border-muted-foreground bg-muted-foreground/60">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="price_low_high">Price low to high</SelectItem>
                <SelectItem value="price_high_low">Price high to low</SelectItem>
                <SelectItem value="recently_listed">Recently listed</SelectItem>
                <SelectItem value="recently_created">Recently created</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status filter */}
          <div className="border-b border-gray-200 pb-4 dark:border-gray-800">
            <div
              className="flex cursor-pointer items-center justify-between py-2"
              onClick={() => setStatusExpanded(!statusExpanded)}
            >
              <span className="font-medium">Status</span>
              {statusExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>

            {statusExpanded && (
              <div className="mt-2 flex gap-2">
                <button
                  className={`rounded-md px-4 py-2 text-sm ${selectedStatus === 'all' ? 'bg-gray-200 font-medium dark:bg-muted-foreground' : 'border'} dark:border-background`}
                  onClick={() => setSelectedStatus('all')}
                >
                  All
                </button>
                <button
                  className={`rounded-md px-4 py-2 text-sm ${selectedStatus === 'listed' ? 'bg-gray-200 font-medium dark:bg-muted-foreground' : 'border'} dark:border-background`}
                  onClick={() => setSelectedStatus('listed')}
                >
                  Listed
                </button>
              </div>
            )}
          </div>

          {/* Rarity filter */}
          <div className="border-b border-gray-200 py-4 dark:border-gray-800">
            <div
              className="flex cursor-pointer items-center justify-between py-2"
              onClick={() => setRarityExpanded(!rarityExpanded)}
            >
              <span className="font-medium">Rarity</span>
              {rarityExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>

            {rarityExpanded && (
              <div className="mt-2">
                {/* Rarity filter content would go here */}
                <p className="text-sm text-gray-500">Rarity options</p>
              </div>
            )}
          </div>

          {/* Price filter */}
          <div className="border-b border-gray-200 py-4 dark:border-gray-800">
            <div
              className="flex cursor-pointer items-center justify-between py-2"
              onClick={() => setPriceExpanded(!priceExpanded)}
            >
              <span className="font-medium">Price</span>
              {priceExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>

            {priceExpanded && (
              <div className="mt-2">
                {/* Price filter content would go here */}
                <p className="text-sm text-gray-500">Price range options</p>
              </div>
            )}
          </div>

          {/* Marketplaces filter */}
          <div className="border-b border-gray-200 py-4 dark:border-gray-800">
            <div
              className="flex cursor-pointer items-center justify-between py-2"
              onClick={() => setMarketplacesExpanded(!marketplacesExpanded)}
            >
              <span className="font-medium">Marketplaces</span>
              {marketplacesExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>

            {marketplacesExpanded && (
              <div className="mt-2">
                {/* Marketplaces filter content would go here */}
                <p className="text-sm text-gray-500">Marketplace options</p>
              </div>
            )}
          </div>

          {/* Traits section */}
          <div className="py-4">
            <h3 className="mb-2 font-medium">Traits</h3>

            <div className="border-t border-gray-200 py-2 dark:border-gray-800">
              <div className="flex items-center justify-between">
                <span className="text-sm">Type</span>
                <span className="rounded-full bg-gray-100 px-2 py-1 text-sm dark:bg-gray-800">
                  1
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="sticky bottom-0 flex justify-between border-t border-gray-200 bg-white p-4 dark:border-background dark:bg-popover">
          <Button
            variant="outline"
            className="mr-2 flex-1 bg-white dark:bg-muted-foreground"
            onClick={handleClearAll}
          >
            Clear all
          </Button>
          <Button
            className="ml-2 flex-1 bg-primary text-white hover:bg-primary/80"
            onClick={handleApplyFilters}
          >
            Done
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default FilterModal

'use client'

import React, { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'

interface FilterSidebarProps {
  onFilterChange?: (filters: any) => void
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({ onFilterChange }) => {
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000])
  const [minPrice, setMinPrice] = useState('0')
  const [maxPrice, setMaxPrice] = useState('10000')
  const [onlyOnSale, setOnlyOnSale] = useState(false)

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange([values[0], values[1]])
    setMinPrice(values[0].toString())
    setMaxPrice(values[1].toString())
    
    if (onFilterChange) {
      onFilterChange({
        priceRange: [values[0], values[1]],
        onlyOnSale
      })
    }
  }

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setMinPrice(value)
    if (value === '' || isNaN(Number(value))) return
    setPriceRange([Number(value), priceRange[1]])
  }

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setMaxPrice(value)
    if (value === '' || isNaN(Number(value))) return
    setPriceRange([priceRange[0], Number(value)])
  }

  const handleReset = () => {
    setPriceRange([0, 10000])
    setMinPrice('0')
    setMaxPrice('10000')
    setOnlyOnSale(false)
  }

  return (
    <div className="pr-4">
      <div className="sticky top-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-lg">Filter</h3>
          <Button variant="ghost" size="sm" onClick={handleReset} className="text-blue-500 hover:text-blue-600 p-0 h-auto">
            Clear All
          </Button>
        </div>

        {/* Status Filter */}
        <Accordion type="multiple" className="w-full border-b border-gray-700 pb-4 mb-4">
          <AccordionItem value="status" className="border-none">
            <AccordionTrigger className="py-2 hover:no-underline">
              <span className="font-medium">Status</span>
            </AccordionTrigger>
            <AccordionContent>
              <div className="flex items-center justify-between">
                <span className="text-sm">Buy Now</span>
                <Switch 
                  checked={onlyOnSale} 
                  onCheckedChange={(checked) => {
                    setOnlyOnSale(checked)
                    if (onFilterChange) {
                      onFilterChange({
                        priceRange,
                        onlyOnSale: checked
                      })
                    }
                  }} 
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Price Filter */}
        <Accordion type="multiple" className="w-full border-b border-gray-700 pb-4 mb-4">
          <AccordionItem value="price" className="border-none">
            <AccordionTrigger className="py-2 hover:no-underline">
              <span className="font-medium">Price</span>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <div>
                  <Slider
                    defaultValue={[0, 10000]}
                    value={priceRange}
                    min={0}
                    max={10000}
                    step={100}
                    onValueChange={handlePriceRangeChange}
                    className="my-4"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <label className="text-xs text-gray-400 mb-1 block">Min</label>
                    <div className="flex items-center bg-gray-800 rounded border border-gray-700 px-2">
                      <input
                        type="text"
                        value={minPrice}
                        onChange={handleMinPriceChange}
                        className="w-full bg-transparent py-1 text-sm focus:outline-none"
                      />
                      <span className="text-xs text-gray-400">ETH</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <label className="text-xs text-gray-400 mb-1 block">Max</label>
                    <div className="flex items-center bg-gray-800 rounded border border-gray-700 px-2">
                      <input
                        type="text"
                        value={maxPrice}
                        onChange={handleMaxPriceChange}
                        className="w-full bg-transparent py-1 text-sm focus:outline-none"
                      />
                      <span className="text-xs text-gray-400">ETH</span>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Properties Filter */}
        <Accordion type="multiple" className="w-full border-b border-gray-700 pb-4 mb-4">
          <AccordionItem value="properties" className="border-none">
            <AccordionTrigger className="py-2 hover:no-underline">
              <span className="font-medium">Properties</span>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Age</span>
                  <div className="flex items-center gap-1">
                    <span className="text-xs bg-gray-800 px-2 py-1 rounded-full">12</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Region</span>
                  <div className="flex items-center gap-1">
                    <span className="text-xs bg-gray-800 px-2 py-1 rounded-full">8</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Distillery</span>
                  <div className="flex items-center gap-1">
                    <span className="text-xs bg-gray-800 px-2 py-1 rounded-full">5</span>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  )
}

export default FilterSidebar

'use client'
import { GET_MY_CASKS } from '@/graphql/queries/casks'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import type { getCasks, getCasks_casks, getCasksVariables } from 'schemaTypes'
import { useQuery } from 'urql'
import EmblaCarousel from '../Carousel/EmblaCarousel'
import FeatureCaskCard from '../FeatureCaskCard'

const FeatureCaskCarousel = () => {
  const router = useRouter()
  const [featureCasks, setFeatureCasks] = useState<getCasks_casks[]>([])
  const [result] = useQuery<getCasks, getCasksVariables>({
    query: GET_MY_CASKS,
  })

  useEffect(() => {
    if (result?.data?.casks) {
      setFeatureCasks(result.data.casks)
    }
  }, [result.data])
  return (
    <>
      {featureCasks.length === 0 && (
        <div className="my-4 rounded-lg bg-gray-50 p-10 text-center text-gray-500">
          <p>No feature casks available at the moment.</p>
        </div>
      )}
      {featureCasks.length > 0 && (
        <EmblaCarousel
          slides={featureCasks.map((cask, index) => (
            <div
              key={index}
              className="w-full"
            >
              <FeatureCaskCard
                data={cask}
                onButtonClick={() => router.push(`/cask-collection?projectId=${cask.projectId}&type=regular`)}
              />
            </div>
          ))}
          showArrowButton={false}
          slideSize="basis-full"
        />
      )}
    </>
  )
}

export default FeatureCaskCarousel

import React from 'react'
import { useRouter } from 'next/navigation'
import { getWholeCasks_wholeCasks } from '@/../schemaTypes'
import { CaskCard } from '@/components/CaskCard'

interface Props {
  data: getWholeCasks_wholeCasks
  onButtonClick?: () => void
}

const WholeCaskCard = ({ data, onButtonClick }: Props) => {
  const router = useRouter()

  // Render price display for whole cask card
  const renderPrice = (caskData: getWholeCasks_wholeCasks) => {
    return (
      <>
        <p className="transform text-xs text-muted-foreground">
          Whole Cask NFT
        </p>
        <p className="transform text-lg font-semibold text-white">
          {caskData.lastPrice} USD
        </p>
      </>
    )
  }

  // Handle button click to navigate to NFT detail page
  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick()
    } else {
      router.push(`/nft-detail?projectId=${data.projectId}&type=whole`)
    }
  }

  return (
    <CaskCard
      data={data}
      cardType="Whole Cask NFT"
      renderPrice={renderPrice}
      onButtonClick={handleButtonClick}
    />
  )
}

export default WholeCaskCard


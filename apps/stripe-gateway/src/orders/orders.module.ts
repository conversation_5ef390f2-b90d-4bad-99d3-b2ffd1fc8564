import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { PaymentOrderSchema, PaymentOrderSchemaFactory } from '../schemas/payment-order.schema';
import { NFTSaleOrderSchema, NFTSaleOrderSchemaFactory } from '../schemas/nft-sale-order.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PaymentOrderSchema.name, schema: PaymentOrderSchemaFactory },
      { name: NFTSaleOrderSchema.name, schema: NFTSaleOrderSchemaFactory },
    ]),
  ],
  controllers: [OrdersController],
  providers: [OrdersService],
  exports: [OrdersService],
})
export class OrdersModule {}

import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import {
  PaymentOrderSchema,
  PaymentOrderDocument,
} from '../schemas/payment-order.schema';
import {
  NFTSaleOrderSchema,
  NFTSaleOrderDocument,
} from '../schemas/nft-sale-order.schema';
import { CreatePaymentOrderDto } from '../common/dto/create-payment-order.dto';
import { UpdatePaymentOrderDto } from '../common/dto/update-payment-order.dto';
import { PaymentStatus } from '../common/enums/payment-status.enum';
import { TransferStatus } from '../common/enums/transfer-status.enum';
import { ProductType } from '../common/enums/product-type.enum';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    @InjectModel(PaymentOrderSchema.name)
    private paymentOrderModel: Model<PaymentOrderDocument>,
    @InjectModel(NFTSaleOrderSchema.name)
    private nftSaleOrderModel: Model<NFTSaleOrderDocument>,
  ) {}

  async createPaymentOrder(
    createPaymentOrderDto: CreatePaymentOrderDto,
  ): Promise<PaymentOrderDocument> {
    this.logger.log('Creating payment order', {
      userId: createPaymentOrderDto.userId,
      productType: createPaymentOrderDto.productType,
    });

    const paymentOrderId = uuidv4();
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now

    const paymentOrder = new this.paymentOrderModel({
      paymentOrderId,
      ...createPaymentOrderDto,
      paymentStatus: PaymentStatus.CREATED,
      paymentRetryCount: 0,
      expiresAt,
    });

    const savedOrder = await paymentOrder.save();
    this.logger.log('Payment order created successfully', { paymentOrderId });

    return savedOrder;
  }

  async findPaymentOrderById(
    paymentOrderId: string,
  ): Promise<PaymentOrderDocument> {
    const order = await this.paymentOrderModel
      .findOne({ paymentOrderId })
      .exec();
    if (!order) {
      throw new NotFoundException(
        `Payment order with ID ${paymentOrderId} not found`,
      );
    }
    return order;
  }

  async findPaymentOrderByStripeSessionId(
    stripeSessionId: string,
  ): Promise<PaymentOrderDocument> {
    const order = await this.paymentOrderModel
      .findOne({ stripeSessionId })
      .exec();
    if (!order) {
      throw new NotFoundException(
        `Payment order with Stripe session ID ${stripeSessionId} not found`,
      );
    }
    return order;
  }

  async updatePaymentOrder(
    paymentOrderId: string,
    updatePaymentOrderDto: UpdatePaymentOrderDto,
  ): Promise<PaymentOrderDocument> {
    this.logger.log('Updating payment order', {
      paymentOrderId,
      updates: updatePaymentOrderDto,
    });

    const order = await this.paymentOrderModel
      .findOneAndUpdate(
        { paymentOrderId },
        {
          ...updatePaymentOrderDto,
          updatedAt: new Date(),
          ...(updatePaymentOrderDto.paymentStatus ===
            PaymentStatus.PAYMENT_SUCCEEDED && {
            paymentCompletedAt: new Date(),
          }),
        },
        { new: true },
      )
      .exec();

    if (!order) {
      throw new NotFoundException(
        `Payment order with ID ${paymentOrderId} not found`,
      );
    }

    this.logger.log('Payment order updated successfully', {
      paymentOrderId,
      newStatus: order.paymentStatus,
    });
    return order;
  }

  async findUserPaymentOrders(
    userId: string,
    limit: number = 10,
    skip: number = 0,
  ): Promise<PaymentOrderDocument[]> {
    return this.paymentOrderModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip)
      .exec();
  }

  async createNFTSaleOrder(
    paymentOrderId: string,
    userWalletAddress: string,
  ): Promise<NFTSaleOrderDocument> {
    this.logger.log('Creating NFT sale order', {
      paymentOrderId,
      userWalletAddress,
    });

    // Verify payment order exists and is paid
    const paymentOrder = await this.findPaymentOrderById(paymentOrderId);
    if (paymentOrder.paymentStatus !== PaymentStatus.PAYMENT_SUCCEEDED) {
      throw new BadRequestException(
        'Payment order must be in PAYMENT_SUCCEEDED status to create NFT sale order',
      );
    }

    if (paymentOrder.productType !== ProductType.NFT) {
      throw new BadRequestException(
        'Payment order must be for NFT product type',
      );
    }

    const nftOrderId = uuidv4();
    const nftSaleOrder = new this.nftSaleOrderModel({
      nftOrderId,
      paymentOrderId,
      userWalletAddress,
      transferStatus: TransferStatus.TRANSFER_PENDING,
      transferRetryCount: 0,
      blockchainConfirmations: 0,
    });

    const savedOrder = await nftSaleOrder.save();
    this.logger.log('NFT sale order created successfully', { nftOrderId });

    return savedOrder;
  }

  async findNFTSaleOrderByPaymentOrderId(
    paymentOrderId: string,
  ): Promise<NFTSaleOrderDocument> {
    const order = await this.nftSaleOrderModel
      .findOne({ paymentOrderId })
      .exec();
    if (!order) {
      throw new NotFoundException(
        `NFT sale order for payment order ${paymentOrderId} not found`,
      );
    }
    return order;
  }

  private validateStatusTransition(
    currentStatus: PaymentStatus,
    newStatus: PaymentStatus,
  ): boolean {
    // Define valid status transitions
    const validTransitions: Record<PaymentStatus, PaymentStatus[]> = {
      [PaymentStatus.CREATED]: [
        PaymentStatus.SESSION_CREATED,
        PaymentStatus.PAYMENT_ERROR,
      ],
      [PaymentStatus.SESSION_CREATED]: [
        PaymentStatus.PAYMENT_PENDING,
        PaymentStatus.PAYMENT_EXPIRED,
      ],
      [PaymentStatus.PAYMENT_PENDING]: [
        PaymentStatus.PAYMENT_PROCESSING,
        PaymentStatus.PAYMENT_CANCELLED,
      ],
      [PaymentStatus.PAYMENT_PROCESSING]: [
        PaymentStatus.PAYMENT_SUCCEEDED,
        PaymentStatus.PAYMENT_FAILED,
        PaymentStatus.PAYMENT_ERROR,
      ],
      [PaymentStatus.PAYMENT_SUCCEEDED]: [], // Terminal state for payment
      [PaymentStatus.PAYMENT_FAILED]: [], // Terminal state
      [PaymentStatus.PAYMENT_CANCELLED]: [], // Terminal state
      [PaymentStatus.PAYMENT_EXPIRED]: [], // Terminal state
      [PaymentStatus.PAYMENT_ERROR]: [
        PaymentStatus.PAYMENT_MANUAL_REVIEW,
        PaymentStatus.REFUND_PENDING,
      ],
      [PaymentStatus.PAYMENT_MANUAL_REVIEW]: [
        PaymentStatus.REFUND_PENDING,
        PaymentStatus.PAYMENT_SUCCEEDED,
      ],
      [PaymentStatus.REFUND_PENDING]: [PaymentStatus.REFUND_COMPLETED],
      [PaymentStatus.REFUND_COMPLETED]: [], // Terminal state
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }
}

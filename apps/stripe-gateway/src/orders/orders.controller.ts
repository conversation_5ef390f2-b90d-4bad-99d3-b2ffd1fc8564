import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Put,
  Logger,
  ValidationPipe,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreatePaymentOrderDto } from '../common/dto/create-payment-order.dto';
import { UpdatePaymentOrderDto } from '../common/dto/update-payment-order.dto';

@Controller('orders')
export class OrdersController {
  private readonly logger = new Logger(OrdersController.name);

  constructor(private readonly ordersService: OrdersService) {}

  @Post('payment')
  async createPaymentOrder(@Body(ValidationPipe) createPaymentOrderDto: CreatePaymentOrderDto) {
    this.logger.log('Creating payment order', { userId: createPaymentOrderDto.userId });
    return this.ordersService.createPaymentOrder(createPaymentOrderDto);
  }

  @Get('payment/:paymentOrderId')
  async getPaymentOrder(@Param('paymentOrderId') paymentOrderId: string) {
    this.logger.log('Getting payment order', { paymentOrderId });
    return this.ordersService.findPaymentOrderById(paymentOrderId);
  }

  @Put('payment/:paymentOrderId')
  async updatePaymentOrder(
    @Param('paymentOrderId') paymentOrderId: string,
    @Body(ValidationPipe) updatePaymentOrderDto: UpdatePaymentOrderDto,
  ) {
    this.logger.log('Updating payment order', { paymentOrderId });
    return this.ordersService.updatePaymentOrder(paymentOrderId, updatePaymentOrderDto);
  }

  @Get('payment/user/:userId')
  async getUserPaymentOrders(
    @Param('userId') userId: string,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('skip', new DefaultValuePipe(0), ParseIntPipe) skip: number,
  ) {
    this.logger.log('Getting user payment orders', { userId, limit, skip });
    return this.ordersService.findUserPaymentOrders(userId, limit, skip);
  }

  @Get('nft/payment/:paymentOrderId')
  async getNFTSaleOrderByPaymentId(@Param('paymentOrderId') paymentOrderId: string) {
    this.logger.log('Getting NFT sale order by payment ID', { paymentOrderId });
    return this.ordersService.findNFTSaleOrderByPaymentOrderId(paymentOrderId);
  }
}

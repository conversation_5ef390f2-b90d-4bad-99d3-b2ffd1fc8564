import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(private configService: ConfigService) {
    const stripeSecretKey = this.configService.get<string>('stripe.secretKey');
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    this.stripe = new Stripe(stripeSecretKey);
  }

  async createPaymentIntent(amount: number, currency = 'usd', metadata?: any) {
    const paymentIntent = await this.stripe.paymentIntents.create({
      amount,
      currency,
      metadata,
    });

    return {
      clientSecret: paymentIntent.client_secret,
    };
  }
  // stripe.service.ts
  async createCheckoutSession(): Promise<{ url: string }> {
    const session = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'payment',
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: '1 x Resident Evil Editions - Single Malt Whisky',
              description:
                'Resident Evil Editions - Single Malt Whisky from DCasks and Capcom',
              images: [
                'https://d1wqzb5bdbcre6.cloudfront.net/37e5e2b2528084e0f923c26faf10c8b018f01134c1c42551d9e13dae6186440a/68747470733a2f2f66696c65732e7374726970652e636f6d2f6c696e6b732f4d44423859574e6a64463878556c67356548525154307849647a4e495246424466475a735833526c63335266566b6474533352425a47524f655468574e6d70475a32784b5655524d656a64703030676c696976434942/6d65726368616e745f69643d616363745f315258397874504f4c4877334844504326636c69656e743d5041594d454e545f50414745',
              ],
            },
            unit_amount: 5000,
          },
          quantity: 1,
        },
      ],
      success_url: 'http://localhost:3000',
      cancel_url: 'http://localhost:3000',

      // 👇 Metadata you want to attach
      metadata: {
        orderId: 'order_12345',
        userId: 'user_98765',
        customNote: 'Thank you!',
      },
    });

    if (!session.url) {
      throw new InternalServerErrorException(
        'Failed to create checkout session',
      );
    }

    return { url: session.url };
  }

  async getPaymentByOrderId(orderId: string) {
    const paymentIntent = await this.stripe.paymentIntents.search({
      query: `metadata['orderId']:'${orderId}'`,
    });
    return paymentIntent;
  }
}

import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { CreateCheckoutSessionDto } from '../common/dto/create-checkout-session.dto';
import { PaymentStatus } from '../common/enums/payment-status.enum';
import { ProductType } from '../common/enums/product-type.enum';
import { OrdersService } from '../orders/orders.service';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private stripe: Stripe;

  constructor(
    private configService: ConfigService,
    private ordersService: OrdersService,
  ) {
    const stripeSecretKey = this.configService.get<string>('stripe.secretKey');
    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }
    this.stripe = new Stripe(stripeSecretKey);
    this.logger.log('Stripe service initialized');
  }

  async createCheckoutSession(
    createCheckoutSessionDto: CreateCheckoutSessionDto,
    paymentOrderId: string,
  ): Promise<{ sessionId: string; url: string }> {
    this.logger.log('Creating Stripe checkout session', {
      paymentOrderId,
      userId: createCheckoutSessionDto.userId,
      amount: createCheckoutSessionDto.amount,
    });

    try {
      const session = await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'payment',
        line_items: [
          {
            price_data: {
              currency: createCheckoutSessionDto.currency,
              product_data: {
                name: createCheckoutSessionDto.productName,
                description: createCheckoutSessionDto.productDescription || '',
                images: createCheckoutSessionDto.productImage
                  ? [createCheckoutSessionDto.productImage]
                  : [],
              },
              unit_amount: Math.round(createCheckoutSessionDto.amount * 100), // Convert to cents
            },
            quantity: 1,
          },
        ],
        success_url:
          createCheckoutSessionDto.successUrl ||
          `${this.getBaseUrl()}/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url:
          createCheckoutSessionDto.cancelUrl || `${this.getBaseUrl()}/cancel`,
        metadata: {
          paymentOrderId,
          userId: createCheckoutSessionDto.userId,
          productType: createCheckoutSessionDto.productType,
          productId: createCheckoutSessionDto.productId,
          ...createCheckoutSessionDto.metadata,
        },
        expires_at: Math.floor(Date.now() / 1000) + 30 * 60, // 30 minutes from now
      });

      if (!session.url) {
        throw new InternalServerErrorException(
          'Failed to create checkout session - no URL returned',
        );
      }

      this.logger.log('Stripe checkout session created successfully', {
        sessionId: session.id,
        paymentOrderId,
      });

      return {
        sessionId: session.id,
        url: session.url,
      };
    } catch (error) {
      this.logger.error(
        'Failed to create Stripe checkout session',
        error instanceof Error ? error.stack : String(error),
        { paymentOrderId },
      );
      throw new InternalServerErrorException(
        `Failed to create checkout session: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  private getBaseUrl(): string {
    // This should be configurable based on environment
    return (
      this.configService.get<string>('app.baseUrl') || 'http://localhost:3000'
    );
  }

  async getCheckoutSession(
    sessionId: string,
  ): Promise<Stripe.Checkout.Session> {
    try {
      const session = await this.stripe.checkout.sessions.retrieve(sessionId);
      return session;
    } catch (error) {
      this.logger.error(
        'Failed to retrieve checkout session',
        error instanceof Error ? error.stack : String(error),
        {
          sessionId,
        },
      );
      throw new InternalServerErrorException(
        `Failed to retrieve checkout session: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  constructWebhookEvent(
    payload: string | Buffer,
    signature?: string,
  ): Stripe.Event {
    const webhookSecret = this.configService.get<string>(
      'stripe.webhookSecret',
    );
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
    }

    if (!signature) {
      this.logger.error('No signature provided for webhook event');
      throw new BadRequestException('No signature provided');
    }

    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
      return event;
    } catch (error) {
      this.logger.error(
        'Webhook signature verification failed',
        error instanceof Error ? error.stack : String(error),
      );
      throw new BadRequestException(`Webhook signature verification failed`);
    }
  }

  /**
   * Process webhook event and handle business logic
   */
  async processWebhookEvent(
    event: Stripe.Event,
  ): Promise<{ received: boolean }> {
    this.logger.log('Processing webhook event', {
      eventType: event.type,
      eventId: event.id,
    });

    try {
      // Handle different event types
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object);
          break;

        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object);
          break;

        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object);
          break;

        case 'checkout.session.expired':
          await this.handleCheckoutSessionExpired(event.data.object);
          break;

        default:
          this.logger.log(`Unhandled event type: ${event.type}`);
      }

      this.logger.log('Webhook event processed successfully', {
        eventType: event.type,
        eventId: event.id,
      });

      return { received: true };
    } catch (error) {
      this.logger.error('Error processing webhook event', {
        stack: error instanceof Error ? error.stack : String(error),
        eventType: event.type,
        eventId: event.id,
      });
      throw new InternalServerErrorException('Failed to process webhook event');
    }
  }

  private async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session,
  ): Promise<void> {
    this.logger.log('Handling checkout session completed', {
      sessionId: session.id,
    });

    const paymentOrderId = session.metadata?.paymentOrderId;
    if (!paymentOrderId) {
      this.logger.warn('No paymentOrderId found in session metadata', {
        sessionId: session.id,
      });
      return;
    }

    // Update payment order status
    await this.ordersService.updatePaymentOrder(paymentOrderId, {
      paymentStatus: PaymentStatus.PAYMENT_PENDING,
      stripePaymentIntentId: session.payment_intent as string,
    });

    this.logger.log('Checkout session completed processed', {
      sessionId: session.id,
      paymentOrderId,
    });
  }

  private async handlePaymentIntentSucceeded(
    paymentIntent: Stripe.PaymentIntent,
  ): Promise<void> {
    this.logger.log('Handling payment intent succeeded', {
      paymentIntentId: paymentIntent.id,
    });

    const paymentOrderId = paymentIntent.metadata?.paymentOrderId;
    if (!paymentOrderId) {
      this.logger.warn('No paymentOrderId found in payment intent metadata', {
        paymentIntentId: paymentIntent.id,
      });
      return;
    }

    // Update payment order status
    await this.ordersService.updatePaymentOrder(paymentOrderId, {
      paymentStatus: PaymentStatus.PAYMENT_SUCCEEDED,
      stripeChargeId: paymentIntent.latest_charge as string,
    });

    // Get the payment order to check if it's an NFT order
    const paymentOrder =
      await this.ordersService.findPaymentOrderById(paymentOrderId);

    if (paymentOrder.productType === ProductType.NFT) {
      // Create NFT sale order for blockchain processing
      const userWalletAddress = paymentOrder.metadata?.userWalletAddress;
      if (!userWalletAddress) {
        throw new BadRequestException(
          'No userWalletAddress found for NFT order',
        );
      }

      await this.ordersService.createNFTSaleOrder(
        paymentOrderId,
        userWalletAddress,
      );
      this.logger.log('NFT sale order created for successful payment', {
        paymentOrderId,
      });
    }

    this.logger.log('Payment intent succeeded processed', {
      paymentIntentId: paymentIntent.id,
      paymentOrderId,
    });
  }

  private async handlePaymentIntentFailed(
    paymentIntent: Stripe.PaymentIntent,
  ): Promise<void> {
    this.logger.log('Handling payment intent failed', {
      paymentIntentId: paymentIntent.id,
    });

    const paymentOrderId = paymentIntent.metadata?.paymentOrderId;
    if (!paymentOrderId) {
      this.logger.warn('No paymentOrderId found in payment intent metadata', {
        paymentIntentId: paymentIntent.id,
      });
      return;
    }

    // Update payment order status
    await this.ordersService.updatePaymentOrder(paymentOrderId, {
      paymentStatus: PaymentStatus.PAYMENT_FAILED,
      paymentErrorDetails: {
        stripeError: paymentIntent.last_payment_error,
        failureCode: paymentIntent.last_payment_error?.code,
        failureMessage: paymentIntent.last_payment_error?.message,
      },
    });

    this.logger.log('Payment intent failed processed', {
      paymentIntentId: paymentIntent.id,
      paymentOrderId,
    });
  }

  private async handleCheckoutSessionExpired(
    session: Stripe.Checkout.Session,
  ): Promise<void> {
    this.logger.log('Handling checkout session expired', {
      sessionId: session.id,
    });

    const paymentOrderId = session.metadata?.paymentOrderId;
    if (!paymentOrderId) {
      this.logger.warn('No paymentOrderId found in session metadata', {
        sessionId: session.id,
      });
      return;
    }

    // Update payment order status
    await this.ordersService.updatePaymentOrder(paymentOrderId, {
      paymentStatus: PaymentStatus.PAYMENT_EXPIRED,
    });

    this.logger.log('Checkout session expired processed', {
      sessionId: session.id,
      paymentOrderId,
    });
  }
}

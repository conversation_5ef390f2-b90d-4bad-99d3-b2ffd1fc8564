import {
  Controller,
  Post,
  Body,
  Logger,
  Res,
  Get,
  Query,
} from '@nestjs/common';
import { StripeService } from './stripe.service';
import { Response } from 'express';

@Controller('stripe')
export class StripeController {
  private readonly logger = new Logger(StripeController.name);

  constructor(private readonly stripeService: StripeService) {}

  @Post('create-payment-intent')
  async createPaymentIntent(@Body() body: { amount: number; orderId: string }) {
    this.logger.log('start create PaymentIntent', body);
    const { amount } = body;

    // Convert amount to smallest currency unit (e.g., cents)
    const finalAmount = Math.round(amount);

    this.logger.log('create PaymentIntent finalAmount', finalAmount, {
      orderId: body.orderId,
    });
    const intent = await this.stripeService.createPaymentIntent(
      finalAmount,
      'sgd',
      { orderId: body.orderId },
    );

    this.logger.log('create PaymentIntent done', intent);
    return intent;
  }

  @Get('create-checkout-session')
  async createCheckout(@Res() res: Response) {
    const session = await this.stripeService.createCheckoutSession();
    res.redirect(session.url); // Redirect to Stripe-hosted page
  }

  @Get('get-payment-by-order-id')
  async getPaymentByOrderId(@Query() queries: { orderId: string }) {
    const paymentIntent = await this.stripeService.getPaymentByOrderId(
      queries.orderId,
    );
    return paymentIntent;
  }
}

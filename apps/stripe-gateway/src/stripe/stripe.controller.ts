import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { CreateCheckoutSessionDto } from '../common/dto/create-checkout-session.dto';
import { OrdersService } from '../orders/orders.service';
import { StripeService } from './stripe.service';

@Controller('stripe')
export class StripeController {
  private readonly logger = new Logger(StripeController.name);

  constructor(
    private readonly stripeService: StripeService,
    private readonly ordersService: OrdersService,
  ) {}

  @Post('create-checkout-session')
  async createCheckoutSession(
    @Body(ValidationPipe) createCheckoutSessionDto: CreateCheckoutSessionDto,
  ) {
    this.logger.log('Creating checkout session', {
      userId: createCheckoutSessionDto.userId,
      productType: createCheckoutSessionDto.productType,
    });

    // First create a payment order
    const paymentOrder = await this.ordersService.createPaymentOrder({
      userId: createCheckoutSessionDto.userId,
      productType: createCheckoutSessionDto.productType,
      productId: createCheckoutSessionDto.productId,
      amount: createCheckoutSessionDto.amount,
      currency: createCheckoutSessionDto.currency,
      productMetadata: createCheckoutSessionDto.productMetadata,
      metadata: createCheckoutSessionDto.metadata,
    });

    // Then create the Stripe checkout session
    const session = await this.stripeService.createCheckoutSession(
      createCheckoutSessionDto,
      paymentOrder.paymentOrderId,
    );

    // Update the payment order with the session ID
    await this.ordersService.updatePaymentOrder(paymentOrder.paymentOrderId, {
      stripeSessionId: session.sessionId,
      paymentStatus: 'SESSION_CREATED' as any,
    });

    this.logger.log('Checkout session created successfully', {
      paymentOrderId: paymentOrder.paymentOrderId,
      sessionId: session.sessionId,
    });

    return {
      paymentOrderId: paymentOrder.paymentOrderId,
      sessionId: session.sessionId,
      url: session.url,
    };
  }

  @Get('session/:sessionId')
  async getCheckoutSession(@Param('sessionId') sessionId: string) {
    this.logger.log('Getting checkout session', { sessionId });
    return this.stripeService.getCheckoutSession(sessionId);
  }
}

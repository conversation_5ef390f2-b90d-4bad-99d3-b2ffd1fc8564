import {
  <PERSON>,
  <PERSON>,
  Req,
  <PERSON><PERSON>,
  Headers,
  HttpStatus,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import Stripe from 'stripe';

@Controller('webhooks/stripe')
export class StripeWebhookController {
  constructor(private configService: ConfigService) {}
  @Post()
  async handleStripeWebhook(
    @Req() req: Request,
    @Res() res: Response,
    @Headers('stripe-signature') signature: string,
  ) {
    const stripeSecretKey = this.configService.get<string>('stripe.secretKey');
    const webhookSecret = this.configService.get<string>('stripe.webhookSecret');

    const stripe = new Stripe(stripeSecretKey || '');
    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(
        req['rawBody'], // Important: must be raw body
        signature,
        webhookSecret || '',
      );
    } catch (err) {
      console.error('Webhook signature verification failed.', err.message);
      return res
        .status(HttpStatus.BAD_REQUEST)
        .send(`Webhook Error: ${err.message}`);
    }

    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('💰 Payment succeeded:', paymentIntent.id);
        // TODO: Fulfill order, update DB, etc.
        break;

      case 'payment_intent.payment_failed':
        const failedIntent = event.data.object as Stripe.PaymentIntent;
        console.warn('❌ Payment failed:', failedIntent.id);
        // TODO: Notify user, update status, etc.
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(HttpStatus.OK).send({ received: true });
  }
}

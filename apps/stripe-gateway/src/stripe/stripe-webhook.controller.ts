import { <PERSON>, <PERSON><PERSON>, Logger, Post, Req } from '@nestjs/common';
import { Request } from 'express';
import { StripeService } from './stripe.service';

@Controller('webhooks/stripe')
export class StripeWebhookController {
  private readonly logger = new Logger(StripeWebhookController.name);

  constructor(private readonly stripeService: StripeService) {}
  @Post()
  async handleStripeWebhook(
    @Req() req: Request,
    @Headers('stripe-signature') signature?: string,
  ): Promise<{ received: boolean }> {
    this.logger.log('Received Stripe webhook', { eventType: 'unknown' });

    const event = this.stripeService.constructWebhookEvent(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      req['rawBody'], // Important: must be raw body
      signature,
    );

    this.logger.log('Processing webhook event', {
      eventType: event.type,
      eventId: event.id,
    });

    // Delegate business logic to StripeService
    return await this.stripeService.processWebhookEvent(event);
  }
}

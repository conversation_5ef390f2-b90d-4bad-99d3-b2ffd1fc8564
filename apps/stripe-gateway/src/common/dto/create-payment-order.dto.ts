import { Is<PERSON>num, IsNotEmpty, IsNumber, IsO<PERSON>al, IsString, IsObject, Min } from 'class-validator';
import { ProductType } from '../enums/product-type.enum';

export class CreatePaymentOrderDto {
  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsEnum(ProductType)
  productType: ProductType;

  @IsNotEmpty()
  @IsString()
  productId: string;

  @IsNumber()
  @Min(0.01)
  amount: number;

  @IsOptional()
  @IsString()
  currency?: string = 'usd';

  @IsOptional()
  @IsObject()
  productMetadata?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

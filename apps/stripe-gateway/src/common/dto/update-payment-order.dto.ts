import { IsEnum, IsOptional, IsString, IsObject } from 'class-validator';
import { PaymentStatus } from '../enums/payment-status.enum';

export class UpdatePaymentOrderDto {
  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @IsOptional()
  @IsString()
  stripeSessionId?: string;

  @IsOptional()
  @IsString()
  stripePaymentIntentId?: string;

  @IsOptional()
  @IsString()
  stripeChargeId?: string;

  @IsOptional()
  @IsObject()
  paymentErrorDetails?: Record<string, any>;

  @IsOptional()
  @IsString()
  adminNotes?: string;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

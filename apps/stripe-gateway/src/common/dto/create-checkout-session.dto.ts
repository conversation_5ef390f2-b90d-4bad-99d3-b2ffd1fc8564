import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
import { ProductType } from '../enums/product-type.enum';

export class CreateCheckoutSessionDto {
  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsEnum(ProductType)
  productType: ProductType;

  @IsNotEmpty()
  @IsString()
  productId: string;

  @IsNumber()
  @Min(0.01)
  amount: number; // (1.23 = $1.23)

  @IsOptional()
  @IsString({})
  currency: string = 'usd';

  @IsNotEmpty()
  @IsString()
  productName: string;

  @IsOptional()
  @IsString()
  productDescription?: string;

  @IsOptional()
  @IsString()
  productImage?: string;

  @IsOptional()
  @IsUrl()
  successUrl?: string;

  @IsOptional()
  @IsUrl()
  cancelUrl?: string;

  @IsOptional()
  @IsObject()
  productMetadata?: Record<string, any>;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

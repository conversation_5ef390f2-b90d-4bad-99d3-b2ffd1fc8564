export enum TransferStatus {
  // Transfer Initiation States
  TRANSFER_PENDING = 'TRANSFER_PENDING',
  TRANSFER_QUEUED = 'TRANSFER_QUEUED',
  TRANSFER_PROCESSING = 'TRANSFER_PROCESSING',

  // Transfer Execution States
  TRANSFER_SUBMITTED = 'TRANSFER_SUBMITTED',
  TRANSFER_CONFIRMING = 'TRANSFER_CONFIRMING',
  TRANSFER_CONFIRMED = 'TRANSFER_CONFIRMED',

  // Transfer Completion States
  TRANSFER_COMPLETED = 'TRANSFER_COMPLETED',

  // Transfer Error States
  TRANSFER_FAILED = 'TRANSFER_FAILED',
  BLOCKCHAIN_ERROR = 'BLOCKCHAIN_ERROR',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  CONTRACT_ERROR = 'CONTRACT_ERROR',
  WALLET_ERROR = 'WALLET_ERROR',

  // Transfer Admin States
  TRANSFER_MANUAL_REVIEW = 'TRANSFER_MANUAL_REVIEW',
  TRANSFER_ADMIN_PROCESSING = 'TRANSFER_ADMIN_PROCESSING',
}

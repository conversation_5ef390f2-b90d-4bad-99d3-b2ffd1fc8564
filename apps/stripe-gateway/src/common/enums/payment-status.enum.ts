export enum PaymentStatus {
  // Initial Payment States
  CREATED = 'CREATED',
  SESSION_CREATED = 'SESSION_CREATED',
  PAYMENT_PENDING = 'PAYMENT_PENDING',
  PAYMENT_PROCESSING = 'PAYMENT_PROCESSING',

  // Payment Completion States
  PAYMENT_SUCCEEDED = 'PAYMENT_SUCCEEDED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  PAYMENT_CANCELLED = 'PAYMENT_CANCELLED',
  PAYMENT_EXPIRED = 'PAYMENT_EXPIRED',

  // Payment Error & Admin States
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  REFUND_PENDING = 'REFUND_PENDING',
  REFUND_COMPLETED = 'REFUND_COMPLETED',
  PAYMENT_MANUAL_REVIEW = 'PAYMENT_MANUAL_REVIEW',
}

import { TransferStatus } from '../enums/transfer-status.enum';

export interface NFTSaleOrder {
  // Core Identifiers
  nftOrderId: string;
  paymentOrderId: string;
  
  // NFT-Specific Information
  tokenId?: string;
  contractAddress?: string;
  nftType?: string;
  
  // Wallet Information
  userWalletAddress: string;
  adminWalletAddress?: string;
  
  // Blockchain Transaction Details
  blockchainTxHash?: string;
  blockchainConfirmations: number;
  gasUsed?: number;
  gasPrice?: string;
  blockNumber?: number;
  
  // NFT Transfer Status Tracking
  transferStatus: TransferStatus;
  transferRetryCount: number;
  transferLastRetryAt?: Date;
  transferErrorDetails?: Record<string, any>;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  transferInitiatedAt?: Date;
  transferCompletedAt?: Date;
  
  // NFT Metadata
  nftMetadata?: Record<string, any>;
  transferMetadata?: Record<string, any>;
}

export interface CreateNFTSaleOrderInput {
  paymentOrderId: string;
  userWalletAddress: string;
  tokenId?: string;
  contractAddress?: string;
  nftType?: string;
  nftMetadata?: Record<string, any>;
}

export interface UpdateNFTSaleOrderInput {
  transferStatus?: TransferStatus;
  blockchainTxHash?: string;
  blockchainConfirmations?: number;
  gasUsed?: number;
  gasPrice?: string;
  blockNumber?: number;
  transferErrorDetails?: Record<string, any>;
  transferMetadata?: Record<string, any>;
}

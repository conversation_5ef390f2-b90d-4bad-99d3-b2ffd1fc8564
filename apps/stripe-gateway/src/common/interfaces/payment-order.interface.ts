import { ProductType } from '../enums/product-type.enum';
import { PaymentStatus } from '../enums/payment-status.enum';

export interface PaymentOrder {
  // Core Identifiers
  paymentOrderId: string;
  userId: string;
  
  // Product Information (Generic)
  productType: ProductType;
  productId: string;
  productMetadata?: Record<string, any>;
  
  // Payment Details
  amount: number;
  currency: string;
  
  // Stripe Integration
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  stripeChargeId?: string;
  stripeCustomerId?: string;
  
  // Payment Status Tracking
  paymentStatus: PaymentStatus;
  paymentRetryCount: number;
  paymentLastRetryAt?: Date;
  paymentErrorDetails?: Record<string, any>;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  paymentCompletedAt?: Date;
  expiresAt?: Date;
  
  // Admin & Audit
  adminNotes?: string;
  metadata?: Record<string, any>;
}

export interface CreatePaymentOrderInput {
  userId: string;
  productType: ProductType;
  productId: string;
  amount: number;
  currency?: string;
  productMetadata?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface UpdatePaymentOrderInput {
  paymentStatus?: PaymentStatus;
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  stripeChargeId?: string;
  paymentErrorDetails?: Record<string, any>;
  adminNotes?: string;
  metadata?: Record<string, any>;
}

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ProductType } from '../common/enums/product-type.enum';
import { PaymentStatus } from '../common/enums/payment-status.enum';

export type PaymentOrderDocument = PaymentOrderSchema & Document;

@Schema({
  timestamps: true,
  collection: 'payment_orders',
})
export class PaymentOrderSchema {
  @Prop({ required: true, unique: true, index: true, type: String })
  paymentOrderId: string;

  @Prop({ required: true, index: true, type: String })
  userId: string;

  @Prop({ required: true, enum: ProductType, index: true, type: String })
  productType: ProductType;

  @Prop({ required: true, type: String })
  productId: string;

  @Prop({ type: Object, default: {} })
  productMetadata: Record<string, any>;

  @Prop({ required: true, type: Number })
  amount: number;

  @Prop({ required: true, default: 'usd', type: String })
  currency: string;

  @Prop({ unique: true, sparse: true, type: String })
  stripeSessionId: string;

  @Prop({ unique: true, sparse: true, type: String })
  stripePaymentIntentId: string;

  @Prop({ unique: true, sparse: true, type: String })
  stripeChargeId: string;

  @Prop({ index: true, type: String })
  stripeCustomerId: string;

  @Prop({
    required: true,
    enum: PaymentStatus,
    index: true,
    default: PaymentStatus.CREATED,
    type: String,
  })
  paymentStatus: PaymentStatus;

  @Prop({ default: 0, type: Number })
  paymentRetryCount: number;

  @Prop({ type: Date })
  paymentLastRetryAt: Date;

  @Prop({ type: Object })
  paymentErrorDetails: Record<string, any>;

  @Prop({ type: Date })
  paymentCompletedAt: Date;

  @Prop({ index: true, type: Date })
  expiresAt: Date;

  @Prop({ type: String })
  adminNotes: string;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
}

export const PaymentOrderSchemaFactory =
  SchemaFactory.createForClass(PaymentOrderSchema);

// Add compound indexes
PaymentOrderSchemaFactory.index({ paymentStatus: 1, createdAt: 1 });
PaymentOrderSchemaFactory.index({ userId: 1, createdAt: -1 });
PaymentOrderSchemaFactory.index({ productType: 1, paymentStatus: 1 });

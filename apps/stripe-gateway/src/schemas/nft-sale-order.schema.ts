import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { TransferStatus } from '../common/enums/transfer-status.enum';

export type NFTSaleOrderDocument = NFTSaleOrderSchema & Document;

@Schema({ 
  timestamps: true,
  collection: 'nft_sale_orders'
})
export class NFTSaleOrderSchema {
  @Prop({ required: true, unique: true, index: true, type: String })
  nftOrderId: string;

  @Prop({ required: true, index: true, type: String })
  paymentOrderId: string;

  @Prop({ type: String })
  tokenId: string;

  @Prop({ type: String })
  contractAddress: string;

  @Prop({ type: String })
  nftType: string;

  @Prop({ required: true, index: true, type: String })
  userWalletAddress: string;

  @Prop({ type: String })
  adminWalletAddress: string;

  @Prop({ unique: true, sparse: true, type: String })
  blockchainTxHash: string;

  @Prop({ default: 0, type: Number })
  blockchainConfirmations: number;

  @Prop({ type: Number })
  gasUsed: number;

  @Prop({ type: String })
  gasPrice: string;

  @Prop({ type: Number })
  blockNumber: number;

  @Prop({ required: true, enum: TransferStatus, index: true, default: TransferStatus.TRANSFER_PENDING, type: String })
  transferStatus: TransferStatus;

  @Prop({ default: 0, type: Number })
  transferRetryCount: number;

  @Prop({ type: Date })
  transferLastRetryAt: Date;

  @Prop({ type: Object })
  transferErrorDetails: Record<string, any>;

  @Prop({ type: Date })
  transferInitiatedAt: Date;

  @Prop({ type: Date })
  transferCompletedAt: Date;

  @Prop({ type: Object, default: {} })
  nftMetadata: Record<string, any>;

  @Prop({ type: Object, default: {} })
  transferMetadata: Record<string, any>;
}

export const NFTSaleOrderSchemaFactory = SchemaFactory.createForClass(NFTSaleOrderSchema);

// Add compound indexes
NFTSaleOrderSchemaFactory.index({ transferStatus: 1, createdAt: 1 });
NFTSaleOrderSchemaFactory.index({ userWalletAddress: 1, createdAt: -1 });
NFTSaleOrderSchemaFactory.index({ paymentOrderId: 1 });

{"name": "stripe-gateway", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "echo \"No tests configured. Use the application directly for testing.\""}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^3.3.0", "@nestjs/core": "^11.0.1", "@nestjs/mongoose": "^10.0.10", "@nestjs/platform-express": "^11.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "mongoose": "^8.8.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "stripe": "^18.2.1", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/node": "^22.10.7", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}}
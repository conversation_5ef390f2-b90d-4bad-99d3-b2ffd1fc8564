<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Stripe Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
      body {
        font-family: sans-serif;
        padding: 2rem;
      }
      #card-element {
        border: 1px solid #ccc;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 1rem;
      }
      #submit {
        padding: 10px 20px;
        background-color: #635bff;
        color: white;
        border: none;
        border-radius: 4px;
      }
      #result {
        margin-top: 1rem;
      }
    </style>
  </head>
  <body>
    <h2>Pay with Card</h2>
    <form id="payment-form">
      <div id="card-element"></div>
      <button id="submit">Pay</button>
      <div id="result"></div>
    </form>

    <script>
      const stripe = Stripe(
        'pk_test_51RX9xtPOLHw3HDPChaJJUd0FCoJPH6CSTLQhS2jozVeAwcyAqT7pGqI0R3ukE56ZFLqfcdfMiWiqcTxzhQCIbiMt00t1myuyxI',
      ); // Replace with your real publishable key

      const elements = stripe.elements();
      const card = elements.create('card');
      card.mount('#card-element');

      const form = document.getElementById('payment-form');
      const result = document.getElementById('result');

      form.addEventListener('submit', async (event) => {
        event.preventDefault();

        // 1. Call your backend to create the PaymentIntent
        const response = await fetch(
          'http://localhost:3000/stripe/create-payment-intent',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ amount: 10000, orderId: 'order_123' }), // $50.00
          },
        );

        const { clientSecret } = await response.json();

        // 2. Confirm the payment
        const { paymentIntent, error } = await stripe.confirmCardPayment(
          clientSecret,
          {
            payment_method: {
              card,
              billing_details: {
                name: 'Jane Doe',
              },
            },
          },
        );

        // 3. Handle the result
        if (error) {
          result.textContent = '❌ Payment failed: ' + error.message;
          result.style.color = 'red';
        } else if (paymentIntent && paymentIntent.status === 'succeeded') {
          result.textContent = '✅ Payment succeeded!';
          result.style.color = 'green';
        }
      });
    </script>
  </body>
</html>

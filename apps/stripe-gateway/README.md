# Stripe Gateway Service

A lean, focused NestJS microservice for handling Stripe payment processing, specifically optimized for the DCasks NFT marketplace payment flow.

## Features

- **Checkout Session Management**: Create and track Stripe checkout sessions
- **Payment Order Tracking**: Complete payment order lifecycle management
- **Webhook Processing**: Automated payment status updates via Stripe webhooks
- **NFT Sale Integration**: Seamless integration with NFT transfer workflow
- **Queue-Optimized**: Designed for 100-user concurrent queue with batch processing

## Optimized API Endpoints

### Payment Orders (`/orders`)
- `POST /orders/payment` - Create payment order for queue users
- `GET /orders/payment/:paymentOrderId` - Get payment order details
- `GET /orders/payment/user/:userId` - Get user payment history
- `PUT /orders/payment/:paymentOrderId` - Update payment order status

### NFT Sale Orders (`/orders`)
- `GET /orders/nft/payment/:paymentOrderId` - Get NFT transfer status

### Stripe Integration (`/stripe`)
- `POST /stripe/create-checkout-session` - Create Stripe checkout session
- `GET /stripe/session/:sessionId` - Retrieve checkout session status

### Webhooks (`/webhooks`)
- `POST /webhooks/stripe` - Stripe webhook event handler

## NFT Marketplace Payment Flow

1. **Queue User Selection** → `POST /orders/payment` (create payment order)
2. **Payment Initiation** → `POST /stripe/create-checkout-session` (create Stripe session)
3. **Status Monitoring** → `GET /orders/payment/:paymentOrderId` (track payment status)
4. **Payment Completion** → `POST /webhooks/stripe` (automatic status update)
5. **NFT Transfer** → Handled by DCasks Backend (not Stripe Gateway)

## Environment Variables

```bash
# Application
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/stripe-gateway

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# CORS
CORS_ORIGIN=http://localhost:3000
```

## Installation & Usage

```bash
# Install dependencies
pnpm install

# Build the application
pnpm build

# Start in development mode
pnpm start:dev

# Start in production mode
pnpm start:prod
```

## Architecture

### Two-Model Design
- **Payment Order Model**: Generic payment processing for all product types
- **NFT Sale Order Model**: Specialized NFT transfer management (created automatically)

### Core Services
- **OrdersService**: Payment and NFT order CRUD operations
- **StripeService**: Stripe API integration and webhook processing
- **StripeWebhookController**: Webhook event handling (thin controller pattern)

## Payment Status Flow

```
CREATED → SESSION_CREATED → PAYMENT_PENDING → PAYMENT_SUCCEEDED
                         ↓
                    PAYMENT_FAILED / PAYMENT_EXPIRED
```

## NFT Transfer Status Flow

```
TRANSFER_PENDING → TRANSFER_INITIATED → TRANSFER_COMPLETED
                ↓
           TRANSFER_FAILED
```

## Integration Points

- **Capcom Frontend**: Payment initiation for queue users
- **DCasks Backend**: NFT transfer processing after successful payments
- **Stripe**: Payment processing and webhook events
- **MongoDB**: Payment order and NFT sale order persistence

## Optimizations for NFT Marketplace

- **Removed Legacy Endpoints**: Eliminated payment intent approach, refund functionality
- **Queue-Focused**: Optimized for 100-user queue with 5-minute payment windows
- **Lean API Surface**: 9 focused endpoints vs 15+ redundant endpoints
- **Webhook-Driven**: Automatic NFT sale order creation via payment success webhooks
- **Status Tracking**: Comprehensive payment and transfer status management

## Security

- **Webhook Signature Verification**: All Stripe webhooks verified with secret
- **Input Validation**: Comprehensive validation on all API endpoints
- **Environment Configuration**: Secure handling of sensitive configuration
- **CORS Protection**: Configured for frontend integration

## Development

Built with modern NestJS patterns:
- **Thin Controllers**: HTTP concerns only, business logic in services
- **NestJS Exceptions**: Proper error handling with automatic HTTP status codes
- **Dependency Injection**: Clean service composition and testability
- **TypeScript**: Full type safety throughout the application

## Production Ready

- ✅ **Optimized API**: Lean, focused endpoints for NFT marketplace
- ✅ **Webhook Processing**: Reliable payment status updates
- ✅ **Error Handling**: Comprehensive error logging and recovery
- ✅ **Type Safety**: Full TypeScript implementation
- ✅ **Clean Architecture**: Following NestJS best practices

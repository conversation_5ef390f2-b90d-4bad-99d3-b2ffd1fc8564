# Stripe Gateway

A simple NestJS-based Stripe payment gateway service for handling payment processing and webhook events.

## Description

This service provides a secure payment gateway using Stripe's API with NestJS configuration management. It handles payment intent creation and webhook processing for payment events.

## Environment Setup

### 1. Copy Environment File
```bash
cp .env.example .env
```

### 2. Configure Environment Variables

#### Required Variables
- `STRIPE_SECRET_KEY`: Your Stripe secret key (starts with `sk_test_` for test mode or `sk_live_` for production)
- `STRIPE_WEBHOOK_SECRET`: Your Stripe webhook endpoint secret (starts with `whsec_`)

#### Optional Variables
- `PORT`: Server port (default: 3001)
- `NODE_ENV`: Environment mode (development/production)
- `CORS_ORIGIN`: Comma-separated allowed CORS origins

### 3. Stripe Configuration

1. **Get your Stripe keys:**
   - Login to [Stripe Dashboard](https://dashboard.stripe.com/)
   - Go to Developers > API keys
   - Copy your Secret key and Publishable key

2. **Set up webhooks:**
   - Go to Developers > Webhooks
   - Add endpoint: `https://your-domain.com/webhooks/stripe`
   - Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
   - Copy the webhook signing secret

3. **Update .env file:**
   ```env
   STRIPE_SECRET_KEY=sk_test_your_actual_secret_key
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret
   ```

## Project Setup

```bash
# Install dependencies
$ pnpm install

# Start development server
$ pnpm run start:dev

# Build for production
$ pnpm run build

# Start production server
$ pnpm run start:prod
```

## NestJS Configuration

This project uses NestJS `@nestjs/config` module for configuration management:

- **Configuration files**: Located in `src/config/`
  - `app.config.ts`: Application settings (port, environment, CORS)
  - `stripe.config.ts`: Stripe-specific configuration
- **Usage**: Configuration is injected via `ConfigService`
- **Environment validation**: Built into NestJS configuration system

## API Endpoints

### Payment Intent Creation
```http
POST /stripe/create-payment-intent
Content-Type: application/json

{
  "amount": 2000,
  "orderId": "order_123"
}
```

### Webhook Handler
```http
POST /webhooks/stripe
Content-Type: application/json
Stripe-Signature: t=timestamp,v1=signature
```

## Testing

```bash
# Unit tests
$ pnpm run test

# E2E tests
$ pnpm run test:e2e

# Test coverage
$ pnpm run test:cov
```

## Webhook Testing

Use Stripe CLI for local webhook testing:

```bash
# Install Stripe CLI
# Forward events to local server
stripe listen --forward-to localhost:3001/webhooks/stripe

# Trigger test events
stripe trigger payment_intent.succeeded
```

## Architecture

- **NestJS Framework**: Modern Node.js framework with TypeScript
- **Configuration Management**: Uses `@nestjs/config` for environment variables
- **Stripe Integration**: Official Stripe Node.js SDK
- **Webhook Security**: Signature verification for all webhook events

## License

This project is licensed under the MIT License.
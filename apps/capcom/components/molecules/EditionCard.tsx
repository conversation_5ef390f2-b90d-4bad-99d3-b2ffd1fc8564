import { Card } from "@/components/ui/card";
import Image from "next/image";

interface EditionCardProps {
  name: string;
  image: string;
  className?: string;
}

export function EditionCard({ name, image, className = "" }: EditionCardProps) {
  return (
    <Card className={`bg-card border-border p-6 hover:border-brand-primary transition-colors group ${className}`}>
      <div className="relative w-full h-80 mb-6">
        <Image
          src={image}
          alt={`${name} Whisky Edition`}
          fill
          className="object-contain group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      <h3 className="font-serif text-xl lg:text-2xl text-center text-card-foreground">
        {name}
      </h3>
    </Card>
  );
}

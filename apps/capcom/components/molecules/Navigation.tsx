import { NavLink } from "@/components/atoms/NavLink";

interface NavigationItem {
  href: string;
  label: string;
  isActive?: boolean;
}

interface NavigationProps {
  items: NavigationItem[];
  className?: string;
}

export function Navigation({ items, className = "" }: NavigationProps) {
  return (
    <nav className={`hidden md:flex items-center space-x-8 ${className}`}>
      {items.map((item) => (
        <NavLink
          key={item.label}
          href={item.href}
          isActive={item.isActive}
        >
          {item.label}
        </NavLink>
      ))}
    </nav>
  );
}

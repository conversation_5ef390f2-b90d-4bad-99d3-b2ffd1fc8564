import Image from "next/image";

interface ProductImageProps {
  src: string;
  alt: string;
  width?: string;
  height?: string;
  className?: string;
  containerClassName?: string;
}

export function ProductImage({ 
  src, 
  alt, 
  width = "w-80", 
  height = "h-96", 
  className = "",
  containerClassName = ""
}: ProductImageProps) {
  return (
    <div className={`relative ${width} ${height} lg:w-96 lg:h-[480px] ${containerClassName}`}>
      <Image
        src={src}
        alt={alt}
        fill
        className={`object-contain ${className}`}
      />
    </div>
  );
}

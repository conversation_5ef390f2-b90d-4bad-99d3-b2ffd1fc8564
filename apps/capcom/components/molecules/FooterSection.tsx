import Link from "next/link";

interface FooterLink {
  href: string;
  label: string;
}

interface FooterSectionProps {
  title: string;
  links?: FooterLink[];
  content?: React.ReactNode;
  className?: string;
}

export function FooterSection({ title, links, content, className = "" }: FooterSectionProps) {
  return (
    <div className={className}>
      <h4 className="font-semibold text-foreground mb-4">{title}</h4>
      {links && (
        <ul className="space-y-2 text-muted-foreground">
          {links.map((link) => (
            <li key={link.label}>
              <Link
                href={link.href}
                className="hover:text-brand-primary transition-colors"
              >
                {link.label}
              </Link>
            </li>
          ))}
        </ul>
      )}
      {content && content}
    </div>
  );
}

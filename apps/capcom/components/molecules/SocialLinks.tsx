import { SocialLink } from "@/components/atoms/SocialLink";

interface SocialLinksProps {
  className?: string;
}

export function SocialLinks({ className = "" }: SocialLinksProps) {
  const socialLinks = [
    { href: "#", label: "twitter" },
    { href: "#", label: "facebook" },
    { href: "#", label: "telegram" },
  ];

  return (
    <div className={`flex space-x-6 ${className}`}>
      {socialLinks.map((link) => (
        <SocialLink key={link.label} href={link.href}>
          {link.label}
        </SocialLink>
      ))}
    </div>
  );
}

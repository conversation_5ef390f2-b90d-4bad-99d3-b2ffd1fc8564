"use client";

import { Logo } from "@/components/atoms/Logo";
import { Navigation } from "@/components/molecules/Navigation";
import { WalletButton } from "@/components/atoms/WalletButton";
import { useState, useEffect } from "react";

export function Header() {
  const [scrollOpacity, setScrollOpacity] = useState(0.3);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const maxScroll = 100; // Maximum scroll distance for full opacity transition

      // Calculate opacity based on scroll position (0.3 to 0.8)
      const minOpacity = 0.1;
      const maxOpacity = 0.9;
      const scrollRatio = Math.min(scrollPosition / maxScroll, 1);
      const newOpacity = minOpacity + (maxOpacity - minOpacity) * scrollRatio;

      setScrollOpacity(newOpacity);
    };

    // Add scroll event listener with passive option for better performance
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Check initial scroll position
    handleScroll();

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const navigationItems = [
    { href: "/", label: "Home" },
    { href: "/", label: "Marketplace" },
    { href: "/", label: "Membership" },
    { href: "/", label: "Collaborations", isActive: true },
    { href: "/", label: "Journals" },
    { href: "/", label: "FAQs" },
  ];

  return (
    <header
      className="sticky top-0 z-50 border-b border-border px-4 lg:px-8 backdrop-blur-sm transition-all duration-300 ease-in-out"
      style={{
        backgroundColor: `hsl(var(--background) / ${scrollOpacity})`,
      }}
    >
      <div className="mx-auto max-w-7xl flex items-center justify-between h-16">
        <Logo />
        <Navigation items={navigationItems} />
        <WalletButton />
      </div>
    </header>
  );
}

import { But<PERSON> } from "@/components/ui/button";
import { EditionCard } from "@/components/molecules/EditionCard";

export function ExploreEditionsSection() {
  const editions = [
    {
      name: "Umbrella Corp",
      image: "/assets/bottle-1.png",
    },
    {
      name: "Tactical",
      image: "/assets/bottle-2.png",
    },
    {
      name: "Bio<PERSON>azard",
      image: "/assets/bottle-3.png",
    },
  ];

  return (
    <section className="px-4 lg:px-8 py-16 lg:py-24 bg-muted/50">
      <div className="mx-auto max-w-7xl">
        <h2 className="font-serif text-3xl lg:text-4xl xl:text-5xl text-center mb-16 tracking-wide">
          EXPLORE EDITIONS
        </h2>

        {/* Product Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12 mb-16">
          {editions.map((edition, index) => (
            <EditionCard
              key={index}
              name={edition.name}
              image={edition.image}
            />
          ))}
        </div>

        {/* See More Button */}
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-brand-primary-foreground px-8 py-3 text-lg tracking-wide"
          >
            SEE MORE ON MARKETPLACE
          </Button>
        </div>
      </div>
    </section>
  );
}

import { Logo } from "@/components/atoms/Logo";
import { FooterSection } from "@/components/molecules/FooterSection";
import { SocialLinks } from "@/components/molecules/SocialLinks";

export function Footer() {
  const marketplaceLinks = [
    { href: "#", label: "Sell a cask" },
    { href: "#", label: "Buy NFT" },
  ];

  const supportLinks = [
    { href: "#", label: "How to connect wallet" },
    { href: "#", label: "How to buy" },
    { href: "#", label: "How to sell" },
    { href: "#", label: "Auctioning" },
  ];

  const usefulLinks = [
    { href: "#", label: "Terms and Conditions" },
    { href: "#", label: "Privacy Policies" },
  ];

  const contactContent = (
    <div className="space-y-2 text-muted-foreground text-sm">
      <p>Dcasks Pte. Ltd.</p>
      <p>Registration number</p>
      <p>(UEN) 202123383R</p>
      <p><EMAIL></p>
      <p className="mt-4">Whatsapp business</p>
    </div>
  );

  return (
    <footer className="border-t border-border px-4 lg:px-8 py-16">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12">
          {/* Logo */}
          <div className="md:col-span-1">
            <Logo showText className="mb-4" />
          </div>

          {/* Footer Sections */}
          <FooterSection title="Marketplace" links={marketplaceLinks} />
          <FooterSection title="Support" links={supportLinks} />
          <FooterSection title="Useful links" links={usefulLinks} />
          <FooterSection title="Contact Us" content={contactContent} />
        </div>

        {/* Social Links and Copyright */}
        <div className="border-t border-border pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <SocialLinks className="mb-4 md:mb-0" />
            <p className="text-muted-foreground text-sm">
              Copyright © DCASKS PTE LTD All Rights Reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

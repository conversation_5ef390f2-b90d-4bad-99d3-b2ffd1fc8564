import Image from "next/image";

interface LogoProps {
  showText?: boolean;
  className?: string;
  width?: number;
  height?: number;
}

export function Logo({
  showText = false,
  className = "",
  width = 40,
  height = 40,
}: LogoProps) {
  return (
    <div className={`flex items-center ${className}`}>
      <Image
        src="/logo.png"
        alt="DCasks Logo"
        width={width}
        height={height}
        className="object-contain"
        priority
      />
      {showText && (
        <span className="font-serif text-2xl font-semibold ml-3">DCASKS</span>
      )}
    </div>
  );
}

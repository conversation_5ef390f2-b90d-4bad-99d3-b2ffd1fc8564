import Link from "next/link";

interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  isActive?: boolean;
  className?: string;
}

export function NavLink({ href, children, isActive = false, className = "" }: NavLinkProps) {
  const baseClasses = "transition-colors";
  const activeClasses = "text-brand-primary border-b-2 border-brand-primary pb-1";
  const inactiveClasses = "text-muted-foreground hover:text-foreground";
  
  const linkClasses = `${baseClasses} ${isActive ? activeClasses : inactiveClasses} ${className}`;

  return (
    <Link href={href} className={linkClasses}>
      {children}
    </Link>
  );
}

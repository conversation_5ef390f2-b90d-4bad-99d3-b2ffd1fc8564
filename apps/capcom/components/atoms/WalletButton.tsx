import { Button } from "@/components/ui/button";

interface WalletButtonProps {
  address?: string;
  onClick?: () => void;
  className?: string;
}

export function WalletButton({
  address = "Connect Wallet",
  onClick,
  className = "",
}: WalletButtonProps) {
  return (
    <Button
      variant="outline"
      onClick={onClick}
      className={`border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-brand-primary-foreground ${className}`}
    >
      {address}
    </Button>
  );
}

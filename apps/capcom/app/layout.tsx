import type { Metada<PERSON> } from "next";
import { Fraunces } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const fraunces = Fraunces({
  subsets: ["latin"],
  variable: "--font-fraunces",
});

const residentEvilFont = localFont({
  src: "../public/fonts/resident_evil_4_remake_font.ttf",
  variable: "--font-resident-evil",
  display: "swap",
});

export const metadata: Metadata = {
  title: "DCasks x Capcom",
  description: "RESIDENT EVIL EDITIONS SINGLE MALT WHISKY",
  icons: {
    icon: "/logo.png",
    apple: "/logo.png",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${fraunces.variable} ${residentEvilFont.variable}`} suppressHydrationWarning>
      <body className={fraunces.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          forcedTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}

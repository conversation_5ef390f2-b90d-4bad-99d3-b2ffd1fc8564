{"ALBSecurityGroup": {"GroupId": "sg-0123456789abcdef0", "GroupName": "dcasks-alb-sg", "Description": "Security group for DCasks ALB", "InboundRules": [{"Protocol": "tcp", "Port": 80, "Source": "0.0.0.0/0", "Description": "HTTP access"}, {"Protocol": "tcp", "Port": 443, "Source": "0.0.0.0/0", "Description": "HTTPS access"}]}, "APISecurityGroup": {"GroupId": "sg-0123456789abcdef1", "GroupName": "dcasks-api-sg", "Description": "Security group for DCasks API services", "InboundRules": [{"Protocol": "tcp", "Port": 9000, "Source": "sg-0123456789abcdef0", "Description": "API access from ALB"}]}, "JobSecurityGroup": {"GroupId": "sg-0123456789abcdef2", "GroupName": "dcasks-job-sg", "Description": "Security group for DCasks Job services", "OutboundRules": [{"Protocol": "tcp", "Port": 443, "Destination": "0.0.0.0/0", "Description": "HTTPS outbound for external APIs"}]}}
# Shared Resources

## How to Use This Folder
1. **Reference shared resources**: Use resource IDs from JSON files in service configurations
2. **Update resource inventory**: Add new shared resources to `resource-inventory.md`
3. **Manage infrastructure**: Use JSON files to document shared AWS resources
4. **Source environment variables**: Use `../dcasks-migration-resources.env` for CLI scripts

## File Structure
- `vpc-network.json`: VPC, subnets, internet gateway configurations
- `security-groups.json`: Security group definitions for ALB, API, Job services
- `load-balancer.json`: ALB configuration with listeners and target groups
- `certificates.json`: ACM certificate configurations
- `iam-roles.json`: Cross-service IAM roles and policies
- `resource-inventory.md`: Complete list of shared resource IDs

## How to Reference Shared Resources
```json
// In service configurations, reference shared resources:
{
  "securityGroups": ["sg-0123456789abcdef1"],
  "subnets": ["subnet-0123456789abcdef1", "subnet-0123456789abcdef2"],
  "loadBalancerArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:loadbalancer/app/dcasks-alb/3e7880e8783b3295"
}
```

## How to Add New Shared Resources
1. Create or update appropriate JSON file
2. Add resource ID to `resource-inventory.md`
3. Update `../dcasks-migration-resources.env` if needed for CLI scripts
4. Reference new resource in service configurations

## AI Maintenance Instructions
- Keep JSON files updated with actual resource IDs
- Maintain resource-inventory.md as single source of truth
- Use shared resources across services to avoid duplication
- Document any new shared infrastructure patterns
- Validate resource IDs before updating configurations

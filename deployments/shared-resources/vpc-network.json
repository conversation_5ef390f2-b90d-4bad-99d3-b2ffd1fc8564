{"VPC": {"VpcId": "vpc-0123456789abcdef0", "CidrBlock": "********/16", "Description": "DCasks Platform VPC"}, "Subnets": {"PublicSubnet1": {"SubnetId": "subnet-0123456789abcdef1", "AvailabilityZone": "ap-southeast-1a", "CidrBlock": "********/24"}, "PublicSubnet2": {"SubnetId": "subnet-0123456789abcdef2", "AvailabilityZone": "ap-southeast-1b", "CidrBlock": "********/24"}}, "InternetGateway": {"InternetGatewayId": "igw-0123456789abcdef0", "Description": "DCasks Platform Internet Gateway"}, "RouteTables": {"PublicRouteTable": {"RouteTableId": "rtb-0123456789abcdef0", "Description": "Public subnet route table"}}}
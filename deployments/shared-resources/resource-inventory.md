# DCasks Platform Shared Resources Inventory

## VPC and Networking
- **VPC ID**: `vpc-0123456789abcdef0` (10.1.0.0/16)
- **Public Subnet 1**: `subnet-0123456789abcdef1` (ap-southeast-1a)
- **Public Subnet 2**: `subnet-0123456789abcdef2` (ap-southeast-1b)
- **Internet Gateway**: `igw-0123456789abcdef0`
- **Route Table**: `rtb-0123456789abcdef0`

## Load Balancer
- **ALB ARN**: `arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:loadbalancer/app/dcasks-alb/3e7880e8783b3295`
- **ALB DNS**: `dcasks-alb-3e7880e8783b3295.ap-southeast-1.elb.amazonaws.com`
- **HTTP Listener**: Port 80
- **HTTPS Listener**: Port 443

## Security Groups
- **ALB Security Group**: `sg-0123456789abcdef0` (dcasks-alb-sg)
- **API Security Group**: `sg-0123456789abcdef1` (dcasks-api-sg)
- **Job Security Group**: `sg-0123456789abcdef2` (dcasks-job-sg)

## Certificates
- **Wildcard Certificate**: `arn:aws:acm:ap-southeast-1:483805378365:certificate/d8052dbe-c47a-4354-ab8f-296dbc30d7f9`
- **Domain**: `*.dcasks.co`
- **Status**: ISSUED

## ECS Clusters
- **Production Cluster**: `dcasks-cluster-prod`
- **Non-Production Cluster**: `dcasks-cluster-nprod`

## CloudWatch Log Groups
- **API Dev**: `/ecs/dcasks-api-dev`
- **API Testnet**: `/ecs/dcasks-api-testnet`
- **API Production**: `/ecs/dcasks-api-production`
- **Job Dev**: `/ecs/dcasks-job-dev`
- **Job Testnet**: `/ecs/dcasks-job-testnet`
- **Job Production**: `/ecs/dcasks-job-production`

## How to Update This Inventory
1. When creating new shared resources, add them to appropriate JSON files
2. Update this inventory with new resource IDs
3. Reference these resources in service configurations
4. Use environment variables from dcasks-migration-resources.env for scripts

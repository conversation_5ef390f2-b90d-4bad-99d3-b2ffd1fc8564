{"LoadBalancer": {"LoadBalancerArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:loadbalancer/app/dcasks-alb/3e7880e8783b3295", "LoadBalancerName": "dcasks-alb", "DNSName": "dcasks-alb-3e7880e8783b3295.ap-southeast-1.elb.amazonaws.com", "Scheme": "internet-facing", "Type": "application"}, "Listeners": {"HTTPListener": {"ListenerArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:listener/app/dcasks-alb/3e7880e8783b3295/abcdef1234567890", "Protocol": "HTTP", "Port": 80}, "HTTPSListener": {"ListenerArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:listener/app/dcasks-alb/3e7880e8783b3295/d3accfe7c1668e23", "Protocol": "HTTPS", "Port": 443}}, "TargetGroups": {"DevAPITargetGroup": {"TargetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/dcasks-api-tg-dev-9000/e76c382c45aa36b7", "TargetGroupName": "dcasks-api-tg-dev-9000", "Port": 9000, "Protocol": "HTTP", "HealthCheckPath": "/health"}}}
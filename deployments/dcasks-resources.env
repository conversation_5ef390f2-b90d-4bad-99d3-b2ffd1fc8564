# DCasks Backend ECS Migration - Resource IDs
# Generated: 2024-01-15 10:40:00 UTC
# Status: Phases 1-2 completed, ready for Phase 3
# Region: ap-southeast-1
# Profile: dcasks-david

# Phase 1: VPC and Network Infrastructure (COMPLETED)
export VPC_ID=vpc-015a028c94079948c                    # dcasks-vpc (10.1.0.0/16)
export PUBLIC_SUBNET_1_ID=subnet-03efc31f4ddd3982c      # dcasks-public-subnet-1a (ap-southeast-1a)
export PUBLIC_SUBNET_2_ID=subnet-091f7ea008ad881be      # dcasks-public-subnet-1b (ap-southeast-1b)
export IGW_ID=igw-0b85efe135245c538                     # dcasks-igw
export PUBLIC_RT_ID=rtb-014390f1f3a0819ce               # dcasks-public-rt

# Phase 2: Security Groups (COMPLETED)
export API_SG_ID=sg-0d1af7867738fbfb7                   # dcasks-api-sg
export JOB_SG_ID=sg-0e70fc67741322cb5                   # dcasks-job-sg (simplified)
export ALB_SG_ID=sg-0c30410ca1c3cccd3                   # dcasks-alb-sg

# AWS Configuration
export AWS_PROFILE=dcasks-david
export AWS_REGION=ap-southeast-1
export PROJECT_NAME=dcasks

# Phase 3: ECS Clusters (COMPLETED)
export PROD_CLUSTER_ARN=arn:aws:ecs:ap-southeast-1:483805378365:cluster/dcasks-cluster-prod
export NPROD_CLUSTER_ARN=arn:aws:ecs:ap-southeast-1:483805378365:cluster/dcasks-cluster-nprod

# Phase 4: Shared ALB Setup (IN PROGRESS)
export CERT_ARN=arn:aws:acm:ap-southeast-1:483805378365:certificate/5f770836-e245-4b30-8b3b-177375069db5
export ALB_ARN=arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:loadbalancer/app/dcasks-alb/3e7880e8783b3295
export ALB_DNS=dcasks-alb-2025786233.ap-southeast-1.elb.amazonaws.com
export PROD_TG_ARN=arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/dcasks-api-tg-production/5554ae488404a141
export DEV_TG_ARN=arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/dcasks-api-tg-dev/799644404fe3e595
export TESTNET_TG_ARN=arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/dcasks-api-tg-testnet/738b47ea62b90b80
export HTTP_LISTENER_ARN=arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:listener/app/dcasks-alb/3e7880e8783b3295/cc3c914e6df2a4f3

# Phase 5: CloudWatch Log Groups (COMPLETED)
export API_LOG_GROUP_PROD=/ecs/dcasks-api-production
export API_LOG_GROUP_DEV=/ecs/dcasks-api-dev
export API_LOG_GROUP_TESTNET=/ecs/dcasks-api-testnet
export JOB_LOG_GROUP_PROD=/ecs/dcasks-job-production
export JOB_LOG_GROUP_DEV=/ecs/dcasks-job-dev
export JOB_LOG_GROUP_TESTNET=/ecs/dcasks-job-testnet

# Phase 6: IAM Roles for ECS (COMPLETED)
export TASK_EXECUTION_ROLE_ARN=arn:aws:iam::483805378365:role/dcasks-ecs-task-execution-role
export TASK_ROLE_ARN=arn:aws:iam::483805378365:role/dcasks-ecs-task-role
export CLOUDWATCH_LOGS_POLICY_ARN=arn:aws:iam::483805378365:policy/dcasks-cloudwatch-logs-policy
export SECRETS_MANAGER_POLICY_ARN=arn:aws:iam::483805378365:policy/dcasks-secrets-manager-policy
export EXTERNAL_API_POLICY_ARN=arn:aws:iam::483805378365:policy/dcasks-external-api-policy

# Phase 7: Secrets Manager Migration (COMPLETED)
export DEV_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-1:483805378365:secret:dcasks/dev/dcasks-backend-eRBkbm
export TESTNET_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-1:483805378365:secret:dcasks/testnet/dcasks-backend-5c7Ru4
export PRODUCTION_SECRET_ARN=arn:aws:secretsmanager:ap-southeast-1:483805378365:secret:dcasks/production/dcasks-backend-J0xMdr

# Phase 8: ECS Task Definitions (COMPLETED)
export API_DEV_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-api-task-dev:1
export JOB_DEV_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-job-task-dev:1
export API_TESTNET_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-api-task-testnet:1
export JOB_TESTNET_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-job-task-testnet:1
export API_PROD_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-api-task-production:1
export JOB_PROD_TASK_ARN=arn:aws:ecs:ap-southeast-1:483805378365:task-definition/dcasks-job-task-production:1

# Deployment Structure (Reorganized)
export DEPLOYMENTS_DIR=deployments
export TASK_DEFINITIONS_DIR=deployments/task-definitions
export SERVICE_DEFINITIONS_DIR=deployments/service-definitions
export INFRASTRUCTURE_DIR=deployments/infrastructure
export SECRETS_DIR=deployments/secrets
export SCRIPTS_DIR=deployments/scripts
export CICD_DIR=deployments/ci-cd
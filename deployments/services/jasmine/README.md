# Jasmine Service

## Service Components
- **Web**: Next.js frontend application
- **Admin**: PayloadJS admin CMS

## Quick Commands
```bash
# Deploy entire service to dev
../scripts/deploy.sh --service jasmine --env dev

# Deploy specific component to production
../scripts/deploy.sh --service jasmine --component web --env production

# Check service health
../scripts/health-check.sh --service jasmine --env dev
```

## File Structure
- `service-{env}.json`: Service configurations
- `task-definition-{env}.json`: Task definitions
- `secrets-{env}.json`: Secrets Manager templates

## Environment Configurations
- **Dev**: Development environment with debug logging
- **Testnet**: Testing environment with staging data
- **Production**: Production environment with optimizations

## How to Update Service
1. Edit appropriate JSON file for target environment
2. Register new task definition:
   ```bash
   aws ecs register-task-definition --cli-input-json file://task-definition-{env}.json
   ```
3. Update service:
   ```bash
   aws ecs update-service --service jasmine-service-{env} --task-definition {new-revision}
   ```
4. Verify: `../scripts/health-check.sh --service jasmine --env {env}`

## AI Maintenance Instructions
- Always update task definition before service
- Use health checks to verify deployments
- Follow naming conventions: jasmine-service-{env}
- Document service-specific configurations in this folder
- Jasmine uses Next.js and PayloadJS - ensure proper build configurations

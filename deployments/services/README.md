# DCasks Platform Services

## Available Services
- **dcasks-backend**: Main backend API and job processing
- **jasmine**: Frontend application and admin CMS
- **stripe-gateway**: Payment processing service (future)

## How to Add New Service
1. Copy `../templates/new-service-template/` to `{new-service-name}/`
2. Replace all `{service-name}` placeholders in all files
3. Replace all `{env}` with actual environments (dev, testnet, production)
4. Update configurations for your specific service requirements
5. Test deployment: `../scripts/deploy.sh --service {new-service-name} --env dev`

## Service Naming Conventions
- Folder name: `{service-name}` (kebab-case)
- Service definitions: `{component}-service-{env}.json`
- Task definitions: `task-definition-{component}-{env}.json`
- Multi-component services: Use component prefix (api, job, web, etc.)

## Standard Service Structure
Each service folder must contain:
```
{service-name}/
├── README.md                         # Service-specific operations guide
├── {component}-service-{env}.json    # Service definitions
├── task-definition-{component}-{env}.json # Task definitions
├── secrets-{env}.json                # Secrets templates
└── troubleshooting.md               # Service-specific issues (optional)
```

## How to Deploy Services
```bash
# Deploy entire service to environment
../scripts/deploy.sh --service {service-name} --env {environment}

# Deploy specific component
../scripts/deploy.sh --service {service-name} --component {component} --env {environment}

# Check service health
../scripts/health-check.sh --service {service-name} --env {environment}
```

## AI Instructions for Service Management
1. Each service must have its own folder under `services/`
2. Follow the same file structure pattern for all services
3. Always create README.md for new services using template
4. Document service-specific configurations in service folder
5. Use shared resources from `../shared-resources/` when possible
6. Test all three environments (dev → testnet → production)
7. Update service README.md when adding new configurations

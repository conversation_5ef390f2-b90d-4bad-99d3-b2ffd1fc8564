{"Name": "dcasks/dev/dcasks-backend", "Description": "DCasks Backend development environment secrets", "SecretString": {"PORT": "9000", "NODE_ENV": "development", "SERVICE_NAME": "API", "MONGODB_URI": "mongodb+srv://dev-user:<EMAIL>/dcasks-dev", "REDIS_URL": "redis://dev-redis:6379", "JWT_SECRET": "dev-jwt-secret-key", "ADMIN_PRIVATE_KEY": "dev-admin-private-key", "WOKENS_HOLDER_PRIVATE_KEY": "dev-wokens-holder-private-key", "BLOCKCHAIN_RPC_URL": "https://dev-blockchain-rpc.example.com", "EXTERNAL_API_KEY": "dev-external-api-key"}}
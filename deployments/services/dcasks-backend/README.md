# DCasks Backend Service

## Service Components
- **API**: GraphQL API server (scalable)
- **Job**: Background job processor (single instance)

## Quick Commands
```bash
# Deploy both components to dev
../scripts/deploy.sh --service dcasks-backend --env dev

# Deploy only API to production
../scripts/deploy.sh --service dcasks-backend --component api --env production

# Check service health
../scripts/health-check.sh --service dcasks-backend --env dev
```

## File Structure
- `api-service-{env}.json`: API service configurations
- `job-service-{env}.json`: Job service configurations
- `task-definition-{component}-{env}.json`: Task definitions
- `secrets-{env}.json`: Secrets Manager templates
- `https-setup.md`: HTTPS implementation procedures
- `troubleshooting.md`: Common issues and solutions

## Environment Configurations
- **Dev**: 1 API instance, 1 Job instance
- **Testnet**: 1 API instance, 1 Job instance
- **Production**: 2-5 API instances (auto-scaling), 1 Job instance

## How to Update Service
1. Edit appropriate JSON file for target environment
2. Register new task definition:
   ```bash
   aws ecs register-task-definition --cli-input-json file://task-definition-{component}-{env}.json
   ```
3. Update service:
   ```bash
   aws ecs update-service --service dcasks-{component}-service-{env} --task-definition {new-revision}
   ```
4. Verify: `../scripts/health-check.sh --service dcasks-backend --env {env}`

## Service Naming Pattern
- **API Services**: `dcasks-api-service-{env}`
- **Job Services**: `dcasks-job-service-{env}`
- **Task Definitions**: `dcasks-{component}-task-{env}`

## Health Checks
- **API**: HTTP GET `/health` on port 9000
- **Job**: Container health check using wget command
- **ALB**: Target group health checks for API service

## External Dependencies
- **MongoDB Atlas**: Database connection
- **Redis**: Caching and session storage
- **Blockchain APIs**: External blockchain integration
- **AWS Secrets Manager**: Environment variables

## AI Maintenance Instructions
- Always update task definition before service
- Use health checks to verify deployments
- Follow https-setup.md for HTTPS configuration
- Check troubleshooting.md before creating new documentation
- Maintain consistent naming: dcasks-{component}-service-{env}
- API service connects to ALB, Job service runs independently

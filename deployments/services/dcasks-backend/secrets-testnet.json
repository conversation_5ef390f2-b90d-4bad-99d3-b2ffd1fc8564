{"Name": "dcasks/testnet/dcasks-backend", "Description": "DCasks Backend testnet environment secrets", "SecretString": {"PORT": "9000", "NODE_ENV": "testnet", "SERVICE_NAME": "API", "MONGODB_URI": "mongodb+srv://testnet-user:<EMAIL>/dcasks-testnet", "REDIS_URL": "redis://testnet-redis:6379", "JWT_SECRET": "testnet-jwt-secret-key", "ADMIN_PRIVATE_KEY": "testnet-admin-private-key", "WOKENS_HOLDER_PRIVATE_KEY": "testnet-wokens-holder-private-key", "BLOCKCHAIN_RPC_URL": "https://testnet-blockchain-rpc.example.com", "EXTERNAL_API_KEY": "testnet-external-api-key"}}
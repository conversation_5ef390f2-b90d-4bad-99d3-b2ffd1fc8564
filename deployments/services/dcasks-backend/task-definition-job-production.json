{"family": "dcasks-job-task-production", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-role", "containerDefinitions": [{"name": "dcasks-job", "image": "dcasks/dcasks-backend:latest", "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dcasks-job-production", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "SERVICE_NAME", "value": "JOB"}], "secrets": [{"name": "PORT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:PORT::"}, {"name": "NODE_ENV", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:NODE_ENV::"}, {"name": "BLOCKCHAIN_NETWORK", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:BLOCKCHAIN_NETWORK::"}, {"name": "DATABASE_URI", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:DATABASE_URI::"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:JWT_SECRET::"}, {"name": "JWT_EXPIRE_TIME", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:JWT_EXPIRE_TIME::"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:AWS_SECRET_ACCESS_KEY::"}, {"name": "ADMIN_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:ADMIN_PRIVATE_KEY::"}, {"name": "WOKENS_HOLDER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:WOKENS_HOLDER_PRIVATE_KEY::"}, {"name": "TWILIO_ACCOUNT_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:TWILIO_ACCOUNT_SID::"}, {"name": "TWILIO_AUTH_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:TWILIO_AUTH_TOKEN::"}, {"name": "TWILIO_SERVICE_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:TWILIO_SERVICE_SID::"}, {"name": "AIRDROP_ENABLE", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:AIRDROP_ENABLE::"}, {"name": "AIRDROP_REGISTER_EMAIL_AMOUNT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:AIRDROP_REGISTER_EMAIL_AMOUNT::"}, {"name": "NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:NETWORK_URL::"}, {"name": "WSS_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:WSS_NETWORK_URL::"}, {"name": "ETH_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:ETH_NETWORK_URL::"}, {"name": "INTERVAL_QUERY_FILTER_EVENTS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:INTERVAL_QUERY_FILTER_EVENTS::"}, {"name": "EVENT_LISTEN_DELAY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/production/dcasks-backend-J0xMdr:EVENT_LISTEN_DELAY::"}]}], "tags": [{"key": "Project", "value": "dcasks"}, {"key": "Environment", "value": "production"}, {"key": "Service", "value": "job"}]}
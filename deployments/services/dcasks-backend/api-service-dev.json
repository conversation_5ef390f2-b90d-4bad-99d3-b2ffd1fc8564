{"serviceName": "dcasks-api-service-dev", "cluster": "dcasks-cluster-nprod", "taskDefinition": "dcasks-api-task-dev:8", "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-03efc31f4ddd3982c", "subnet-091f7ea008ad881be"], "securityGroups": ["sg-0d1af7867738fbfb7"], "assignPublicIp": "ENABLED"}}, "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/dcasks-api-tg-dev-9000/e76c382c45aa36b7", "containerName": "dcasks-api", "containerPort": 9000}], "healthCheckGracePeriodSeconds": 300, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "deploymentCircuitBreaker": {"enable": true, "rollback": true}}, "enableExecuteCommand": true, "tags": [{"key": "Project", "value": "dcasks"}, {"key": "Environment", "value": "dev"}, {"key": "Service", "value": "api"}, {"key": "Cluster", "value": "dcasks-cluster-nprod"}]}
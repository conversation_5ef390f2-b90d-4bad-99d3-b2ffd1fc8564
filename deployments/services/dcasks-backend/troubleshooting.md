# DCasks Backend Troubleshooting

## Common Issues and Solutions

### Service Not Starting

**Issue**: ECS service shows 0 running tasks
**Symptoms**: DesiredCount > 0 but RunningCount = 0

**Solutions**:
1. **Check task definition revision**:
   ```bash
   aws ecs describe-services --cluster {cluster} --services {service-name} --query 'services[0].taskDefinition'
   ```
2. **Update to latest task definition**:
   ```bash
   aws ecs update-service --cluster {cluster} --service {service-name} --task-definition {task-family}:{latest-revision}
   ```

### Docker Image Pull Failures

**Issue**: Tasks fail with `CannotPullContainerError`
**Symptoms**: Tasks stop immediately after starting

**Solutions**:
1. **Verify image URL in task definition**:
   - Correct: `483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:{tag}`
   - Incorrect: `dcasks/dcasks-backend:latest`
2. **Check ECR repository exists**:
   ```bash
   aws ecr describe-repositories --repository-names dcasks-backend
   ```

### Health Check Failures

**Issue**: Tasks running but showing UNHEALTHY status
**Symptoms**: Service running but ALB health checks failing

**Solutions**:
1. **Use wget instead of curl**:
   ```json
   "healthCheck": {
     "command": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9000/health || exit 1"]
   }
   ```
2. **Check health endpoint**:
   ```bash
   curl http://{alb-dns}/health
   ```

### Environment Variable Issues

**Issue**: Application fails to start due to missing environment variables
**Symptoms**: Container logs show "ADMIN_PRIVATE_KEY is not defined"

**Solutions**:
1. **Verify Secrets Manager configuration**:
   ```bash
   aws secretsmanager get-secret-value --secret-id dcasks/{env}/dcasks-backend
   ```
2. **Check task definition secrets section**:
   ```json
   "secrets": [
     {
       "name": "ADMIN_PRIVATE_KEY",
       "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:483805378365:secret:dcasks/{env}/dcasks-backend:ADMIN_PRIVATE_KEY::"
     }
   ]
   ```

### HTTPS Issues

**Issue**: HTTPS endpoint not accessible
**Symptoms**: SSL connection errors or timeouts

**Solutions**:
1. **Check ALB listeners**:
   ```bash
   aws elbv2 describe-listeners --load-balancer-arn {alb-arn}
   ```
2. **Verify certificate status**:
   ```bash
   aws acm describe-certificate --certificate-arn {cert-arn} --query 'Certificate.Status'
   ```
3. **Check security group rules**:
   ```bash
   aws ec2 describe-security-groups --group-ids {alb-sg-id} --query 'SecurityGroups[0].IpPermissions[?FromPort==`443`]'
   ```

### Service Naming Issues

**Issue**: Service not found or incorrect naming
**Symptoms**: AWS CLI commands fail with service not found

**Solutions**:
1. **Use correct naming pattern**:
   - API: `dcasks-api-service-{env}`
   - Job: `dcasks-job-service-{env}`
2. **List existing services**:
   ```bash
   aws ecs list-services --cluster {cluster}
   ```

## Diagnostic Commands

### Check Service Status
```bash
aws ecs describe-services --cluster {cluster} --services {service-name} --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}'
```

### Check Task Health
```bash
aws ecs describe-tasks --cluster {cluster} --tasks {task-id} --query 'tasks[0].{Status:lastStatus,Health:healthStatus}'
```

### View Container Logs
```bash
aws logs get-log-events --log-group-name /ecs/dcasks-{component}-{env} --log-stream-name ecs/dcasks-{component}/{task-id}
```

### Check Target Group Health
```bash
aws elbv2 describe-target-health --target-group-arn {target-group-arn}
```

## Prevention Tips
1. Always test in dev environment first
2. Use health checks to verify deployments
3. Monitor CloudWatch logs during deployments
4. Keep task definition and service definition in sync
5. Follow naming conventions consistently

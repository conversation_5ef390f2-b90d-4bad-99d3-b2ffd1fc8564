{"family": "dcasks-api-task-testnet", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-role", "containerDefinitions": [{"name": "dcasks-api", "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:latest", "essential": true, "portMappings": [{"containerPort": 9000, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dcasks-api-testnet", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "secrets": [{"name": "PORT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:PORT::"}, {"name": "NODE_ENV", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:NODE_ENV::"}, {"name": "BLOCKCHAIN_NETWORK", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:BLOCKCHAIN_NETWORK::"}, {"name": "DATABASE_URI", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:DATABASE_URI::"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:JWT_SECRET::"}, {"name": "JWT_EXPIRE_TIME", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:JWT_EXPIRE_TIME::"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:AWS_SECRET_ACCESS_KEY::"}, {"name": "ADMIN_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:ADMIN_PRIVATE_KEY::"}, {"name": "WOKENS_HOLDER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:WOKENS_HOLDER_PRIVATE_KEY::"}, {"name": "TWILIO_ACCOUNT_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:TWILIO_ACCOUNT_SID::"}, {"name": "TWILIO_AUTH_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:TWILIO_AUTH_TOKEN::"}, {"name": "TWILIO_SERVICE_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:TWILIO_SERVICE_SID::"}, {"name": "AIRDROP_ENABLE", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:AIRDROP_ENABLE::"}, {"name": "AIRDROP_REGISTER_EMAIL_AMOUNT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:AIRDROP_REGISTER_EMAIL_AMOUNT::"}, {"name": "NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:NETWORK_URL::"}, {"name": "WSS_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:WSS_NETWORK_URL::"}, {"name": "ETH_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/testnet/dcasks-backend-5c7Ru4:ETH_NETWORK_URL::"}], "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:9000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}], "tags": [{"key": "Project", "value": "dcasks"}, {"key": "Environment", "value": "testnet"}, {"key": "Service", "value": "api"}]}
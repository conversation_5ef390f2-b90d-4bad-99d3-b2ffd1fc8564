{"Name": "dcasks/production/dcasks-backend", "Description": "DCasks Backend production environment secrets", "SecretString": {"PORT": "9000", "NODE_ENV": "production", "SERVICE_NAME": "API", "MONGODB_URI": "mongodb+srv://prod-user:<EMAIL>/dcasks-production", "REDIS_URL": "redis://prod-redis:6379", "JWT_SECRET": "production-jwt-secret-key", "ADMIN_PRIVATE_KEY": "production-admin-private-key", "WOKENS_HOLDER_PRIVATE_KEY": "production-wokens-holder-private-key", "BLOCKCHAIN_RPC_URL": "https://blockchain-rpc.example.com", "EXTERNAL_API_KEY": "production-external-api-key"}}
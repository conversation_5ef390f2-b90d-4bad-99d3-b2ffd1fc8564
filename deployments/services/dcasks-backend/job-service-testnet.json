{"serviceName": "dcasks-job-service-testnet", "cluster": "dcasks-cluster-nprod", "taskDefinition": "dcasks-job-task-testnet:1", "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-03efc31f4ddd3982c", "subnet-091f7ea008ad881be"], "securityGroups": ["sg-0e70fc67741322cb5"], "assignPublicIp": "ENABLED"}}, "deploymentConfiguration": {"maximumPercent": 100, "minimumHealthyPercent": 0, "deploymentCircuitBreaker": {"enable": true, "rollback": true}}, "enableExecuteCommand": true, "tags": [{"key": "Project", "value": "dcasks"}, {"key": "Environment", "value": "testnet"}, {"key": "Service", "value": "job"}, {"key": "Cluster", "value": "dcasks-cluster-nprod"}]}
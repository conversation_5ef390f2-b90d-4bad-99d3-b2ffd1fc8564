# HTTPS Setup for DCasks Backend

## Overview
This document outlines the HTTPS implementation procedures for DCasks Backend API service, based on the successful dev environment setup.

## Prerequisites
- ACM wildcard certificate: `arn:aws:acm:ap-southeast-1:483805378365:certificate/d8052dbe-c47a-4354-ab8f-296dbc30d7f9`
- ALB with HTTP listener already configured
- Cloudflare DNS management access
- API service running and healthy

## Implementation Steps

### Step 1: Verify Certificate Status
```bash
aws acm describe-certificate --certificate-arn arn:aws:acm:ap-southeast-1:483805378365:certificate/d8052dbe-c47a-4354-ab8f-296dbc30d7f9 --query 'Certificate.Status'
```
**Expected**: Status should be "ISSUED"

### Step 2: Add HTTPS Listener to ALB
```bash
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:loadbalancer/app/dcasks-alb/3e7880e8783b3295 \
  --protocol HTTPS \
  --port 443 \
  --certificates CertificateArn=arn:aws:acm:ap-southeast-1:483805378365:certificate/d8052dbe-c47a-4354-ab8f-296dbc30d7f9 \
  --default-actions Type=forward,TargetGroupArn={target-group-arn}
```

### Step 3: Update Security Group (if needed)
```bash
# Check if HTTPS rule exists
aws ec2 describe-security-groups --group-ids {alb-security-group-id} --query 'SecurityGroups[0].IpPermissions[?FromPort==`443`]'

# Add HTTPS rule if missing
aws ec2 authorize-security-group-ingress --group-id {alb-security-group-id} --protocol tcp --port 443 --cidr 0.0.0.0/0
```

### Step 4: Configure Cloudflare SSL
1. Set SSL/TLS mode to "Full (Strict)"
2. Enable "Always Use HTTPS" (optional)
3. Configure HSTS headers (optional)

### Step 5: Test HTTPS Access
```bash
# Test HTTPS endpoint
curl -v https://{env}-api-ecs.dcasks.co/health

# Verify SSL certificate
openssl s_client -connect {env}-api-ecs.dcasks.co:443 -servername {env}-api-ecs.dcasks.co
```

## Environment-Specific Domains
- **Dev**: `dev-api-ecs.dcasks.co`
- **Testnet**: `testnet-api-ecs.dcasks.co`
- **Production**: `api.dcasks.co`

## Health Check Configuration
Ensure task definition uses wget for health checks:
```json
"healthCheck": {
  "command": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9000/health || exit 1"],
  "interval": 30,
  "timeout": 5,
  "retries": 3,
  "startPeriod": 60
}
```

## Troubleshooting
- **Certificate not found**: Verify certificate ARN and region
- **HTTPS timeout**: Check security group rules for port 443
- **SSL errors**: Verify Cloudflare SSL mode is "Full (Strict)"
- **Health check failures**: Ensure wget command is used instead of curl

## Success Criteria
- ✅ HTTPS endpoint returns 200 OK
- ✅ Valid SSL certificate chain
- ✅ HTTP backward compatibility maintained
- ✅ ECS service remains healthy

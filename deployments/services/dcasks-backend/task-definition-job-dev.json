{"family": "dcasks-job-task-dev", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::************:role/dcasks-ecs-task-role", "containerDefinitions": [{"name": "dcasks-job", "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:dev", "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dcasks-job-dev", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "SERVICE_NAME", "value": "JOB"}], "secrets": [{"name": "PORT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:PORT::"}, {"name": "NODE_ENV", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:NODE_ENV::"}, {"name": "BLOCKCHAIN_NETWORK", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:BLOCKCHAIN_NETWORK::"}, {"name": "DATABASE_URI", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:DATABASE_URI::"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:JWT_SECRET::"}, {"name": "JWT_EXPIRE_TIME", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:JWT_EXPIRE_TIME::"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:AWS_ACCESS_KEY_ID::"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:AWS_SECRET_ACCESS_KEY::"}, {"name": "ADMIN_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:ADMIN_PRIVATE_KEY::"}, {"name": "WOKENS_HOLDER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:WOKENS_HOLDER_PRIVATE_KEY::"}, {"name": "SOLANA_WOKENS_HOLDER_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:SOLANA_WOKENS_HOLDER_PRIVATE_KEY::"}, {"name": "TWILIO_ACCOUNT_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:TWILIO_ACCOUNT_SID::"}, {"name": "TWILIO_AUTH_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:TWILIO_AUTH_TOKEN::"}, {"name": "TWILIO_SERVICE_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:TWILIO_SERVICE_SID::"}, {"name": "AIRDROP_ENABLE", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:AIRDROP_ENABLE::"}, {"name": "AIRDROP_REGISTER_EMAIL_AMOUNT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:AIRDROP_REGISTER_EMAIL_AMOUNT::"}, {"name": "NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:NETWORK_URL::"}, {"name": "WSS_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:WSS_NETWORK_URL::"}, {"name": "ETH_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:ETH_NETWORK_URL::"}, {"name": "CONTRACT_USDT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_USDT::"}, {"name": "CONTRACT_BOTTLE", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_BOTTLE::"}, {"name": "CONTRACT_PROFIT_MODEL", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_PROFIT_MODEL::"}, {"name": "CONTRACT_MARKETPLACE", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_MARKETPLACE::"}, {"name": "CONTRACT_MEMBERSHIP", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_MEMBERSHIP::"}, {"name": "CONTRACT_WOKENS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_WOKENS::"}, {"name": "CONTRACT_MULTI_TOKEN_TRANSFER", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_MULTI_TOKEN_TRANSFER::"}, {"name": "CONTRACT_WHOLE_CASK", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_WHOLE_CASK::"}, {"name": "CONTRACT_TULIP", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:CONTRACT_TULIP::"}, {"name": "BRC420_DEPLOY_IDS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:BRC420_DEPLOY_IDS::"}, {"name": "SOLANA_NETWORK_ENDPOINT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:SOLANA_NETWORK_ENDPOINT::"}, {"name": "SOLANA_COLLECTION_ADDRESS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:SOLANA_COLLECTION_ADDRESS::"}, {"name": "SOLANA_WOKENS_ADDRESS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:SOLANA_WOKENS_ADDRESS::"}, {"name": "BOT_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:BOT_TOKEN::"}, {"name": "INTERVAL_QUERY_FILTER_EVENTS", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:INTERVAL_QUERY_FILTER_EVENTS::"}, {"name": "EVENT_LISTEN_DELAY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:************:secret:dcasks/dev/dcasks-backend-eRBkbm:EVENT_LISTEN_DELAY::"}]}], "tags": [{"key": "Project", "value": "dcasks"}, {"key": "Environment", "value": "dev"}, {"key": "Service", "value": "job"}]}
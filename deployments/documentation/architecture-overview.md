# DCasks Platform Architecture Overview

## High-Level Architecture

```mermaid
graph TB
    subgraph "External"
        CF[Cloudflare]
        Users[Users]
    end
    
    subgraph "AWS Infrastructure"
        subgraph "Application Load Balancer"
            ALB[dcasks-alb<br/>HTTP:80 / HTTPS:443]
        end
        
        subgraph "ECS Clusters"
            subgraph "Non-Production Cluster"
                DevAPI[dcasks-api-service-dev]
                DevJob[dcasks-job-service-dev]
                TestAPI[dcasks-api-service-testnet]
                TestJob[dcasks-job-service-testnet]
                JasmineTest[jasmine-service-testnet]
            end
            
            subgraph "Production Cluster"
                ProdAPI[dcasks-api-service-production<br/>Auto-scaling: 2-5]
                ProdJob[dcasks-job-service-production]
                JasmineProd[jasmine-service-production]
            end
        end
        
        subgraph "Shared Resources"
            VPC[VPC: 10.1.0.0/16]
            Subnets[Public Subnets<br/>ap-southeast-1a/1b]
            SG[Security Groups]
            ACM[ACM Certificate<br/>*.dcasks.co]
        end
    end
    
    subgraph "External Services"
        MongoDB[MongoDB Atlas]
        Blockchain[Blockchain APIs]
        S3[S3 Storage]
    end
    
    Users --> CF
    CF --> ALB
    ALB --> DevAPI
    ALB --> TestAPI
    ALB --> ProdAPI
    ALB --> JasmineTest
    ALB --> JasmineProd
    
    DevAPI --> MongoDB
    TestAPI --> MongoDB
    ProdAPI --> MongoDB
    
    DevJob --> Blockchain
    TestJob --> Blockchain
    ProdJob --> Blockchain
    
    JasmineTest --> S3
    JasmineProd --> S3
```

## Service Architecture

### DCasks Backend
- **API Component**: GraphQL API server with ALB integration
- **Job Component**: Background job processor with external API access
- **Environments**: dev, testnet, production
- **Scaling**: API auto-scales, Job single instance

### Jasmine
- **Web Component**: Next.js frontend application
- **Admin Component**: PayloadJS admin CMS
- **Environments**: dev, testnet, production
- **Storage**: S3 integration for file uploads

## Network Architecture

### VPC Configuration
- **CIDR**: 10.1.0.0/16
- **Subnets**: Public subnets in ap-southeast-1a and ap-southeast-1b
- **Internet Access**: Internet Gateway for direct external connectivity
- **Cost Optimization**: No NAT Gateway required

### Security Groups
- **ALB Security Group**: HTTP (80) and HTTPS (443) from internet
- **API Security Group**: Port 9000 from ALB only
- **Job Security Group**: HTTPS outbound for external APIs

## SSL/TLS Configuration

### Certificate Management
- **Wildcard Certificate**: *.dcasks.co (ACM managed)
- **Validation**: DNS validation via Cloudflare
- **Usage**: ALB HTTPS listeners for all environments

### Cloudflare Integration
- **SSL Mode**: Full (Strict)
- **Features**: DDoS protection, caching, security
- **Domains**: dev-api-ecs.dcasks.co, testnet-api-ecs.dcasks.co, api.dcasks.co

## Environment Strategy

### Development
- **Purpose**: Feature development and testing
- **Resources**: Minimal (1 instance per service)
- **Domain**: dev-*.dcasks.co

### Testnet
- **Purpose**: Integration testing and staging
- **Resources**: Minimal (1 instance per service)
- **Domain**: testnet-*.dcasks.co

### Production
- **Purpose**: Live user traffic
- **Resources**: Auto-scaling (2-5 API instances)
- **Domain**: *.dcasks.co (apex domains)

## Deployment Strategy

### Blue-Green Deployment
- ECS service updates with rolling deployment
- Health checks ensure zero downtime
- Rollback capability via task definition revisions

### Infrastructure as Code
- Service configurations in JSON files
- Shared resources documented and versioned
- Template-driven service creation

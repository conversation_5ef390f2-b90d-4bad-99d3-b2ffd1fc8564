# DCasks Platform Documentation

## How to Use This Folder
1. **Find operational guides**: Use specific .md files for step-by-step procedures
2. **Create new documentation**: Use templates in `templates/` folder
3. **Update architecture**: Edit `architecture-overview.md` with Mermaid diagrams
4. **Document procedures**: Follow established patterns for consistency

## Documentation Files
- `architecture-overview.md`: Platform architecture with Mermaid diagrams
- `deployment-procedures.md`: Step-by-step deployment procedures
- `https-implementation-guide.md`: HTTPS setup procedures (from our work)
- `environment-promotion.md`: Dev→Testnet→Production procedures
- `disaster-recovery.md`: Backup and recovery procedures
- `troubleshooting-guide.md`: Common issues across all services

## Documentation Standards
- Use Mermaid syntax for diagrams
- Include practical commands and examples
- Focus on operational procedures, not theory
- Keep documents concise and actionable
- Cross-reference related documents

## AI Maintenance Instructions
1. Always use templates for new documentation
2. Keep procedures up-to-date with actual implementation
3. Include specific commands and file paths
4. Test procedures before documenting
5. Link related documents explicitly
6. Update architecture diagrams when infrastructure changes

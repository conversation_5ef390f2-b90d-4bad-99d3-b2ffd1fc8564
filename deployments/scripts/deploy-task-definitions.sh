#!/bin/bash

# DCasks Backend ECS Task Definitions Deployment Script
# This script registers all ECS task definitions for the DCasks backend services

set -e

# Configuration
AWS_PROFILE="dcasks-david"
AWS_REGION="ap-southeast-1"
TASK_DEFINITIONS_DIR="$(dirname "$0")/../services/dcasks-backend"

echo "=== DCasks Backend ECS Task Definitions Deployment ==="
echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
echo "AWS Profile: $AWS_PROFILE"
echo "AWS Region: $AWS_REGION"
echo "Task Definitions Directory: $TASK_DEFINITIONS_DIR"
echo ""

# Function to register a task definition
register_task_definition() {
    local task_def_file="$1"
    local task_def_name=$(basename "$task_def_file" .json)
    
    echo "Registering task definition: $task_def_name"
    
    local task_arn=$(aws ecs register-task-definition \
        --cli-input-json "file://$task_def_file" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text)
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully registered: $task_arn"
    else
        echo "❌ Failed to register: $task_def_name"
        exit 1
    fi
    
    echo ""
}

# Register all task definitions
echo "Registering Development Environment Task Definitions..."
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-api-dev.json"
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-job-dev.json"

echo "Registering Testnet Environment Task Definitions..."
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-api-testnet.json"
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-job-testnet.json"

echo "Registering Production Environment Task Definitions..."
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-api-production.json"
register_task_definition "$TASK_DEFINITIONS_DIR/task-definition-job-production.json"

echo "=== Task Definition Deployment Complete ==="
echo "All 6 task definitions have been successfully registered."
echo ""

# Validation
echo "Validating registered task definitions..."
aws ecs list-task-definitions \
    --family-prefix dcasks \
    --profile "$AWS_PROFILE" \
    --region "$AWS_REGION" \
    --query 'taskDefinitionArns' \
    --output table

echo ""
echo "✅ DCasks Backend ECS Task Definitions deployment completed successfully!"

#!/bin/bash

# DCasks Backend Environment-specific Build Helper
# This script provides shortcuts for building images for specific environments

set -e

SCRIPT_DIR="$(dirname "$0")"
BUILD_SCRIPT="$SCRIPT_DIR/build-and-push-image.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "DCasks Backend Environment-specific Build Helper"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  dev         Build for development environment"
    echo "  testnet     Build for testnet environment"
    echo "  production  Build for production environment"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --commit <sha>    Specify commit SHA (default: auto-detect)"
    echo "  --dry-run         Show what would be built without building"
    echo ""
    echo "Examples:"
    echo "  $0 dev                           # Build dev with auto-detected commit"
    echo "  $0 testnet --commit abc123f      # Build testnet with specific commit"
    echo "  $0 production --dry-run          # Show what would be built for production"
    echo ""
    echo "Tag naming convention:"
    echo "  Primary: {environment}                    (e.g., dev, testnet, latest)"
    echo "  Commit:  {environment}-{commit_sha}       (e.g., dev-abc123f)"
    echo "  Full:    {environment}-{commit_sha}-{timestamp}"
}

# Function to get current git info
get_git_info() {
    local commit_sha=""
    local branch_name=""

    if git rev-parse --git-dir > /dev/null 2>&1; then
        commit_sha=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
        branch_name=$(git branch --show-current 2>/dev/null || echo "unknown")
    else
        commit_sha="unknown"
        branch_name="unknown"
    fi

    echo "$commit_sha"
}

# Function to validate environment
validate_environment() {
    local env="$1"
    case "$env" in
        "dev"|"testnet"|"production")
            return 0
            ;;
        *)
            print_error "Invalid environment: $env"
            echo "Valid environments: dev, testnet, production"
            return 1
            ;;
    esac
}

# Function to build for environment
build_for_environment() {
    local environment="$1"
    local commit_sha="$2"
    local dry_run="$3"
    
    # Validate environment
    if ! validate_environment "$environment"; then
        exit 1
    fi
    
    # Get git info if commit not specified
    if [ -z "$commit_sha" ]; then
        commit_sha=$(get_git_info)
        local branch_name=$(git branch --show-current 2>/dev/null || echo "unknown")

        print_info "Auto-detected commit: $commit_sha (branch: $branch_name)"
    fi
    
    # Show what will be built
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local env_tag=""
    case "$environment" in
        "dev") env_tag="dev" ;;
        "testnet") env_tag="testnet" ;;
        "production") env_tag="latest" ;;
    esac
    
    echo ""
    print_info "Build Configuration:"
    echo "  Environment: $environment"
    echo "  Commit SHA: $commit_sha"
    echo "  Timestamp: $timestamp"
    echo ""
    echo "Tags that will be created:"
    echo "  Primary: $env_tag"
    echo "  Commit:  $env_tag-$commit_sha"
    echo "  Full:    $env_tag-$commit_sha-$timestamp"
    echo ""
    
    if [ "$dry_run" = "true" ]; then
        print_warning "DRY RUN - No actual build will be performed"
        echo ""
        echo "Command that would be executed:"
        echo "  $BUILD_SCRIPT $environment $commit_sha"
        return 0
    fi
    
    # Confirm for production builds
    if [ "$environment" = "production" ]; then
        echo -n "Are you sure you want to build for PRODUCTION? (y/N): "
        read -r confirmation
        if [[ ! "$confirmation" =~ ^[Yy]$ ]]; then
            print_warning "Production build cancelled"
            exit 0
        fi
    fi
    
    # Execute the build
    print_info "Starting build for $environment environment..."
    if "$BUILD_SCRIPT" "$environment" "$commit_sha"; then
        print_success "Build completed successfully for $environment"
    else
        print_error "Build failed for $environment"
        exit 1
    fi
}

# Main script logic
main() {
    local command="$1"
    local commit_sha=""
    local dry_run="false"
    
    # Parse arguments
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --commit)
                commit_sha="$2"
                shift 2
                ;;
            --dry-run)
                dry_run="true"
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Handle commands
    case "$command" in
        "dev"|"testnet"|"production")
            build_for_environment "$command" "$commit_sha" "$dry_run"
            ;;
        "help"|"--help"|"-h"|"")
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Check if build script exists
if [ ! -f "$BUILD_SCRIPT" ]; then
    print_error "Build script not found: $BUILD_SCRIPT"
    exit 1
fi

# Run main function
main "$@"

# DCasks Backend Docker Image Build Scripts

This directory contains scripts for building and pushing Docker images for the DCasks backend service to Amazon ECR with environment-specific tagging.

## Tag Naming Convention

The new tagging convention combines both COMMIT_SHA and environment-based tags for better traceability and deployment management:

### Tag Format
- **Primary Tag**: `{environment}` (e.g., `dev`, `testnet`, `latest`)
- **Commit Tag**: `{environment}-{commit_sha}` (e.g., `dev-abc123f`)
- **Full Tag**: `{environment}-{commit_sha}-{timestamp}` (e.g., `dev-abc123f-20250609-143022`)

### Environment Mapping
- `dev` → `dev`
- `testnet` → `testnet`
- `production` → `latest`

## Scripts

### 1. build-and-push-image.sh
Main script for building and pushing Docker images to ECR.

**Usage:**
```bash
./build-and-push-image.sh [ENVIRONMENT] [COMMIT_SHA]
```

**Arguments:**
- `ENVIRONMENT`: Target environment (dev, testnet, production) [default: dev]
- `COMMIT_SHA`: Git commit SHA (short) [default: auto-detect from git]

**Examples:**
```bash
# Build for dev with auto-detected commit
./build-and-push-image.sh

# Build for dev with auto-detected commit (explicit)
./build-and-push-image.sh dev

# Build for testnet with specific commit
./build-and-push-image.sh testnet abc123f

# Build for production with current commit
./build-and-push-image.sh production $(git rev-parse --short HEAD)

# Show help
./build-and-push-image.sh --help
```

### 2. build-for-env.sh
Helper script with environment-specific shortcuts and additional features.

**Usage:**
```bash
./build-for-env.sh <command> [options]
```

**Commands:**
- `dev`: Build for development environment
- `testnet`: Build for testnet environment
- `production`: Build for production environment
- `help`: Show help message

**Options:**
- `--commit <sha>`: Specify commit SHA (default: auto-detect)
- `--dry-run`: Show what would be built without building

**Examples:**
```bash
# Build for dev with auto-detected commit
./build-for-env.sh dev

# Build for testnet with specific commit
./build-for-env.sh testnet --commit abc123f

# Dry run for production (shows what would be built)
./build-for-env.sh production --dry-run

# Show help
./build-for-env.sh help
```

## Features

### Automatic Git Integration
- Auto-detects current commit SHA if not specified
- Shows current branch information
- Validates git repository status

### Multi-Tag Support
Each build creates three tags for maximum flexibility:
1. **Primary tag** for environment-based deployments
2. **Commit tag** for specific version tracking
3. **Full tag** for complete traceability

### Environment-Specific Updates
- Automatically updates task definitions for the target environment
- Creates backups of task definition files
- Handles both old and new image URI patterns

### Safety Features
- Production build confirmation prompt
- Dry-run mode for testing
- Comprehensive error handling and validation
- Colored output for better readability

## GitLab CI Integration

The tagging convention is compatible with existing GitLab CI variables:

```yaml
# GitLab CI equivalent
DOCKER_TAG: dev          # Maps to PRIMARY_TAG
APP_VERSION: abc123f     # Maps to COMMIT_SHA
```

## ECR Repository

**Repository URI**: `483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend`

## Prerequisites

1. **AWS CLI** configured with `dcasks-david` profile
2. **Docker** installed and running
3. **Git** repository (for auto-detection)
4. **ECR repository** created (`dcasks-backend`)

## Task Definition Updates

The scripts automatically update task definitions in `deployments/services/dcasks-backend/` based on the target environment:

- `dev` → Updates `*dev*.json` files
- `testnet` → Updates `*testnet*.json` files
- `production` → Updates `*production*.json` files

## Workflow

1. **Build and Push Image**:
   ```bash
   ./build-for-env.sh dev
   ```

2. **Register Updated Task Definitions**:
   ```bash
   ./deploy-task-definitions.sh
   ```

3. **Deploy ECS Services**:
   ```bash
   ./deploy-ecs-services.sh
   ```

## Migration from Old Tagging

The scripts handle migration from the old tagging system:
- Old: `dcasks/dcasks-backend:latest`
- New: `483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:dev`

Task definitions are automatically updated to use the new ECR URIs.

## Troubleshooting

### Common Issues

1. **Docker not running**:
   ```
   ❌ Docker is not running. Please start Docker and try again.
   ```
   Solution: Start Docker Desktop or Docker daemon

2. **ECR authentication failed**:
   ```
   ❌ Failed to authenticate with ECR
   ```
   Solution: Check AWS CLI configuration and permissions

3. **Git not found**:
   ```
   commit_sha: unknown
   ```
   Solution: Run from within a git repository or specify commit manually

4. **Task definition not found**:
   ```
   ⚠️  No task definitions found for {environment} environment
   ```
   Solution: Ensure task definition files exist in `deployments/services/dcasks-backend/`

### Debug Mode

For detailed output, run with bash debug mode:
```bash
bash -x ./build-and-push-image.sh dev
```

## Security Notes

- Uses non-root user in Docker container
- ECR authentication via AWS CLI
- Task definitions are backed up before modification
- Production builds require manual confirmation

#!/bin/bash

# DCasks Backend ALB Listener Rules Setup
# This script creates the necessary ALB listener rules for routing traffic to different environments

set -e

# Configuration
AWS_PROFILE="dcasks-david"
AWS_REGION="ap-southeast-1"
ALB_ARN="arn:aws:elasticloadbalancing:ap-southeast-1:************:loadbalancer/app/dcasks-alb/3e7880e8783b3295"
LISTENER_ARN="arn:aws:elasticloadbalancing:ap-southeast-1:************:listener/app/dcasks-alb/3e7880e8783b3295/cc3c914e6df2a4f3"

# Target Group ARNs
PROD_TG_ARN="arn:aws:elasticloadbalancing:ap-southeast-1:************:targetgroup/dcasks-api-tg-production/5554ae488404a141"
DEV_TG_ARN="arn:aws:elasticloadbalancing:ap-southeast-1:************:targetgroup/dcasks-api-tg-dev/799644404fe3e595"
TESTNET_TG_ARN="arn:aws:elasticloadbalancing:ap-southeast-1:************:targetgroup/dcasks-api-tg-testnet/738b47ea62b90b80"

echo "=== DCasks ALB Listener Rules Setup ==="
echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
echo "AWS Profile: $AWS_PROFILE"
echo "AWS Region: $AWS_REGION"
echo "ALB ARN: $ALB_ARN"
echo "Listener ARN: $LISTENER_ARN"
echo ""

# Function to create listener rule with retry
create_listener_rule() {
    local priority="$1"
    local condition_field="$2"
    local condition_value="$3"
    local target_group_arn="$4"
    local environment="$5"
    
    echo "Creating listener rule for $environment environment"
    echo "  Priority: $priority"
    echo "  Condition: $condition_field = $condition_value"
    echo "  Target Group: $target_group_arn"
    
    # Try to create the rule
    local rule_arn=$(aws elbv2 create-rule \
        --listener-arn "$LISTENER_ARN" \
        --priority "$priority" \
        --conditions Field="$condition_field",Values="$condition_value" \
        --actions Type=forward,TargetGroupArn="$target_group_arn" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'Rules[0].RuleArn' \
        --output text 2>/dev/null)
    
    if [ $? -eq 0 ] && [ "$rule_arn" != "None" ]; then
        echo "✅ Successfully created rule: $rule_arn"
    else
        echo "⚠️  Rule creation failed or rule already exists for priority $priority"
        # Try to find existing rule
        echo "   Checking for existing rules..."
    fi
    
    echo ""
}

echo "=== Creating ALB Listener Rules ==="
echo ""

# Create rules for different environments using path-based routing
# Note: Production uses default action, so no rule needed

echo "Creating development environment rule..."
create_listener_rule "200" "path-pattern" "/dev/*" "$DEV_TG_ARN" "development"

echo "Creating testnet environment rule..."
create_listener_rule "300" "path-pattern" "/testnet/*" "$TESTNET_TG_ARN" "testnet"

echo "=== Verifying ALB Configuration ==="
echo ""

# Verify target groups are now associated with load balancer
echo "Checking target group associations..."

for env in "dev" "testnet" "production"; do
    case $env in
        "dev")
            tg_arn="$DEV_TG_ARN"
            ;;
        "testnet")
            tg_arn="$TESTNET_TG_ARN"
            ;;
        "production")
            tg_arn="$PROD_TG_ARN"
            ;;
    esac
    
    echo "Checking $env target group..."
    lb_arns=$(aws elbv2 describe-target-groups \
        --target-group-arns "$tg_arn" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'TargetGroups[0].LoadBalancerArns' \
        --output text 2>/dev/null)
    
    if [ "$lb_arns" != "None" ] && [ -n "$lb_arns" ]; then
        echo "✅ $env target group is associated with load balancer"
    else
        echo "❌ $env target group is NOT associated with load balancer"
    fi
done

echo ""
echo "=== ALB Rules Setup Complete ==="
echo "Target groups should now be associated with the ALB"
echo "ECS services can now be created with load balancer integration"
echo ""

#!/bin/bash

# DCasks Backend Docker Image Build and <PERSON>ush Script
# This script builds the Docker image and pushes it to Amazon ECR

set -e

# Configuration
AWS_PROFILE="dcasks-david"
AWS_REGION="ap-southeast-1"
AWS_ACCOUNT_ID="************"
ECR_REPOSITORY="dcasks-backend"
ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY}"
DOCKERFILE_PATH="apps/dcasks-backend"

# Show usage if help is requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0 [ENVIRONMENT] [COMMIT_SHA]"
    echo ""
    echo "Arguments:"
    echo "  ENVIRONMENT  Target environment (dev, testnet, production) [default: dev]"
    echo "  COMMIT_SHA   Git commit SHA (short) [default: auto-detect from git]"
    echo ""
    echo "Examples:"
    echo "  $0                           # Build for dev with auto-detected commit"
    echo "  $0 dev                       # Build for dev with auto-detected commit"
    echo "  $0 testnet abc123f           # Build for testnet with specific commit"
    echo "  $0 production \$(git rev-parse --short HEAD)"
    echo ""
    echo "Tag naming convention:"
    echo "  Primary: {environment}"
    echo "  Commit:  {environment}-{commit_sha}"
    echo "  Full:    {environment}-{commit_sha}-{timestamp}"
    echo ""
    exit 0
fi

# Environment and tagging configuration
ENVIRONMENT="${1:-dev}"  # Default to dev if not specified
COMMIT_SHA="${2:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}"
TIMESTAMP="$(date +%Y%m%d-%H%M%S)"

# Tag naming convention: environment-commitsha-timestamp
case "$ENVIRONMENT" in
    "dev")
        ENV_TAG="dev"
        ;;
    "testnet")
        ENV_TAG="testnet"
        ;;
    "production"|"prod"|"main")
        ENV_TAG="latest"
        ENVIRONMENT="production"
        ;;
    *)
        echo "❌ Invalid environment: $ENVIRONMENT"
        echo "Valid environments: dev, testnet, production"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

# Primary tags
PRIMARY_TAG="${ENV_TAG}"
COMMIT_TAG="${ENV_TAG}-${COMMIT_SHA}"
FULL_TAG="${ENV_TAG}-${COMMIT_SHA}-${TIMESTAMP}"

echo "=== DCasks Backend Docker Image Build and Push ==="
echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
echo "AWS Profile: $AWS_PROFILE"
echo "AWS Region: $AWS_REGION"
echo "ECR Repository: $ECR_URI"
echo "Dockerfile Path: $DOCKERFILE_PATH"
echo "Environment: $ENVIRONMENT"
echo "Commit SHA: $COMMIT_SHA"
echo ""
echo "Image Tags:"
echo "  Primary Tag: $PRIMARY_TAG"
echo "  Commit Tag: $COMMIT_TAG"
echo "  Full Tag: $FULL_TAG"
echo ""



# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi
    echo "✅ Docker is running"
}

# Function to authenticate with ECR
ecr_login() {
    echo "Authenticating with Amazon ECR..."
    
    aws ecr get-login-password \
        --region "$AWS_REGION" \
        --profile "$AWS_PROFILE" | \
    docker login \
        --username AWS \
        --password-stdin \
        "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully authenticated with ECR"
    else
        echo "❌ Failed to authenticate with ECR"
        exit 1
    fi
    echo ""
}

# Function to build Docker image
build_image() {
    echo "Building Docker image..."
    echo "Build context: $DOCKERFILE_PATH"
    echo "Building with multiple tags:"
    echo "  - $ECR_URI:$PRIMARY_TAG"
    echo "  - $ECR_URI:$COMMIT_TAG"
    echo "  - $ECR_URI:$FULL_TAG"

    # Change to the dcasks-backend directory for build context
    cd "$DOCKERFILE_PATH"

    # Build the Docker image with multiple tags
    docker build \
        --platform linux/amd64 \
        --build-arg SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN \
        --tag "$ECR_URI:$PRIMARY_TAG" \
        --tag "$ECR_URI:$COMMIT_TAG" \
        --tag "$ECR_URI:$FULL_TAG" \
        .

    if [ $? -eq 0 ]; then
        echo "✅ Successfully built Docker image with all tags"
    else
        echo "❌ Failed to build Docker image"
        exit 1
    fi

    # Return to original directory
    cd - > /dev/null
    echo ""
}

# Function to push image to ECR
push_image() {
    echo "Pushing Docker images to ECR..."

    # Push all tags
    local tags=("$PRIMARY_TAG" "$COMMIT_TAG" "$FULL_TAG")
    local failed_pushes=0

    for tag in "${tags[@]}"; do
        echo "Pushing tag: $tag"
        if docker push "$ECR_URI:$tag"; then
            echo "✅ Successfully pushed: $ECR_URI:$tag"
        else
            echo "❌ Failed to push: $ECR_URI:$tag"
            ((failed_pushes++))
        fi
    done

    if [ $failed_pushes -eq 0 ]; then
        echo "✅ All images pushed successfully"
    elif [ $failed_pushes -lt ${#tags[@]} ]; then
        echo "⚠️  Some images failed to push ($failed_pushes/${#tags[@]})"
    else
        echo "❌ All image pushes failed"
        exit 1
    fi
    echo ""
}

# Function to verify image in ECR
verify_image() {
    echo "Verifying images in ECR..."

    # Get all image tags from ECR
    local all_tags=$(aws ecr describe-images \
        --repository-name "$ECR_REPOSITORY" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'imageDetails[*].imageTags[*]' \
        --output text | tr '\t' '\n' | sort -u)

    local tags_to_verify=("$PRIMARY_TAG" "$COMMIT_TAG" "$FULL_TAG")
    local verified_count=0

    echo "Checking for pushed tags in ECR:"
    for tag in "${tags_to_verify[@]}"; do
        if echo "$all_tags" | grep -q "^${tag}$"; then
            echo "✅ Found: $tag"
            ((verified_count++))
        else
            echo "❌ Missing: $tag"
        fi
    done

    if [ $verified_count -eq ${#tags_to_verify[@]} ]; then
        echo "✅ All images successfully verified in ECR"
    elif [ $verified_count -gt 0 ]; then
        echo "⚠️  Some images verified ($verified_count/${#tags_to_verify[@]})"
    else
        echo "❌ No images found in ECR"
        exit 1
    fi

    echo ""
    echo "All available tags in repository:"
    echo "$all_tags" | sed 's/^/  - /'
    echo ""
}

# Function to update task definitions with new image URI
update_task_definitions() {
    echo "Updating task definitions with new image URI..."

    local task_def_dir="deployments/services/dcasks-backend"
    local old_patterns=("dcasks/dcasks-backend:latest" "************.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:*")
    local new_image="$ECR_URI:$PRIMARY_TAG"

    echo "Replacing image URI in task definitions:"
    echo "  New image: $new_image"
    echo "  Environment: $ENVIRONMENT"
    echo ""

    # Determine which task definitions to update based on environment
    local task_pattern=""
    case "$ENVIRONMENT" in
        "dev")
            task_pattern="*dev*.json"
            ;;
        "testnet")
            task_pattern="*testnet*.json"
            ;;
        "production")
            task_pattern="*production*.json"
            ;;
    esac

    # Update task definition files for the specific environment
    local updated_count=0
    for task_def_file in $task_def_dir/$task_pattern; do
        if [ -f "$task_def_file" ]; then
            echo "  Updating: $(basename "$task_def_file")"

            # Create backup
            cp "$task_def_file" "$task_def_file.backup"

            # Replace image URI - handle both old and new patterns
            sed -i.tmp \
                -e "s|dcasks/dcasks-backend:[^\"]*|$new_image|g" \
                -e "s|************\.dkr\.ecr\.ap-southeast-1\.amazonaws\.com/dcasks-backend:[^\"]*|$new_image|g" \
                "$task_def_file"
            rm "$task_def_file.tmp"

            echo "    ✅ Updated $(basename "$task_def_file")"
            ((updated_count++))
        fi
    done

    if [ $updated_count -gt 0 ]; then
        echo "✅ Updated $updated_count task definition(s) for $ENVIRONMENT environment"
    else
        echo "⚠️  No task definitions found for $ENVIRONMENT environment"
    fi
    echo ""
}

# Main execution
echo "=== Step 1: Pre-flight Checks ==="
check_docker

echo "=== Step 2: ECR Authentication ==="
ecr_login

echo "=== Step 3: Build Docker Image ==="
build_image

echo "=== Step 4: Push Image to ECR ==="
push_image

echo "=== Step 5: Verify Image in ECR ==="
verify_image

echo "=== Step 6: Update Task Definitions ==="
update_task_definitions

echo "=== Docker Image Build and Push Complete ==="
echo "Environment: $ENVIRONMENT"
echo "Commit SHA: $COMMIT_SHA"
echo ""
echo "Built and pushed images:"
echo "  Primary: $ECR_URI:$PRIMARY_TAG"
echo "  Commit:  $ECR_URI:$COMMIT_TAG"
echo "  Full:    $ECR_URI:$FULL_TAG"
echo ""
echo "Next steps:"
echo "1. Register updated task definitions:"
echo "   ./deployments/scripts/deploy-task-definitions.sh"
echo "2. Update ECS services to use new task definitions"
echo "3. Monitor service deployment and health"
echo ""
echo "GitLab CI equivalent tags:"
echo "  DOCKER_TAG=$PRIMARY_TAG"
echo "  APP_VERSION=$COMMIT_SHA"
echo ""
echo "✅ Build and push completed successfully!"

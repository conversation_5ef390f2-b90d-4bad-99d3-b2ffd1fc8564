#!/bin/bash

# DCasks Backend Complete Task Definitions Generator
# This script generates complete task definitions with all required environment variables

set -e

# Configuration
AWS_PROFILE="dcasks-david"
AWS_REGION="ap-southeast-1"
SECRET_BASE_ARN="arn:aws:secretsmanager:ap-southeast-1:483805378365:secret"
TASK_DEF_DIR="deployments/services/dcasks-backend"

echo "=== DCasks Backend Complete Task Definitions Generator ==="
echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
echo ""

# All required environment variables based on .env.example and application code
ENV_VARS=(
    "PORT"
    "NODE_ENV"
    "BLOCKCHAIN_NETWORK"
    "DATABASE_URI"
    "JWT_SECRET"
    "JWT_EXPIRE_TIME"
    "AWS_ACCESS_KEY_ID"
    "AWS_SECRET_ACCESS_KEY"
    "ADMIN_PRIVATE_KEY"
    "WOKENS_HOLDER_PRIVATE_KEY"
    "SOLANA_WOKENS_HOLDER_PRIVATE_KEY"
    "TWILIO_ACCOUNT_SID"
    "TWILIO_AUTH_TOKEN"
    "TWILIO_SERVICE_SID"
    "AIRDROP_ENABLE"
    "AIRDROP_REGISTER_EMAIL_AMOUNT"
    "NETWORK_URL"
    "WSS_NETWORK_URL"
    "ETH_NETWORK_URL"
    "CONTRACT_USDT"
    "CONTRACT_BOTTLE"
    "CONTRACT_PROFIT_MODEL"
    "CONTRACT_MARKETPLACE"
    "CONTRACT_MEMBERSHIP"
    "CONTRACT_WOKENS"
    "CONTRACT_MULTI_TOKEN_TRANSFER"
    "CONTRACT_WHOLE_CASK"
    "CONTRACT_TULIP"
    "BRC420_DEPLOY_IDS"
    "SOLANA_NETWORK_ENDPOINT"
    "SOLANA_COLLECTION_ADDRESS"
    "SOLANA_WOKENS_ADDRESS"
    "BOT_TOKEN"
    "INTERVAL_QUERY_FILTER_EVENTS"
    "EVENT_LISTEN_DELAY"
)

# Function to generate secrets section for task definition
generate_secrets_section() {
    local environment="$1"
    local secret_suffix=""
    
    case "$environment" in
        "dev") secret_suffix="eRBkbm" ;;
        "testnet") secret_suffix="testnet-suffix" ;;  # Update with actual suffix
        "production") secret_suffix="prod-suffix" ;;  # Update with actual suffix
    esac
    
    echo "      \"secrets\": ["
    
    local first=true
    for var in "${ENV_VARS[@]}"; do
        if [ "$first" = true ]; then
            first=false
        else
            echo ","
        fi
        echo -n "        {"
        echo -n "\"name\": \"$var\", "
        echo -n "\"valueFrom\": \"$SECRET_BASE_ARN:dcasks/$environment/dcasks-backend-$secret_suffix:$var::\""
        echo -n "}"
    done
    
    echo ""
    echo "      ],"
}

# Function to generate complete API task definition
generate_api_task_definition() {
    local environment="$1"
    local cpu="$2"
    local memory="$3"
    local image_uri="$4"
    
    cat > "$TASK_DEF_DIR/task-definition-api-$environment.json" << EOF
{
  "family": "dcasks-api-task-$environment",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "$cpu",
  "memory": "$memory",
  "executionRoleArn": "arn:aws:iam::483805378365:role/dcasks-ecs-task-execution-role",
  "taskRoleArn": "arn:aws:iam::483805378365:role/dcasks-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "dcasks-api",
      "image": "$image_uri",
      "essential": true,
      "portMappings": [
        {
          "containerPort": 9000,
          "protocol": "tcp"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/dcasks-api-$environment",
          "awslogs-region": "ap-southeast-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "environment": [
        {
          "name": "SERVICE_NAME",
          "value": "API"
        }
      ],
$(generate_secrets_section "$environment")
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:9000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ],
  "tags": [
    {
      "key": "Project",
      "value": "dcasks"
    },
    {
      "key": "Environment",
      "value": "$environment"
    },
    {
      "key": "Service",
      "value": "api"
    }
  ]
}
EOF
}

# Function to generate complete Job task definition
generate_job_task_definition() {
    local environment="$1"
    local cpu="$2"
    local memory="$3"
    local image_uri="$4"
    
    cat > "$TASK_DEF_DIR/task-definition-job-$environment.json" << EOF
{
  "family": "dcasks-job-task-$environment",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "$cpu",
  "memory": "$memory",
  "executionRoleArn": "arn:aws:iam::483805378365:role/dcasks-ecs-task-execution-role",
  "taskRoleArn": "arn:aws:iam::483805378365:role/dcasks-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "dcasks-job",
      "image": "$image_uri",
      "essential": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/dcasks-job-$environment",
          "awslogs-region": "ap-southeast-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "environment": [
        {
          "name": "SERVICE_NAME",
          "value": "JOB"
        }
      ],
$(generate_secrets_section "$environment")
    }
  ],
  "tags": [
    {
      "key": "Project",
      "value": "dcasks"
    },
    {
      "key": "Environment",
      "value": "$environment"
    },
    {
      "key": "Service",
      "value": "job"
    }
  ]
}
EOF
}

echo "=== Generating Complete Task Definitions ==="
echo ""

# Generate for development environment
echo "Generating development task definitions..."
generate_api_task_definition "dev" "256" "512" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:dev"
generate_job_task_definition "dev" "256" "512" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:dev"
echo "✅ Development task definitions generated"

# Generate for testnet environment
echo "Generating testnet task definitions..."
generate_api_task_definition "testnet" "256" "512" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:testnet"
generate_job_task_definition "testnet" "256" "512" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:testnet"
echo "✅ Testnet task definitions generated"

# Generate for production environment
echo "Generating production task definitions..."
generate_api_task_definition "production" "512" "1024" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:latest"
generate_job_task_definition "production" "512" "1024" "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:latest"
echo "✅ Production task definitions generated"

echo ""
echo "=== Task Definition Generation Complete ==="
echo "Generated files:"
echo "  - task-definition-api-dev.json"
echo "  - task-definition-job-dev.json"
echo "  - task-definition-api-testnet.json"
echo "  - task-definition-job-testnet.json"
echo "  - task-definition-api-production.json"
echo "  - task-definition-job-production.json"
echo ""
echo "Next steps:"
echo "1. Review the generated task definitions"
echo "2. Register them with ECS: ./deploy-task-definitions.sh"
echo "3. Update ECS services to use new task definitions"
echo ""
echo "✅ Generation completed successfully!"

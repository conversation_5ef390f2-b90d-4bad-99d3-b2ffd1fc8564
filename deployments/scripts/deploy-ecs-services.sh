#!/bin/bash

# DCasks Backend ECS Services Deployment Script
# Phase 9: Deploy all ECS services for API and Job services across environments

set -e

# Configuration
AWS_PROFILE="dcasks-david"
AWS_REGION="ap-southeast-1"
SERVICE_DEFINITIONS_DIR="$(dirname "$0")/../services/dcasks-backend"

echo "=== DCasks Backend ECS Services Deployment - Phase 9 ==="
echo "Timestamp: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
echo "AWS Profile: $AWS_PROFILE"
echo "AWS Region: $AWS_REGION"
echo "Service Definitions Directory: $SERVICE_DEFINITIONS_DIR"
echo ""

# Function to create an ECS service
create_ecs_service() {
    local service_def_file="$1"
    local service_name=$(basename "$service_def_file" .json)
    
    echo "Creating ECS service: $service_name"
    echo "Service definition file: $service_def_file"
    
    # Extract cluster name from service definition for validation
    local cluster_name=$(jq -r '.cluster' "$service_def_file")
    echo "Target cluster: $cluster_name"
    
    # Create the service
    local service_arn=$(aws ecs create-service \
        --cli-input-json "file://$service_def_file" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'service.serviceArn' \
        --output text)
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully created service: $service_arn"
        echo "   Service Name: $service_name"
        echo "   Cluster: $cluster_name"
    else
        echo "❌ Failed to create service: $service_name"
        exit 1
    fi
    
    echo ""
}

# Function to wait for service stability
wait_for_service_stability() {
    local cluster_name="$1"
    local service_name="$2"
    
    echo "Waiting for service stability: $service_name in cluster $cluster_name"
    
    aws ecs wait services-stable \
        --cluster "$cluster_name" \
        --services "$service_name" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION"
    
    if [ $? -eq 0 ]; then
        echo "✅ Service $service_name is stable"
    else
        echo "⚠️  Service $service_name stability check timed out (this is normal for initial deployment)"
    fi
    
    echo ""
}

# Function to check service status
check_service_status() {
    local cluster_name="$1"
    local service_name="$2"
    
    echo "Checking service status: $service_name"
    
    aws ecs describe-services \
        --cluster "$cluster_name" \
        --services "$service_name" \
        --profile "$AWS_PROFILE" \
        --region "$AWS_REGION" \
        --query 'services[0].{ServiceName:serviceName,Status:status,RunningCount:runningCount,DesiredCount:desiredCount,TaskDefinition:taskDefinition}' \
        --output table
    
    echo ""
}

echo "=== Phase 9.1: Deploy Development Environment Services ==="
echo "Target Cluster: dcasks-cluster-nprod"
echo ""

# Deploy development services
create_ecs_service "$SERVICE_DEFINITIONS_DIR/api-service-dev.json"
create_ecs_service "$SERVICE_DEFINITIONS_DIR/job-service-dev.json"

echo "=== Phase 9.2: Deploy Testnet Environment Services ==="
echo "Target Cluster: dcasks-cluster-nprod"
echo ""

# Deploy testnet services
create_ecs_service "$SERVICE_DEFINITIONS_DIR/api-service-testnet.json"
create_ecs_service "$SERVICE_DEFINITIONS_DIR/job-service-testnet.json"

echo "=== Phase 9.3: Deploy Production Environment Services ==="
echo "Target Cluster: dcasks-cluster-prod"
echo ""

# Deploy production services
create_ecs_service "$SERVICE_DEFINITIONS_DIR/api-service-production.json"
create_ecs_service "$SERVICE_DEFINITIONS_DIR/job-service-production.json"

echo "=== Phase 9.4: Service Status Validation ==="
echo ""

# Check all service statuses
echo "Development Environment Services:"
check_service_status "dcasks-cluster-nprod" "dcasks-api-service-dev"
check_service_status "dcasks-cluster-nprod" "dcasks-job-service-dev"

echo "Testnet Environment Services:"
check_service_status "dcasks-cluster-nprod" "dcasks-api-service-testnet"
check_service_status "dcasks-cluster-nprod" "dcasks-job-service-testnet"

echo "Production Environment Services:"
check_service_status "dcasks-cluster-prod" "dcasks-api-service-production"
check_service_status "dcasks-cluster-prod" "dcasks-job-service-production"

echo "=== Phase 9 ECS Services Deployment Complete ==="
echo "All 6 ECS services have been successfully created."
echo ""
echo "Next Steps:"
echo "1. Monitor service health and task startup"
echo "2. Verify ALB target group registrations for API services"
echo "3. Test external connectivity for Job services"
echo "4. Configure auto-scaling policies"
echo "5. Proceed to Phase 10: Production Migration & Testing"
echo ""
echo "✅ Phase 9 completed successfully!"

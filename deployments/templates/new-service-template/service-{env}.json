{"serviceName": "{service-name}-service-{env}", "cluster": "dcasks-cluster-{cluster}", "taskDefinition": "{service-name}-task-{env}", "desiredCount": 1, "launchType": "FARGATE", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-0123456789abcdef1", "subnet-0123456789abcdef2"], "securityGroups": ["sg-0123456789abcdef1"], "assignPublicIp": "ENABLED"}}, "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:483805378365:targetgroup/{service-name}-tg-{env}/abcdef1234567890", "containerName": "{service-name}", "containerPort": 9000}], "healthCheckGracePeriodSeconds": 300, "enableExecuteCommand": true, "tags": [{"key": "Environment", "value": "{env}"}, {"key": "Service", "value": "{service-name}"}]}
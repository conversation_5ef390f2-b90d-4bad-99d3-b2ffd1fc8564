# {SERVICE-NAME} Service

## Service Components
- **{COMPONENT-1}**: Description of first component
- **{COMPONENT-2}**: Description of second component (if applicable)

## Quick Commands
```bash
# Deploy entire service to dev
../scripts/deploy.sh --service {service-name} --env dev

# Deploy specific component to production
../scripts/deploy.sh --service {service-name} --component {component} --env production

# Check service health
../scripts/health-check.sh --service {service-name} --env dev
```

## File Structure
- `{component}-service-{env}.json`: Service configurations
- `task-definition-{component}-{env}.json`: Task definitions
- `secrets-{env}.json`: Secrets Manager templates
- `troubleshooting.md`: Common issues and solutions

## Environment Configurations
- **Dev**: Development environment settings
- **Testnet**: Testing environment settings
- **Production**: Production environment settings

## How to Update Service
1. Edit appropriate JSON file for target environment
2. Register new task definition:
   ```bash
   aws ecs register-task-definition --cli-input-json file://task-definition-{component}-{env}.json
   ```
3. Update service:
   ```bash
   aws ecs update-service --service {service-name}-{component}-service-{env} --task-definition {new-revision}
   ```
4. Verify: `../scripts/health-check.sh --service {service-name} --env {env}`

## AI Maintenance Instructions
- Always update task definition before service
- Use health checks to verify deployments
- Follow naming conventions: {service-name}-{component}-service-{env}
- Document service-specific configurations in this folder
- Update troubleshooting.md for service-specific issues

{"family": "{service-name}-task-{env}", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::483805378365:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::483805378365:role/ecsTaskRole", "containerDefinitions": [{"name": "{service-name}", "image": "483805378365.dkr.ecr.ap-southeast-1.amazonaws.com/{service-name}:{env}", "portMappings": [{"containerPort": 9000, "protocol": "tcp"}], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/{service-name}-{env}", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "secrets": [{"name": "PORT", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:483805378365:secret:dcasks/{env}/{service-name}:PORT::"}], "healthCheck": {"command": ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}], "tags": [{"key": "Environment", "value": "{env}"}, {"key": "Service", "value": "{service-name}"}]}
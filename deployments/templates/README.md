# Service Templates

## How to Create New Service
1. Copy `new-service-template/` folder to `../services/{new-service-name}/`
2. Replace `{service-name}` placeholders in all files
3. Replace `{env}` with actual environments (dev, testnet, production)
4. Replace `{SERVICE-NAME}` with proper case service name
5. Update configurations for your specific service requirements
6. Test deployment: `../scripts/deploy.sh --service {new-service-name} --env dev`

## Template Files Explained
- `README.md`: Service-specific documentation template
- `service-{env}.json`: ECS service configuration template
- `task-definition-{env}.json`: ECS task definition template
- `secrets-{env}.json`: AWS Secrets Manager template

## Placeholder Replacement Guide
- `{service-name}`: Replace with kebab-case service name (e.g., stripe-gateway)
- `{SERVICE-NAME}`: Replace with proper case service name (e.g., Stripe Gateway)
- `{env}`: Replace with environment (dev, testnet, production)
- `{component}`: Replace with component name (api, job, web, etc.)
- `{cluster}`: Replace with cluster suffix (nprod for dev/testnet, prod for production)

## Example Replacement
```bash
# Original template file: service-{env}.json
# After replacement: service-dev.json, service-testnet.json, service-production.json

# Original placeholder: {service-name}-service-{env}
# After replacement: stripe-gateway-service-dev
```

## Available Templates
- `ecs-fargate-api-template.json`: Template for API services with ALB integration
- `ecs-fargate-job-template.json`: Template for background job services
- `ecs-fargate-web-template.json`: Template for web frontend services

## AI Instructions for New Services
1. Always use templates as starting point
2. Follow naming conventions: `{component}-{environment}.json`
3. Update `../shared-resources/resource-inventory.md` if new shared resources needed
4. Create service-specific troubleshooting.md if service has unique issues
5. Test all three environments (dev → testnet → production)
6. Document any new patterns in templates for future services

# DCasks Platform Deployments

## How to Use This Folder
1. **Deploy a service**: `./scripts/deploy.sh --service {service-name} --env {environment}`
2. **List all services**: `ls services/`
3. **Add new service**: Copy `templates/new-service-template/` to `services/{service-name}/`
4. **Manage shared resources**: Use files in `shared-resources/`

## Folder Structure
- `services/`: All platform services (dcasks-backend, jasmine, stripe-gateway, etc.)
- `shared-resources/`: Infrastructure shared across services
- `scripts/`: Deployment automation scripts
- `templates/`: Templates for creating new services
- `documentation/`: Platform-wide operational guides
- `dcasks-resources.env`: AWS resource IDs for CLI scripts

## File Naming Conventions
- Service definitions: `{component}-service-{env}.json`
- Task definitions: `task-definition-{component}-{env}.json`
- Secrets: `secrets-{env}.json`
- Environments: `dev`, `testnet`, `production`

## Quick Commands
```bash
# Deploy dcasks-backend to dev
./scripts/deploy.sh --service dcasks-backend --env dev

# Deploy jasmine to production
./scripts/deploy.sh --service jasmine --env production

# Check health of all services in dev
./scripts/health-check.sh --env dev

# Create new service
cp -r templates/new-service-template/ services/my-new-service/
```

## Available Services
- **dcasks-backend**: Main backend API and job processing
- **jasmine**: Frontend application and admin CMS
- **stripe-gateway**: Payment processing service (future)

## Environment Management
- **dev**: Development environment (single instances)
- **testnet**: Testing environment (single instances)
- **production**: Production environment (auto-scaling)

## AI Maintenance Instructions
1. All services must be in `services/` folder
2. Follow naming conventions above
3. Use templates for new services
4. Test in dev environment first
5. Update service README.md when adding new configurations
6. Reference shared resources from `shared-resources/`
7. Use `dcasks.env` for CLI environment variables

## How to Add New Service
1. Copy `templates/new-service-template/` to `services/{new-service-name}/`
2. Replace all `{service-name}` placeholders in files
3. Replace all `{env}` with actual environments (dev, testnet, production)
4. Update configurations for your specific service requirements
5. Test deployment: `./scripts/deploy.sh --service {new-service-name} --env dev`

## Shared Resources
- VPC, subnets, security groups, ALB, certificates managed in `shared-resources/`
- Reference shared resource IDs from `dcasks-resources.env`
- Use shared resources across all services to avoid duplication

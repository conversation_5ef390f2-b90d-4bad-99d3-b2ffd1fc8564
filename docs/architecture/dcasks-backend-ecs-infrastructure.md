# DCasks Backend ECS Infrastructure Architecture

**Document Version**: 1.0  
**Last Updated**: 2025-01-27  
**Status**: Implementation Complete (Dev), Testnet/Production Ready  
**AWS Region**: ap-southeast-1  

## Overview

This document provides a comprehensive view of the DCasks backend ECS infrastructure architecture, detailing the migration from EC2 Docker deployment to Amazon ECS using AWS Fargate. The architecture implements service separation, cost optimization, and multi-environment support while maintaining external API connectivity.

## Infrastructure Architecture Diagram

```mermaid
graph TB
    %% DNS & CDN Services
    subgraph "DNS & CDN"
        CloudFlare[Cloudflare DNS<br/>Host-based Routing<br/>DDoS Protection]
    end

    %% External API Services
    subgraph "External APIs"
        MongoDB[MongoDB Atlas]
        Blockchain[Blockchain APIs<br/>Arbitrum/Ethereum]
        Twilio[Twilio SMS API]
    end

    %% Internet Gateway
    IGW[Internet Gateway<br/>dcasks-igw]

    %% VPC and Networking
    subgraph "AWS VPC: dcasks-vpc (********/16)"
        subgraph "Public Subnets (Multi-AZ)"
            PubSub1[Public Subnet 1a<br/>********/24<br/>ap-southeast-1a]
            PubSub2[Public Subnet 1b<br/>********/24<br/>ap-southeast-1b]
        end

        %% Application Load Balancer
        subgraph "Shared ALB"
            ALB["Application Load Balancer<br/>dcasks-alb<br/>Host-based Routing"]
            ALBListener["HTTP Listener :80"]
            TG["Target Group<br/>dcasks-api-tg-ENV"]
        end

        %% ECS Cluster (Representative Pattern)
        subgraph "ECS Cluster: dcasks-cluster-ENV"
            subgraph "ECS Services"
                APIService["dcasks-api-service<br/>Fargate<br/>Auto-scaling: 1-5 instances<br/>CPU/Memory: Environment-specific"]
                JobService["dcasks-job-service<br/>Fargate<br/>Fixed: 1 instance<br/>CPU/Memory: Environment-specific"]
            end
        end
    end

    %% AWS Services
    AWSServices["AWS Services<br/>• Secrets Manager: dcasks/ENV/dcasks-backend<br/>• CloudWatch Logs: /ecs/dcasks-api-ENV & /ecs/dcasks-job-ENV<br/>• ECR Repository: dcasks-backend (COMMIT_SHA-ENV)"]

    %% Connections
    CloudFlare --> ALB
    ALB --> ALBListener
    ALBListener --> TG
    TG --> APIService

    APIService --> PubSub1
    APIService --> PubSub2
    JobService --> PubSub1
    JobService --> PubSub2

    PubSub1 --> IGW
    PubSub2 --> IGW
    IGW --> MongoDB
    IGW --> Blockchain
    IGW --> Twilio

    APIService -.-> AWSServices
    JobService -.-> AWSServices
```

## Environment-Specific Implementation

The above diagram represents the general architecture pattern that is replicated across three environments. Here's how the pattern applies to each environment:

### Environment Mapping Table

| Component | Development | Testnet | Production |
|-----------|-------------|---------|------------|
| **ECS Cluster** | dcasks-cluster-nprod | dcasks-cluster-nprod | dcasks-cluster-prod |
| **API Service** | dcasks-api-service-dev | dcasks-api-service-testnet | dcasks-api-service-production |
| **Job Service** | dcasks-job-service-dev | dcasks-job-service-testnet | dcasks-job-service-production |
| **Target Group** | dcasks-api-tg-dev | dcasks-api-tg-testnet | dcasks-api-tg-production |
| **Domain** | dev-api.dcasks.co | testnet-api.dcasks.co | api.dcasks.co |
| **Secrets** | dcasks/dev/dcasks-backend | dcasks/testnet/dcasks-backend | dcasks/production/dcasks-backend |
| **API Log Group** | /ecs/dcasks-api-dev | /ecs/dcasks-api-testnet | /ecs/dcasks-api-production |
| **Job Log Group** | /ecs/dcasks-job-dev | /ecs/dcasks-job-testnet | /ecs/dcasks-job-production |
| **Image Tag** | COMMIT_SHA-dev | COMMIT_SHA-testnet | COMMIT_SHA-production |

### Resource Allocation by Environment

| Environment | API Service Scaling | Job Service | CPU | Memory | Log Retention |
|-------------|-------------------|-------------|-----|--------|---------------|
| **Development** | 1-2 instances | 1 instance | 256 | 512 MB | 30 days |
| **Testnet** | 1-2 instances | 1 instance | 256 | 512 MB | 30 days |
| **Production** | 2-5 instances | 1 instance | 512 | 1024 MB | 90 days |

### Cluster Distribution

- **dcasks-cluster-nprod**: Hosts both dev and testnet environments (cost optimization)
- **dcasks-cluster-prod**: Dedicated to production environment (isolation and performance)

### Host-Based Routing Configuration

The shared ALB uses host-based routing to direct traffic to the appropriate environment:

```
dev-api.dcasks.co → dcasks-api-tg-dev → dcasks-api-service-dev
testnet-api.dcasks.co → dcasks-api-tg-testnet → dcasks-api-service-testnet
api.dcasks.co → dcasks-api-tg-production → dcasks-api-service-production
```

## Security Groups Configuration

The architecture uses three main security groups to control network access:

### ALB Security Group (dcasks-alb-sg)
- **Purpose**: Controls access to the Application Load Balancer
- **Inbound Rules**:
  - HTTP (port 80) from anywhere (0.0.0.0/0)
  - HTTPS (port 443) from anywhere (0.0.0.0/0) - when SSL is configured
- **Outbound Rules**:
  - HTTP (port 3000) to API services in ECS clusters

### API Security Group (dcasks-api-sg)
- **Purpose**: Controls access to API service containers
- **Inbound Rules**:
  - HTTP (port 3000) from ALB security group only
- **Outbound Rules**:
  - All traffic (0.0.0.0/0) for external API calls and database access

### Job Security Group (dcasks-job-sg)
- **Purpose**: Controls access for background job service containers
- **Inbound Rules**:
  - None (no inbound traffic required)
- **Outbound Rules**:
  - HTTPS (port 443) to anywhere for external API calls
  - HTTP (port 80) to anywhere for external API calls
  - MongoDB (port 27017) to anywhere for MongoDB Atlas connectivity
  - Custom ports (8545, etc.) for blockchain RPC connectivity

## Service Separation Architecture

```mermaid
graph LR
    subgraph "Current EC2 Docker Compose"
        EC2API[dcasks-api<br/>SERVICE_NAME=API<br/>Port: 3000]
        EC2Job[dcasks-job<br/>SERVICE_NAME=JOB<br/>No exposed ports]
        EC2API --- EC2Job
    end
    
    subgraph "Target ECS Fargate"
        subgraph "dcasks-api Services"
            ECSAPI1["dcasks-api-service-production<br/>Auto-scaling: 2-5 instances"]
            ECSAPI2["dcasks-api-service-testnet<br/>Auto-scaling: 1-2 instances"]
            ECSAPI3["dcasks-api-service-dev<br/>Auto-scaling: 1-2 instances"]
        end

        subgraph "dcasks-job Services"
            ECSJob1["dcasks-job-service-production<br/>Fixed: 1 instance"]
            ECSJob2["dcasks-job-service-testnet<br/>Fixed: 1 instance"]
            ECSJob3["dcasks-job-service-dev<br/>Fixed: 1 instance"]
        end
    end
    
    EC2API --> ECSAPI1
    EC2API --> ECSAPI2
    EC2API --> ECSAPI3
    EC2Job --> ECSJob1
    EC2Job --> ECSJob2
    EC2Job --> ECSJob3
```

## Environment Progression Strategy

```mermaid
graph TD
    subgraph "Development Flow"
        Dev[Development<br/>dcasks-cluster-nprod<br/>Branch: dev<br/>Tag: dev]
        Test[Testnet<br/>dcasks-cluster-nprod<br/>Branch: testnet<br/>Tag: testnet]
        Prod[Production<br/>dcasks-cluster-prod<br/>Branch: main<br/>Tag: latest]
    end
    
    Dev --> Test
    Test --> Prod
    
    subgraph "Resource Allocation"
        DevRes["Dev: CPU 256, Memory 512<br/>Minimal resources"]
        TestRes["Testnet: CPU 256, Memory 512<br/>Minimal resources"]
        ProdRes["Production: CPU 512, Memory 1024<br/>Higher resources"]
    end
    
    Dev -.-> DevRes
    Test -.-> TestRes
    Prod -.-> ProdRes
```

## Service Communication & Dependencies

The architecture maintains service independence while ensuring proper communication across all environments:

### API Service (dcasks-api-service)
- **Purpose**: Handles HTTP requests, GraphQL endpoints
- **Scaling**: Auto-scaling based on CPU utilization
  - Production: 70% CPU threshold, 2-5 instances
  - Dev/Testnet: 80% CPU threshold, 1-2 instances
- **Load Balancer**: Connected to environment-specific ALB target groups
- **Health Check**: `GET /health` endpoint using wget command
- **Port**: 3000 (internal container port)
- **Dependencies**: Database, external APIs, secrets from AWS Secrets Manager

### Job Service (dcasks-job-service)
- **Purpose**: Background processing, blockchain event listeners, automated tasks
- **Scaling**: Fixed single instance per environment (no load balancer needed)
- **External Connectivity**: Direct access to MongoDB Atlas, blockchain APIs, Twilio
- **Environment Variable**: `SERVICE_NAME=JOB` (triggers job processing logic)
- **Dependencies**: Database, blockchain networks, external APIs, secrets from AWS Secrets Manager

### Service Independence
- **Separate Scaling**: API service scales independently based on traffic, Job service maintains consistent processing
- **Isolated Failures**: Job service failures don't affect API availability and vice versa
- **Resource Optimization**: Different CPU/memory allocation based on service requirements

## Network Architecture Details

### VPC Configuration
- **CIDR**: ********/16 (modified to avoid conflicts)
- **Subnets**: Public subnets only (cost optimization)
- **Internet Access**: Internet Gateway (no NAT Gateway)
- **Cost Savings**: ~$90/month (no NAT Gateway charges)

### Security Groups
- **ALB Security Group**: HTTP inbound, ECS outbound
- **API Security Group**: ALB inbound, all outbound
- **Job Security Group**: No inbound, selective outbound (HTTPS, MongoDB, blockchain)

### Load Balancer Strategy
- **Single ALB**: Shared across all environments
- **Routing**: Host-based routing by domain
- **Cost Savings**: ~$32/month (2 fewer ALBs)
- **Domains**: 
  - `dev-api.dcasks.co` → dev environment
  - `testnet-api.dcasks.co` → testnet environment  
  - `api.dcasks.co` → production environment

## Implementation Status

### ✅ Completed (Dev Environment)
- VPC and networking infrastructure
- Security groups with proper egress rules
- ECS clusters (prod and nprod)
- Shared ALB with target groups
- CloudWatch log groups with retention policies
- IAM roles and Secrets Manager integration
- Dev environment services fully operational

### ⚠️ Pending (Testnet/Production)
- Docker image URL corrections in task definitions
- Health check fixes application
- Service validation and testing

## Cost Optimization Features

1. **No NAT Gateway**: Public subnets with Internet Gateway (~$90/month savings)
2. **Shared ALB**: Single load balancer for all environments (~$32/month savings)
3. **Right-sized Resources**: Minimal allocation for dev/testnet environments
4. **Log Retention**: 30 days for dev/testnet, 90 days for production

## Monitoring & Health Checks

- **Health Endpoint**: `GET /health` for API services
- **Health Check Command**: `wget --no-verbose --tries=1 --spider http://localhost:9000/health`
- **CloudWatch Logs**: Environment-specific log groups with retention policies
- **Container Insights**: Enabled for both ECS clusters
- **Auto-scaling Metrics**: CPU utilization-based scaling

## External Integrations

All services maintain connectivity to:
- **MongoDB Atlas**: Database connectivity via HTTPS
- **Blockchain APIs**: Arbitrum and Ethereum network access
- **Twilio**: SMS service integration
- **AWS Services**: Secrets Manager, CloudWatch, ECR

## Deployment Strategy

1. **Parallel Deployment**: Maintain EC2 during ECS transition
2. **Environment Progression**: dev → testnet → production
3. **Traffic Shifting**: DNS-based routing for gradual migration
4. **Rollback Capability**: Route 53 weighted routing for quick rollback

This architecture provides a robust, scalable, and cost-optimized foundation for the DCasks backend services while maintaining all existing functionality and external integrations.

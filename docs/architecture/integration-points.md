# Integration Points

## External System Integrations
```mermaid
flowchart LR
    App[Application] --> ExtAPI1[External API 1]
    App --> ExtAPI2[External API 2]
    App --> ExtSystem[External System]
    
    %% Add all integration points
```

## API Integrations

### External API 1
[Description of the integration]

| Endpoint | Method | Purpose | Request | Response | Error Handling |
|----------|--------|---------|---------|----------|----------------|
| `/api/...` | GET/POST | Description | Format | Format | Strategy |

### External API 2
[Description of the integration]

| Endpoint | Method | Purpose | Request | Response | Error Handling |
|----------|--------|---------|----------|----------|----------------|
| `/api/...` | GET/POST | Description | Format | Format | Strategy |

## System Integrations

### External System
[Description of the system integration]

```mermaid
sequenceDiagram
    participant App as Application
    participant ES as External System
    
    App->>ES: Request
    ES-->>App: Response
    
    %% Add detailed flow
```

## Authentication & Authorization
[Description of authentication and authorization mechanisms for integrations]

## Integration Patterns
[Description of integration patterns used, such as webhooks, polling, event-driven, etc.]

## Error Handling & Resilience
[Description of error handling strategies and resilience patterns]

## Related Memory Bank References
- [System Patterns](/memory-bank/systemPatterns.md)
- [Tech Context](/memory-bank/techContext.md)

## Blockchain Integration

### Ethereum Integration Points
- **Smart Contracts**: DCasks interacts with Ethereum smart contracts using ethers.js
- **Wallet Connection**: Frontend connects to MetaMask or other web3 wallets
- **Transaction Handling**: 
  - Frontend initiates transactions through wallet providers
  - Backend initiates transactions using admin wallets for automated operations
  - Transaction hashes are stored in the database for tracking and auditability
- **Token ID Management**:
  - Deterministic token IDs are generated using entity nonce values
  - Token IDs are checked against existing IDs to prevent collisions
  - Random generation serves as fallback mechanism
- **Event Listening**: Backend listens for contract events to update the database

### Solana Integration Points
// ... existing code ... 
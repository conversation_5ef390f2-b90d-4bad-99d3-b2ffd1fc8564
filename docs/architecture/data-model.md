# Data Model

## Entity Relationship Diagram
```mermaid
erDiagram
    ENTITY1 ||--o{ ENTITY2 : relationship
    ENTITY1 {
        id ID
        string field1
        string field2
    }
    ENTITY2 {
        id ID
        string field1
        id entity1_id
    }
    
    %% Add all entities and relationships
```

## Key Entities

### Entity1
[Description of Entity1]

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | ID | Primary key | Auto-increment |
| field1 | String | Description | [Optional/Required] |
| field2 | String | Description | [Optional/Required] |

### Entity2
[Description of Entity2]

| Field | Type | Description | Constraints |
|-------|------|-------------|------------|
| id | ID | Primary key | Auto-increment |
| field1 | String | Description | [Optional/Required] |
| entity1_id | ID | Foreign key to Entity1 | [Optional/Required] |

## Database Schema
[Description of database schema, including tables, views, stored procedures, etc.]

## Data Access Patterns
[Description of how data is accessed, including caching strategies, read/write patterns, etc.]

## Related Memory Bank References
- [Tech Context](/memory-bank/techContext.md)

## Blockchain Models

### WholeCaskIssue
Represents a request to issue a whole cask NFT.

```typescript
class WholeCaskIssue {
  _id: string;
  status: string; // 'pending', 'approved', 'rejected', 'minted'
  signature: string; // Signature for the issuance
  nonce: string; // Used for generating deterministic token IDs
  creatorAddress: string; // Wallet address of the creator
  wholeCaskId: string; // Reference to the physical cask
  txHash: string; // Transaction hash from NFT minting operation
  // Other fields...
}
```

### NFTToken
// ... existing code ... 
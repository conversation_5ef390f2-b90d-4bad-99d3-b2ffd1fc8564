# DCasks Mainnet Configuration Guide

This document provides detailed configuration settings for the DCasks platform deployment to Arbitrum mainnet and associated services.

## Smart Contract Configuration

### Environment Variables
```
# Ethereum Network Configuration
ETHERSCAN_API_KEY=your_etherscan_api_key
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# Admin Private Keys (Store securely, never commit to repositories)
PK_OWNER=0x...
PK_ADMIN=0x...
PK_CASK_OWNER=0x... 
```

### Hardhat Configuration
Key parameters in `hardhat.config.ts` for mainnet:

```typescript
networks: {
  mainnet: {
    url: "https://arb1.arbitrum.io/rpc",
    chainId: 42161,
    accounts: [
      process.env.PK_OWNER || "",
      process.env.PK_ADMIN || "",
      process.env.PK_CASK_OWNER || "",
    ],
  },
  eth: {
    url: "https://eth.drpc.org",
    chainId: 1,
    accounts: [
      process.env.PK_OWNER || "",
      process.env.PK_ADMIN || "",
      process.env.PK_CASK_OWNER || "",
    ],
  },
}
```

### Gas Settings
- Gas Reporter: Enable with `REPORT_GAS=true`
- Gas Optimization: Enabled in solidity compiler settings with 200 optimization runs

## Backend Configuration

### Environment Variables
```
# Server Configuration
NODE_ENV=production
PORT=3000
API_PREFIX=/api
ENABLE_DOCUMENTATION=false

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/dcasks_prod?retryWrites=true&w=majority
MONGODB_DB_NAME=dcasks_prod

# Blockchain Configuration
ETHEREUM_RPC_URL=https://arb1.arbitrum.io/rpc
ETHEREUM_CHAIN_ID=42161
ETHEREUM_WALLET_PRIVATE_KEY=0x... # Admin wallet for automated operations

# Contract Addresses (Updated after deployment)
DCASK_NFT_ADDRESS=0x...
DCASK_MARKET_ADDRESS=0x...
DCASK_PROFIT_MODEL_ADDRESS=0x...
DCASK_MEMBERSHIP_ADDRESS=0x...

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET=dcasks-production-assets

# Email Configuration
AWS_SES_REGION=us-east-1
EMAIL_FROM=<EMAIL>

# Authentication
JWT_SECRET=long_random_string_min_32_chars
JWT_EXPIRATION=1d

# Monitoring
SENTRY_DSN=https://your_sentry_dsn
ENABLE_LOGGING=true
LOG_LEVEL=info
```

### Security Configuration
In `main.ts`:

```typescript
// CORS Configuration
const corsOptions = {
  origin: [
    'https://dcasks.com',
    'https://admin.dcasks.com',
    'https://jasmine.dcasks.com',
  ],
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400,
};
app.enableCors(corsOptions);

// Request Limiting
app.use(
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
  }),
);

// Helmet for HTTP headers security
app.use(helmet());
```

## Frontend Configuration

### Environment Variables
```
# API Configuration
NEXT_PUBLIC_API_URL=https://api.dcasks.co
NEXT_PUBLIC_GRAPHQL_URL=https://api.dcasks.co/graphql

# Blockchain Configuration
NEXT_PUBLIC_ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
NEXT_PUBLIC_ARBITRUM_CHAIN_ID=42161

# Contract Addresses (Updated after deployment)
NEXT_PUBLIC_DCASK_NFT_ADDRESS=0x...
NEXT_PUBLIC_DCASK_MARKET_ADDRESS=0x...
NEXT_PUBLIC_DCASK_PROFIT_MODEL_ADDRESS=0x...
NEXT_PUBLIC_DCASK_MEMBERSHIP_ADDRESS=0x...

# Features Toggles
NEXT_PUBLIC_ENABLE_WHOLE_CASK=true
NEXT_PUBLIC_ENABLE_LIQUID_STAKING=true

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://your_sentry_dsn
```

### Build Configuration
In `next.config.js`:

```javascript
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['dcasks-production-assets.s3.amazonaws.com'],
    formats: ['image/avif', 'image/webp'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Enable SWC minification
  swcMinify: true,
};
```

## Admin CMS Configuration

### Environment Variables
```
# API Configuration
REACT_APP_API_URL=https://api.dcasks.co
REACT_APP_GRAPHQL_URL=https://api.dcasks.co/graphql

# Authentication
REACT_APP_AUTH_PROVIDER=graphql
REACT_APP_JWT_SECRET=same_as_backend_jwt_secret

# Features
REACT_APP_ENABLE_WHOLE_CASK=true

# Monitoring
REACT_APP_SENTRY_DSN=https://your_sentry_dsn
```

## Jasmine Telegram Mini App Configuration

### Environment Variables
```
# Server & Application Configuration
NODE_ENV=production
PORT=3001
PAYLOAD_PUBLIC_SERVER_URL=https://jasmine.dcasks.co
PAYLOAD_PUBLIC_SITE_URL=https://jasmine.dcasks.co

# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/jasmine_prod?retryWrites=true&w=majority
PAYLOAD_SECRET=long_random_string_for_payload_encryption
PAYLOAD_CONFIG_PATH=src/payload.config.ts

# S3 Configuration
S3_BUCKET=jasmine-production-assets
S3_REGION=us-east-1
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key
MEDIA_DOMAIN=https://jasmine-production-assets.s3.amazonaws.com

# DCasks API Integration
DCASKS_API_URL=https://api.dcasks.co
DCASKS_GRAPHQL_URL=https://api.dcasks.co/graphql

# Blockchain Configuration
NEXT_PUBLIC_ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
NEXT_PUBLIC_ARBITRUM_CHAIN_ID=42161

# Contract Addresses (Updated after deployment)
NEXT_PUBLIC_DCASK_NFT_ADDRESS=0x...
NEXT_PUBLIC_DCASK_MARKET_ADDRESS=0x...
NEXT_PUBLIC_DCASK_PROFIT_MODEL_ADDRESS=0x...
NEXT_PUBLIC_DCASK_MEMBERSHIP_ADDRESS=0x...

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
NEXT_PUBLIC_TELEGRAM_BOT_USERNAME=dcasks_bot
TELEGRAM_WEBHOOK_URL=https://jasmine.dcasks.co/api/webhook
TELEGRAM_SECRET_TOKEN=long_random_string_for_webhook_verification

# PayloadJS Admin Configuration
PAYLOAD_ADMIN_EMAIL=<EMAIL>
PAYLOAD_ADMIN_PASSWORD=secure_admin_password
PAYLOAD_PREVIEW_SECRET=long_random_string_for_preview_mode

# Authentication
PAYLOAD_JWT_SECRET=long_random_string_for_jwt_tokens
PAYLOAD_JWT_EXPIRATION=1d
COOKIE_SECRET=long_random_string_for_cookie_encryption

# Security
NEXT_PUBLIC_MAX_FILE_SIZE=10485760 # 10MB
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf
PAYLOAD_UPLOAD_LIMITS_IMAGE_SIZE=5000000 # 5MB
PAYLOAD_UPLOAD_LIMITS_DOC_SIZE=10000000 # 10MB

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=https://your_sentry_dsn
SENTRY_DSN=https://your_sentry_dsn
```

### PayloadJS Configuration
In `payload.config.ts`:

```typescript
import { buildConfig } from 'payload/config';
import { s3Adapter } from '@payloadcms/plugin-cloud-storage/s3';

const cloudStorage = s3Adapter({
  config: {
    credentials: {
      accessKeyId: process.env.S3_ACCESS_KEY || '',
      secretAccessKey: process.env.S3_SECRET_KEY || '',
    },
    region: process.env.S3_REGION || 'us-east-1',
    endpoint: undefined, // only set if using a custom endpoint
  },
  bucket: process.env.S3_BUCKET || '',
});

export default buildConfig({
  serverURL: process.env.PAYLOAD_PUBLIC_SERVER_URL,
  admin: {
    user: 'users',
    buildPath: 'build/admin',
    bundler: 'webpack',
    meta: {
      titleSuffix: '- DCasks Jasmine Admin',
      favicon: '/favicon.ico',
    },
  },
  collections: [
    // Collection definitions go here
  ],
  upload: {
    limits: {
      maxFileSize: parseInt(process.env.PAYLOAD_UPLOAD_LIMITS_IMAGE_SIZE || '5000000', 10),
      maxFiles: 5,
    },
  },
  plugins: [
    cloudStorage({
      collections: {
        // Collection-specific storage configurations go here
      },
    }),
  ],
  db: {
    adapter: 'mongoose',
    url: process.env.MONGODB_URI || 'mongodb://localhost/jasmine',
  },
  csrf: {
    enabled: process.env.NODE_ENV === 'production',
    cookieSecure: process.env.NODE_ENV === 'production',
  },
  cors: [
    'https://dcasks.com',
    'https://api.dcasks.co',
    'https://telegram.org',
    'https://*.telegram.org',
    'https://telegram-web-app.github.io',
  ],
  graphQL: {
    schemaOutputFile: path.resolve(__dirname, 'generated-schema.graphql'),
  },
  rateLimit: {
    window: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per window
  },
  typescript: {
    outputFile: path.resolve(__dirname, 'payload-types.ts'),
  },
});
```

### Telegram Bot Configuration
In Telegram BotFather, set the following:

1. Bot Commands:
```
start - Start using the DCasks mini app
help - Get help with using the app
settings - Adjust your preferences
profile - View your profile
marketplace - Browse available casks
connect - Connect your wallet
```

2. Menu Button:
   - Set to type "webApp"
   - URL: `https://jasmine.dcasks.co`
   - Text: "Open DCasks"

3. Bot Settings:
   - Enable inline mode: Yes
   - Allow groups: Optional (depending on use case)
   - Group privacy: Enabled
   - Domain: jasmine.dcasks.co

### Web App Configuration
In `next.config.js`:

```javascript
const { withPayload } = require('@payloadcms/next-payload');

module.exports = withPayload({
  reactStrictMode: true,
  images: {
    domains: [
      'jasmine-production-assets.s3.amazonaws.com',
      'dcasks-production-assets.s3.amazonaws.com',
    ],
    formats: ['image/avif', 'image/webp'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Optimize for Telegram Mini App
  swcMinify: true,
  experimental: {
    optimizeCss: true,
  },
  // Required for Telegram Mini App embedding
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "frame-ancestors https://telegram.org https://*.telegram.org https://telegram-web-app.github.io;"
          }
        ]
      }
    ]
  }
});
```

### MongoDB Collection Configuration

Key collections to configure in PayloadJS:

```typescript
// Users Collection
{
  slug: 'users',
  auth: true,
  admin: {
    useAsTitle: 'email',
  },
  fields: [
    {
      name: 'telegramId',
      type: 'text',
      unique: true,
    },
    {
      name: 'wallet',
      type: 'text',
      unique: true,
    },
    // Additional user fields
  ],
  indexes: [
    {
      fields: { telegramId: 1 },
      options: { unique: true },
    },
    {
      fields: { wallet: 1 },
      options: { sparse: true },
    },
  ],
}

// Media Collection
{
  slug: 'media',
  upload: {
    staticURL: '/media',
    staticDir: 'media',
    disableLocalStorage: true, // Use S3 instead
    adminThumbnail: 'thumbnail',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 200,
        height: 200,
        position: 'centre',
      },
      {
        name: 'medium',
        width: 800,
        height: 800,
        position: 'centre',
      },
      {
        name: 'large',
        width: 1600,
        height: 1600,
        position: 'centre',
      },
    ],
  },
  fields: [
    // Media-specific fields
  ],
}

// TelegramSessions Collection
{
  slug: 'telegram-sessions',
  fields: [
    {
      name: 'telegramId',
      type: 'text',
      required: true,
    },
    {
      name: 'sessionData',
      type: 'json',
      required: true,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
    },
  ],
  indexes: [
    {
      fields: { telegramId: 1 },
      options: { unique: true },
    },
    {
      fields: { expiresAt: 1 },
      options: { expireAfterSeconds: 0 },
    },
  ],
}
```

## Database Indexes

Critical MongoDB indexes for production performance:

```javascript
// User Collection
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ wallet: 1 });
db.users.createIndex({ telegramId: 1 }); // For Telegram users

// NFT Collection
db.nfts.createIndex({ tokenId: 1 });
db.nfts.createIndex({ creator: 1 });
db.nfts.createIndex({ owner: 1 });

// WholeCask Collection
db.wholecasks.createIndex({ tokenId: 1 }, { unique: true });
db.wholecasks.createIndex({ creator: 1 });
db.wholecasks.createIndex({ "transactions.txHash": 1 });

// Transaction Collections
db.transactions.createIndex({ txHash: 1 }, { unique: true });
db.transactions.createIndex({ tokenId: 1 });
db.transactions.createIndex({ timestamp: -1 });

// Market Collections
db.marketlistings.createIndex({ tokenId: 1 });
db.marketlistings.createIndex({ status: 1 });
db.marketlistings.createIndex({ endTime: 1 });

// Telegram User Collections
db.telegramUsers.createIndex({ telegramId: 1 }, { unique: true });
db.telegramUsers.createIndex({ userId: 1 });

// Jasmine Specific Collections (PayloadJS)
db.payload_users.createIndex({ telegramId: 1 }, { unique: true, sparse: true });
db.payload_users.createIndex({ wallet: 1 }, { sparse: true });
db.payload_telegram_sessions.createIndex({ telegramId: 1 }, { unique: true });
db.payload_telegram_sessions.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
db.payload_media.createIndex({ updatedAt: -1 });
```

## Monitoring Configuration

### Sentry Settings
Configure the Sentry SDK with appropriate sampling rates for production:

```javascript
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: 'production',
  tracesSampleRate: 0.2,
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Mongo({
      useMongoose: true,
      tracing: true,
    }),
  ],
  beforeSend(event) {
    // Scrub sensitive data if needed
    if (event.request?.data) {
      delete event.request.data.password;
      delete event.request.data.token;
    }
    return event;
  },
});
```

### Critical Alert Settings
Configure monitoring alerts for:

1. Backend service health (5xx errors exceeding 1% of requests)
2. Transaction failures (failed contract interactions exceeding 5%)
3. Database latency (query times exceeding 500ms)
4. API endpoint performance (response times exceeding 2000ms)
5. Authentication failures (repeated login failures from same IP)
6. Telegram webhook failures (failed deliveries exceeding 5%)

## Blockchain Monitoring

Configure event listeners for critical contract events:

```typescript
// Example event monitoring configuration
export const criticalEvents = [
  {
    contract: 'DCaskNft',
    event: 'TransferSingle',
    handler: 'handleTransferSingle',
    priority: 'high',
  },
  {
    contract: 'DCaskNft',
    event: 'TransferBatch',
    handler: 'handleTransferBatch',
    priority: 'high',
  },
  {
    contract: 'DCaskMarket',
    event: 'ListingCreated',
    handler: 'handleListingCreated',
    priority: 'medium',
  },
  {
    contract: 'DCaskMarket',
    event: 'ListingSold',
    handler: 'handleListingSold',
    priority: 'high',
  },
];
```

## Related Documentation
- [Mainnet Deployment Checklist](./mainnet-checklist.md)
- [Mainnet Deployment Architecture](./mainnet-architecture.md)
- [System Patterns](/memory-bank/systemPatterns.md) 
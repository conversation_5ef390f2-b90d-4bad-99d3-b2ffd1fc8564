# DCasks Mainnet Deployment Checklist

## 1. Smart Contract Deployment

### Pre-Deployment Preparation
- [x] Create secure `.env` file with mainnet private keys (PK_OWNER, PK_ADMIN, PK_CASK_OWNER)
- [x] Ensure ETHERSCAN_API_KEY is set for contract verification
- [x] Perform final code review of all contracts 
- [x] Run comprehensive test suite against testnet environment
- [x] Confirm all contract functions conform to ERC1155/ERC20 standards
- [x] Prepare gas cost estimates for deployment transactions

### Deployment Process
- [x] Deploy contracts to Arbitrum mainnet using `npx hardhat run scripts/run-deploy-all.ts --network mainnet`
- [x] Verify all deployed contracts on Arbitrum Explorer
- [x] Update contract address registry with mainnet addresses
- [x] Validate deployed contracts using Hardhat tasks
- [x] Perform smoke tests on all critical contract functions
- [x] Set up appropriate role permissions for admin accounts

### Post-Deployment Verification
- [x] Verify owner and admin addresses have correct permissions
- [x] Verify create cask functionality
- [ ] Confirm DCaskNft contract implements ERC1155 correctly
- [ ] Validate marketplace functionality with test transactions
- [ ] Ensure profit model calculations work as expected
- [ ] Test whole cask NFT functionality

## 2. Backend Deployment

### Pre-Deployment Preparation
- [x] Update environment variables for mainnet blockchain connections
- [x] Configure production MongoDB connection strings
- [x] Set up AWS S3 buckets for production file storage
- [x] Set up Sentry for production error tracking
- [x] Review security settings for GraphQL endpoints
- [x] Prepare database indexes for optimized queries
- [x] Alchemy and Quicknode API keys (WSS and HTTP endpoints)

### Deployment Process
- [x] Build Docker image with production configuration
- [x] Deploy backend using container orchestration
- [ ] Configure proper scaling and resource allocation
- [ ] ~~Set up health check endpoints~~
- [x] Deploy job processing service for background tasks
- [ ] ~~Set up proper logging configuration~~

### Post-Deployment Verification
- [x] Validate GraphQL API endpoints for all core functionality
- [x] Confirm blockchain integration with mainnet contracts
- [x] Verify proper handling of blockchain events
- [ ] Test whole cask functionality end-to-end
- [ ] Ensure proper creator field updates during NFT transfers

## 3. Frontend Deployment

### Pre-Deployment Preparation
- [x] Update environment variables with mainnet contract addresses
- [x] Configure backend API endpoint for production
- [x] Set up Sentry for frontend error tracking
- [x] Update Environment Variables in Vercel

### Deployment Process
- [x] Build production Next.js application
- [x] Deploy to Vercel or equivalent platform
- [x] Set up SSL certificates and security headers

### Post-Deployment Verification
- [x] Validate wallet connection with MetaMask
- [x] Test marketplace functionality with real transactions
- [ ] Verify whole cask component rendering
- [x] Verify error handling across key components

## 4. Admin CMS Deployment

### Pre-Deployment Preparation
- [x] Update GraphQL endpoint configuration for production
- [x] Configure admin authentication mechanisms
- [x] Review security settings and access controls

### Deployment Process
- [x] Build & deploy production Admin CMS application to Vercel
- [x] Configure proper authentication
- [x] Configure domain settings and security

### Post-Deployment Verification
- [x] Validate admin user authentication
- [x] Test cask owner request approval workflow
- [x] Test cask issue request approval workflow
- [ ] Test whole cask issue request approval workflow

## 5. Jasmine Telegram Mini App Deployment

### Pre-Deployment Preparation
- [x] Update environment variables with mainnet configuration for both frontend and backend
- [x] Configure MongoDB connection string for production
- [x] Set up AWS S3 bucket for Jasmine media storage
- [x] Set up Sentry for comprehensive error tracking
- [x] Configure PayloadJS settings for production environment
- [x] Prepare Telegram Bot API integration settings
- [x] Configure proper CORS settings for Telegram integration

### Deployment Process
- [x] Build production version of the Next.js application (with PayloadJS)
- [x] Set up proper AWS S3 permissions and CORS policy
- [x] Set up SSL certificates and security headers
- [x] Register and configure Telegram Bot with BotFather
- [ ] ~~Configure Telegram bot webhook endpoints~~
- [ ] Set up proper logging for both frontend and backend components

### Database Configuration
- [x] Initialize MongoDB collections with proper schemas
- [x] Create necessary indexes for Telegram user data
- [x] Set up authentication configuration
- [x] Configure access control for PayloadJS admin panel

### Post-Deployment Verification
- [ ] Validate mini app loading in Telegram environments
- [ ] Test authentication flow via Telegram
- [ ] ~~Verify blockchain integration functionality (haven't implemented yet)~~
- [x] Test PayloadJS admin panel functionality
- [x] Verify file uploads to S3 via PayloadJS
- [x] Test database queries and performance
- [ ] ~~Validate webhook functionality for Telegram events~~es
- [x] Validate user data persistence in MongoDB

## 6. Post-Launch Activities

- [ ] Monitor system performance and user activity
- [ ] Address any issues identified during initial usage
- [ ] Gather feedback for system improvements
- [ ] Plan for feature enhancements based on real usage
- [ ] Schedule regular maintenance and updates
- [ ] Continue documentation improvements based on operational experience 
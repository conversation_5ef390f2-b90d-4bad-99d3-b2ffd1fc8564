# DCasks Mainnet Deployment Architecture

## Overall Architecture

```mermaid
flowchart TD
    WebUser[Web User] --> Frontend[Frontend \n Next.js]
    TelegramUser[Telegram User] --> JasmineApp[Jasmine \n PayloadJS]
    Admin[Admin User] --> AdminCMS[Admin CMS]
    Admin --> JasmineApp
    
    Frontend --> Backend
    JasmineApp --> Backend
    AdminCMS --> Backend
    
    Backend[Backend \n NestJS] --> MongoDB[(MongoDB Atlas\n Main Database \n Jasmine Database)]
    JasmineApp --> MongoDB
    Backend --> AWS_S3[(AWS S3\n Main Assets \n Jasmine Assets)]
    JasmineApp --> AWS_S3
    
    Backend <--> SmartContracts[Smart Contracts \n Arbitrum Mainnet]
    Frontend <--> SmartContracts
    JasmineApp <--> SmartContracts
    Backend <--> TelegramAPI[Telegram Bot API]
```

## Component Deployment Details

### Smart Contracts
- **Network**: Arbitrum Mainnet (Chain ID: 42161)
- **Deployment Method**: Hardhat scripts
- **Verification**: Automated via Etherscan API
- **Key Contracts**:
  - DCaskNft (ERC1155 implementation)
  - DCaskMarket (Marketplace functionality)
  - DCaskProfitModel (Fee calculations)
  - DCaskMembership (Membership tiers)
  - Supporting contracts (Swap, Wokens, MultiTransfer)

### Backend
- **Platform**: Kubernetes cluster
- **Containerization**: Docker
- **Scaling**: Horizontal pod autoscaling
- **Services**:
  - API Service: GraphQL endpoints for application functionality
  - Job Processing Service: Background tasks for blockchain monitoring
- **External Dependencies**:
  - MongoDB Atlas: Primary database
  - AWS S3: File storage for cask images and documents
  - AWS SES: Email notifications
  - Sentry: Error tracking and monitoring

### Frontend
- **Platform**: Vercel
- **Deployment**: Git-based continuous deployment
- **Build Process**: Next.js production build
- **CDN**: Vercel Edge Network
- **Caching Strategy**: Static page caching where applicable
- **Environment Variables**: Managed through Vercel platform

### Admin CMS
- **Platform**: Dedicated hosting (Vercel or similar)
- **Authentication**: JWT-based admin authentication
- **Access Control**: Role-based permissions
- **Connection**: GraphQL API to Backend

### Jasmine Telegram Mini App
- **Architecture**: Full-stack Next.js application with PayloadJS
- **Platform**: Vercel or similar hosting
- **Backend Framework**: PayloadJS (built on Express.js)
- **Database**: MongoDB Atlas (dedicated database)
- **Storage**: AWS S3 for media and assets
- **Admin Interface**: Built-in PayloadJS admin dashboard
- **Authentication**:
  - Telegram-based user authentication
  - JWT tokens for session management
  - PayloadJS built-in authentication for admin access
- **API Layer**: 
  - PayloadJS REST and GraphQL APIs
  - Custom API routes for Telegram webhook
  - Integration with DCasks main backend API
- **Blockchain Integration**: Direct interaction with smart contracts
- **UI Framework**: 
  - Next.js frontend with PayloadJS integration
  - Telegram Mini App SDK compliance
- **Media Management**: PayloadJS media uploads with S3 storage
- **Content Management**: Collections and fields defined in PayloadJS

## Network Configuration

### Domain Structure
- Main Application: `https://dcasks.com`, `https://dcasks.co`
- Admin CMS: `https://admin.dcasks.com`
- Backend API: `https://api.dcasks.co`
- Jasmine Mini App: `https://jasmine.dcasks.co`
- Jasmine Admin: `https://jasmine.dcasks.co/admin`

### Security Configuration
- SSL certificates on all endpoints
- Proper CORS configuration
- Rate limiting on API endpoints
- WAF protection
- DDoS mitigation

## Database Configuration

### MongoDB Setup
- Production cluster with appropriate replication
- Separate collections for critical data
- Proper indexing for query optimization
- Regular backups with point-in-time recovery
- Monitoring for query performance

### Jasmine MongoDB Configuration
- Dedicated database for Jasmine application
- PayloadJS collections for users, media, and app-specific data
- Standard indexes for session management
- Automated backups with MongoDB Atlas

## Storage Configuration

### AWS S3 Setup
- Main bucket for DCasks platform assets
- Dedicated bucket for Jasmine media content
- CloudFront distribution for CDN capabilities
- Proper CORS configuration for web and mobile access
- Lifecycle policies for efficient storage management

## Blockchain Integration

### Arbitrum Configuration
- Production RPC endpoints with fallback providers
- Transaction monitoring for contract events
- Gas optimization for mainnet transactions
- Secure wallet management for admin operations

## Telegram Integration

### Bot Configuration
- Registered bot with BotFather
- Webhook configuration for event handling
- Menu button for launching mini app
- Command handlers for basic interactions
- User authentication flow with Telegram Login Widget

## Monitoring and Alerting

### Key Metrics
- Backend service health and performance
- Frontend error rates
- Jasmine Mini App performance in Telegram environment
- PayloadJS admin panel performance
- MongoDB query performance
- AWS S3 operations and latency
- Blockchain transaction success rates
- Database performance
- API response times
- User activity levels

### Alert Configuration
- Critical error notifications
- Performance degradation alerts
- Security incident alerts
- Blockchain event processing failures
- Database connection issues

## Disaster Recovery

### Backup Strategy
- Daily database backups
- Configuration backups
- Smart contract ABIs and deployment details
- Infrastructure-as-code templates
- PayloadJS configuration backups
- AWS S3 bucket versioning

### Recovery Procedures
- Database restore process
- Service redeployment procedures
- Fallback configurations
- Emergency contact protocol

## Related Documentation
- [Mainnet Deployment Checklist](./mainnet-checklist.md)
- [System Patterns](/memory-bank/systemPatterns.md)
- [Technical Context](/memory-bank/techContext.md) 
# Project Documentation

## Project Overview
This document provides comprehensive documentation for the dCasks project. It contains detailed information about features, architecture, and technical implementations to complement the context in the Memory Bank.

## Key Features
*Features will be added here as they are implemented, with links to detailed documentation.*

## Feature Documentation

- [Token ID Generation and Transaction Tracking](/docs/features/token-id-generation.md) - Deterministic NFT token IDs and blockchain transaction tracking
- [WholeCask Transfer and Creator Updates](/docs/features/whole-cask-transfer.md) - Automatic creator field updates for WholeCask NFT transfers
- [Price Chart Component](./features/price-chart/price-chart-component.md) - Interactive price history visualization for NFTs

## Architecture
- [Technical Overview](/docs/architecture/technical-overview.md) - High-level technical architecture
- [Data Model](/docs/architecture/data-model.md) - Core data models and relationships
- [Integration Points](/docs/architecture/integration-points.md) - Key integration points between services

## Deployment
- [Mainnet Deployment Checklist](/docs/deployment/mainnet-checklist.md) - Comprehensive checklist for mainnet deployment
- [Mainnet Deployment Architecture](/docs/deployment/mainnet-architecture.md) - Architecture diagram and component details for mainnet
- [Mainnet Configuration Guide](/docs/deployment/mainnet-configuration.md) - Environment variables and configuration settings for production

## Related Memory Bank Files
- [Project Brief](/memory-bank/projectbrief.md)
- [Product Context](/memory-bank/productContext.md) 
- [System Patterns](/memory-bank/systemPatterns.md)
- [Tech Context](/memory-bank/techContext.md)
- [Active Context](/memory-bank/activeContext.md)
- [Progress](/memory-bank/progress.md)

## Document Maintenance
This documentation is automatically maintained by Cursor AI and synchronized with the Memory Bank. To update all documentation, use the command **update documentation**. 
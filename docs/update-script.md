# Documentation Update Process

This file provides guidance for AI to follow when the user issues the **update documentation** command.

## Update Process

1. **Scan the codebase:**
   - Identify new files and changes since last documentation update
   - Focus on components, features, and architectural changes

2. **Update feature documentation:**
   - For each identified feature:
     - Check if documentation exists in `docs/features/`
     - If it exists, update with new changes
     - If it doesn't exist, create new documentation using the template
   - Ensure each feature document includes:
     - Clear description
     - User flow diagrams (Mermaid)
     - Technical implementation details
     - Component diagrams (Mermaid)
     - Code references
     - API documentation (if applicable)
     - State management information
     - Links to related memory-bank files

3. **Update architecture documentation:**
   - Update `technical-overview.md` with any architectural changes
   - Update `data-model.md` with any data model changes
   - Update `integration-points.md` with any integration changes
   - Ensure diagrams (Mermaid) are up-to-date

4. **Update overview:**
   - Update `docs/overview.md` with links to all documentation
   - Ensure all features are listed
   - Verify all links are correct

5. **Synchronize with memory-bank:**
   - Cross-reference documentation with memory-bank files
   - Update any memory-bank files as needed to reference documentation
   - Ensure consistency between documentation and memory-bank

## Documentation File Structure

- `docs/overview.md` - Main index
- `docs/features/` - Feature documentation
  - `_template.md` - Template for new feature documentation
  - `[feature-name].md` - Individual feature documentation
- `docs/architecture/` - Architecture documentation
  - `technical-overview.md` - System architecture
  - `data-model.md` - Data model
  - `integration-points.md` - Integration points

## When To Create New Feature Documentation

Create new feature documentation when:
1. A new feature is implemented
2. A new component is added that provides significant functionality
3. A new API endpoint or group of endpoints is added

## When To Update Existing Documentation

Update existing documentation when:
1. A feature's behavior or implementation changes
2. API endpoints change
3. Component relationships change
4. The data model changes
5. Integration points change

## Memory Bank Integration

Maintain bidirectional links between documentation and memory-bank:
- Feature documentation should link to relevant memory-bank files
- Memory-bank activeContext.md should reference current work documented
- Memory-bank systemPatterns.md should reference architecture documentation 
# WholeCask Transfer and Creator Field Updates

## Overview
This feature ensures that the `creator` field in the WholeCask model is properly updated whenever a WholeCask NFT is transferred to a new owner. This keeps ownership records accurate in the database, reflecting the current owner of the NFT.

## Business Context
WholeCask NFTs represent ownership of entire casks in the DCasks platform. When these assets change ownership through blockchain transactions, it's essential that our database records remain in sync with the blockchain state. The creator field is particularly important as it determines who has ownership rights and is displayed in the DCasks frontend.

## Implementation Details

### Blockchain Event Handling
The system listens for `TransferSingle` and `TransferBatch` events from the DCaskWholeCask smart contract. When these events occur, the NFTService processes them to update various database records.

### WholeCask Creator Update Logic
When a transfer event is detected, the system:
1. Checks if the transfer is to a real user (not to the marketplace)
2. Verifies that it's a genuine ownership change (from one user to another)
3. Updates the WholeCask model's creator field with the new owner's address
4. Logs the update for monitoring purposes

### Key Code Implementation

The implementation is located in `apps/dcasks-backend/src/nft/nft.service.ts`:

```typescript
// Update the WholeCask model creator field when ownership changes
// Only update if it's a real transfer to a new owner (not to marketplace)
if (!inMarketplace && event.payload[2] !== event.payload[1]) {
  try {
    await this.wholeCaskModel.findOneAndUpdate(
      { projectId: tokenId },
      { $set: { creator: to } },
      { session }
    );
    this.logger.log(`Updated WholeCask creator for projectId ${tokenId} to ${to}`);
  } catch (error) {
    this.logger.error(`Failed to update WholeCask creator: ${error.message}`, error.stack);
  }
}
```

A similar implementation exists for batch transfers.

## Edge Cases & Error Handling

- **Marketplace Transfers**: If a NFT is transferred to the marketplace (DCaskTulip contract), the creator field is not updated since the marketplace is not the true owner.
- **Self-Transfers**: If the from and to addresses are the same, no update is performed to avoid unnecessary database operations.
- **Database Errors**: Any errors during the update operation are caught, logged, and don't block the processing of other events.

## Interfaces & Dependencies

### Required Models
- `WholeCask`: Contains the creator field that needs to be updated
- `NFT`: Contains the NFT ownership information

### Service Dependencies
- `NFTService`: Handles blockchain events and updates the WholeCask creator field
- `ProviderService`: Provides transaction support and contract addresses

## Related Files
- `apps/dcasks-backend/src/nft/nft.service.ts`: Contains the event handling logic
- `apps/dcasks-backend/src/nft/nft.module.ts`: Registers the WholeCask model
- `apps/dcasks-backend/src/cask/models/whole-cask.model.ts`: Defines the WholeCask model structure

## Cross-References
- **Memory Bank**: See `memory-bank/activeContext.md` for current WholeCask functionality implementation status
- **Architecture**: The NFT event handling follows the event-driven architecture pattern described in architecture documentation

## Testing
Manual testing can be performed by:
1. Minting a new WholeCask NFT
2. Transferring it to another address
3. Verifying that the creator field is updated in the database

## Monitoring
Successful updates and errors are logged with the following patterns:
- Success: `Updated WholeCask creator for projectId ${tokenId} to ${to}`
- Error: `Failed to update WholeCask creator: ${error.message}`

Monitor these log entries to ensure the feature is working correctly. 
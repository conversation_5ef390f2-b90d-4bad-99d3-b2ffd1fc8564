# Token ID Generation and Transaction Tracking

## Overview
This feature implements deterministic token ID generation for whole cask NFTs and transaction hash tracking for blockchain transactions. The implementation improves consistency in NFT identification and enhances auditability of blockchain operations.

## Implementation Details

### Deterministic Token ID Generation
- **Purpose**: Generate consistent token IDs for whole cask NFTs based on existing data rather than random generation
- **Implementation**: Uses the `nonce` value from the `WholeCaskIssue` model as the token ID
- **Fallback**: Random token ID generation remains as a fallback when no nonce exists
- **Location**: `apps/dcasks-backend/src/cask/whole-cask-issue.service.ts` in the `mintTokenAfterApproval` method

### Transaction Hash Tracking
- **Purpose**: Store transaction hashes from blockchain operations for auditability and status tracking
- **Implementation**: Added `txHash` field to the `WholeCaskIssue` model
- **Storage**: Transaction hash is stored after successful NFT minting operations
- **GraphQL**: Exposed through the GraphQL API for frontend consumption
- **Location**: 
  - Model: `apps/dcasks-backend/src/cask/models/whole-cask-issue.model.ts`
  - Service: `apps/dcasks-backend/src/cask/whole-cask-issue.service.ts`
  - GraphQL: `apps/dcasks-cms/src/apollo/mutations/whole-cask-issues.ts`

## Usage Flow

```mermaid
sequenceDiagram
    Admin->>CMS: Approve whole cask issue
    CMS->>Backend: Handle whole cask issue (approved=true)
    Backend->>Blockchain: Mint NFT with token ID from nonce
    Blockchain->>Backend: Return transaction hash
    Backend->>Database: Store txHash in WholeCaskIssue
    Backend->>CMS: Return updated WholeCaskIssue with txHash
    CMS->>Admin: Display transaction status
```

## Benefits
- **Consistency**: Predictable token IDs based on existing data
- **Traceability**: Improved tracking of blockchain transactions
- **Auditability**: Direct link between database records and blockchain transactions
- **User Experience**: Better status tracking for NFT minting operations

## Related Files
- `apps/dcasks-backend/src/cask/models/whole-cask-issue.model.ts`
- `apps/dcasks-backend/src/cask/whole-cask-issue.service.ts`
- `apps/dcasks-backend/schema.gql`
- `apps/dcasks-cms/src/apollo/mutations/whole-cask-issues.ts`

## Memory Bank References
- Related to the "NFT Minting Improvements" work item in `memory-bank/progress.md`
- Implements "Deterministic Token ID Generation" mentioned in `memory-bank/techContext.md`
- Aligns with the "Blockchain Transaction Patterns" in `memory-bank/systemPatterns.md` 
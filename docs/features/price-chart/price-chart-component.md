# Price Chart Component

## Overview
The Price Chart component displays historical price data for NFT bottles in the DCasks platform. It shows both the selected bottle's price history (as a line chart) and other bottles' price points (as scatter points) for comparison. The component includes time range selection and allows users to view data in different time periods.

## User Experience Flow
```mermaid
flowchart TD
    Start[User Views NFT Detail] --> Chart[Price Chart Loads]
    Chart --> SelectRange[User Selects Time Range]
    SelectRange --> UpdateChart[Chart Updates with New Range]
    Chart --> HoverPoint[User Hovers Over Data Point]
    HoverPoint --> ShowTooltip[Display Tooltip with Price Info]
    Chart --> RefreshData[User Refreshes Data]
    RefreshData --> UpdateChart
```

## Technical Implementation
The Price Chart component uses Chart.js with React integration through react-chartjs-2. The component fetches data using Apollo Client and the useNftPriceChart hook.

```mermaid
flowchart TD
    PriceChart[PriceChart Component] --> TimeRange[TimeRangeSelector]
    PriceChart --> GraphQLHook[useNftPriceChart]
    PriceChart --> ChartLib[Chart.js via react-chartjs-2]
    
    GraphQLHook --> Apollo[Apollo Client]
    Apollo --> Backend[GraphQL Backend]
    
    PriceChart --> DataProcess[formatChartData]
    
    ChartLib --> TooltipConfig[Custom Tooltip Configuration]
    ChartLib --> ScalesConfig[Axes Configuration]
```

## Key Features
- Line chart for selected bottle price history
- Scatter plot for other bottles from the same project
- Time range selection with predefined periods
- Refresh button to reload latest data
- Individual tooltips for each data point showing:
  - Date and time of price point
  - Bottle number
  - Price in USDT

## Recent Updates
- **Individual Tooltip Enhancement**: Modified tooltip configuration to display individualized tooltips for each data point (instead of showing all datasets for the same x-axis point).
- Implemented using Chart.js tooltip configuration:
  - Changed mode from 'index' to 'nearest' to only show the closest point
  - Set intersect: true to ensure tooltip only appears when hovering directly over a point
  - Customized tooltip callbacks to display formatted date/time and bottle-specific price information

## Code References
- Main Component: `apps/dcasks-frontend/pages/nft-detail/_components/price-chart/index.tsx` - Primary Chart implementation
- GraphQL Query: `apps/dcasks-frontend/apollo/queries/nft-price-chart.ts` - Query for price data
- Apollo Hook: `apps/dcasks-frontend/hooks/apollo.ts` - useNftPriceChart hook
- Utility Functions: `apps/dcasks-frontend/pages/nft-detail/_components/price-chart/priceChartUtils.ts` - Data formatting

## API Endpoints
| Endpoint | Method | Description | Request | Response |
|----------|--------|-------------|---------|----------|
| GraphQL | POST | Price chart data | `{ tokenId, projectId, startDate, endDate }` | Price points with dates and values |

## State Management
- Local state using React useState for selected time range
- Apollo Client cache for GraphQL data
- Chart.js internal state for chart rendering and interactions

## Related Memory Bank References
- [activeContext.md](/memory-bank/activeContext.md) - Frontend stabilization efforts
- [techContext.md](/memory-bank/techContext.md) - Technical stack details
- [progress.md](/memory-bank/progress.md) - Current development status 
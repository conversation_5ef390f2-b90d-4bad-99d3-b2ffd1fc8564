# CaskCard Component System

## Overview

The CaskCard component system provides a unified approach to displaying cask data throughout the DCasks application. It implements a reusable, type-safe architecture that replaced the previously redundant implementations in `WholeCaskCard` and `MarketplaceCard`.

## Problem Solved

Before this implementation, different card components across the application had:
- Duplicated layout and styling code
- Inconsistent hover behaviors
- Similar but disconnected image handling
- Repetitive date formatting logic

The CaskCard component system solves these issues by providing a single, generic base component that can be easily extended for specific use cases.

## Architecture

```mermaid
flowchart TD
    CaskCard[CaskCard<T>] --> CaskHoverDetail[CaskHoverDetail<T>]
    CaskCard --> CaskCardFooter[CaskCardFooter<T>]

    WholeCaskCard[WholeCaskCard] --> CaskCard
    MarketplaceCard[MarketplaceCard] --> CaskCard

    CaskHoverDetail --> DefaultCaskDetails[DefaultCaskDetails<T>]
    CaskHoverDetail --> Button[Button]

    Utils[Utility Functions] -.-> DefaultCaskDetails
```

## Implementation Details

### TypeScript Generics

The component uses TypeScript generics to maintain type safety across different data models:

```typescript
export interface BaseCaskData {
  name: string
  cover: string
  ays: any // Date or string representation
}

export function CaskCard<T extends BaseCaskData>({
  data,
  // Other props
}: CaskCardProps<T>) {
  // Implementation
}
```

### Render Props Pattern

The component uses render props for customizable content areas:

```typescript
interface CaskCardProps<T extends BaseCaskData> {
  // ...other props
  renderDetails?: (data: T) => ReactNode
  renderPrice?: (data: T) => ReactNode
}
```

This allows each implementation to provide its own price display:

```typescript
// In WholeCaskCard
const renderPrice = (caskData: getWholeCasks_wholeCasks) => {
  return (
    <>
      <p className="transform text-xs text-muted-foreground">
        Whole Cask NFT
      </p>
      <p className="transform text-lg font-semibold text-black dark:text-white">
        {caskData.lastPrice} USD
      </p>
    </>
  )
}
```

### Animated Interactions

The component includes rich hover effects with staggered animations:

```jsx
<h3 className="translate-x-1/2 transform text-xl font-bold text-primary opacity-0 transition-all duration-300 ease-out group-hover:translate-x-0 group-hover:opacity-100">
  {data.name}
</h3>
```

## Benefits

1. **Reduced Duplication**: Eliminated redundant card implementations
2. **Type Safety**: Added robust TypeScript typing with generics
3. **Flexibility**: Allows for data-specific rendering while maintaining consistent structure
4. **Consistent UI**: Ensures visual consistency across different card types
5. **Maintainability**: Easier to update card styling or behavior in one place

## Usage Examples

See the implementations in `WholeCaskCard` and `MarketplaceCard` for complete usage examples.

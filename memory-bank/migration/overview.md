# DCasks Frontend Migration Plan

## Overview

This document provides a high-level overview of the plan to migrate the DCasks frontend from Next.js 12 Pages Router to Next.js 15 App Router with PayloadJS integration. The migration aims to improve maintainability, performance, and developer experience while preserving all existing functionality.

```mermaid
flowchart TD
    A[Current Architecture] --> B[Migration Process] --> C[Target Architecture]
    B --> D[1. Setup & Infrastructure]
    B --> E[2. Core Components]
    B --> F[3. Feature Migration]
    B --> G[4. Data Layer]
    B --> H[5. Launch & Handover]
```

## Current vs Target Architecture

| Aspect | Current | Target |
|--------|---------|--------|
| Framework | Next.js 12 (Pages Router) | Next.js 15 (App Router) |
| Data Fetching | Apollo Client | urql + GraphQL Codegen → PayloadJS |
| State Management | Jotai | React Query + Jotai |
| UI Components | Custom | shadcn/ui with custom styling |
| Testing | Minimal | Playwright |
| Blockchain | Reown AppKit + Ethers.js | Reown AppKit + Ethers.js |

## Key Migration Principles

1. **Component Structure**: Break down large components into smaller, focused components
2. **Data Flow**: Clear separation between server and client state
3. **File Organization**: Logical grouping of related files with descriptive naming
4. **Type Safety**: Strong typing throughout the codebase
5. **Performance**: Leverage server components for improved performance
6. **Testing**: Comprehensive testing with Playwright

## Migration Phases

1. **Setup and Infrastructure** (2 weeks)
2. **Core Components** (3 weeks)
3. **Feature Migration** (6 weeks)
4. **Data Layer** (3 weeks)
5. **Launch and Handover** (2 weeks)

**Total Duration: 16 weeks**

## Detailed Documentation

For more detailed information, please refer to the following documents:

- [Architecture Documentation](architecture/current-architecture.md)
- [Component Migration](components/inventory.md)
- [Feature Migration](features/home.md)
- [API Strategy](api/connector-strategy.md)
- [Project Timeline](progress/milestones.md)
- [Technical Resources](resources/tech-stack.md)

## Next Steps

1. Review the [detailed architecture documentation](architecture/target-architecture.md)
2. Explore the [component inventory](components/inventory.md)
3. Understand the [file organization principles](architecture/file-organization.md)
4. Check the [sprint planning](progress/sprint-planning.md) for current tasks

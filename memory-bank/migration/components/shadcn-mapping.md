# shadcn/ui Component Mapping

This document maps current DCasks frontend components to their shadcn/ui equivalents, providing a clear reference for the migration process.

## UI Component Mapping

| Current Component | shadcn/ui Equivalent | Customization Needed | Notes |
|-------------------|----------------------|----------------------|-------|
| `Button.tsx` | `ui/button.tsx` | Medium | Adapt to DCasks styling |
| `Card.tsx` | `ui/card.tsx` | Low | Add custom variants |
| `Input.tsx` | `ui/input.tsx` | Low | Match current styling |
| `Select.tsx` | `ui/select.tsx` | Medium | Add custom styling |
| `Checkbox.tsx` | `ui/checkbox.tsx` | Low | Match current styling |
| `Radio.tsx` | `ui/radio-group.tsx` | Low | Match current styling |
| `Tabs.tsx` | `ui/tabs.tsx` | Medium | Add custom styling |
| `Modal.tsx` | `ui/dialog.tsx` | High | Adapt to current modal patterns |
| `Tooltip.tsx` | `ui/tooltip.tsx` | Low | Match current styling |
| `Dropdown.tsx` | `ui/dropdown-menu.tsx` | Medium | Add custom styling |
| `Alert.tsx` | `ui/alert.tsx` | Low | Match current styling |
| `Badge.tsx` | `ui/badge.tsx` | Low | Add status variants |
| `Spinner.tsx` | Custom component | Medium | Create custom spinner |
| `Pagination.tsx` | `ui/pagination.tsx` | Medium | Match current styling |
| `TagStatus.tsx` | `ui/badge.tsx` | Medium | Create custom variants |
| `DatePicker.tsx` | `ui/calendar.tsx` + `ui/popover.tsx` | High | Create date picker from primitives |

## Form Component Integration

shadcn/ui provides excellent form components that integrate well with React Hook Form. Here's how to map the current form components:

```typescript
// Current implementation
<Input
  name="name"
  label="Name"
  register={register}
  error={errors.name}
  placeholder="Enter your name"
/>

// shadcn/ui implementation
<FormField
  control={form.control}
  name="name"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Name</FormLabel>
      <FormControl>
        <Input placeholder="Enter your name" {...field} />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## Custom Components

Some components will need to be custom-built using shadcn/ui primitives:

### 1. CaskCard

```typescript
// Using shadcn/ui Card components
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";

export function CaskCard({ cask }) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-48">
          <Image
            src={cask.cover}
            alt={cask.name}
            fill
            className="object-cover"
          />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold">{cask.name}</h3>
        <p className="text-sm text-muted-foreground">{cask.distillery}</p>
        <div className="mt-2 flex items-center justify-between">
          <span className="text-sm">Age: {cask.ays} years</span>
          <Badge variant="outline">{cask.region}</Badge>
        </div>
      </CardContent>
      <CardFooter className="border-t p-4">
        <div className="flex w-full items-center justify-between">
          <p className="font-medium">{formatPrice(cask.price)}</p>
          <Button size="sm">View Details</Button>
        </div>
      </CardFooter>
    </Card>
  );
}
```

### 2. PriceRangeFilter

```typescript
// Using shadcn/ui Slider component
import { Slider } from "@/components/ui/slider";

export function PriceRangeFilter({ min, max, value, onChange }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Price Range</h4>
        <span className="text-sm text-muted-foreground">
          {formatPrice(value[0])} - {formatPrice(value[1])}
        </span>
      </div>
      <Slider
        defaultValue={value}
        min={min}
        max={max}
        step={100}
        onValueChange={onChange}
      />
    </div>
  );
}
```

## Layout Components

Layout components will need to be custom-built but can leverage shadcn/ui primitives:

### Navbar

```typescript
// Using shadcn/ui components
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";

export function Navbar() {
  return (
    <header className="border-b bg-background">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Logo />
          <nav className="hidden md:flex items-center gap-6">
            {/* Navigation links */}
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <WalletConnect />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              {/* Mobile navigation */}
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
```

## Modal Components

Marketplace modals will need to be rebuilt using shadcn/ui Dialog component:

```typescript
// Using shadcn/ui Dialog
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export function ModalCreateSell({ isOpen, onClose, onSubmit, nft }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>List NFT for Sale</DialogTitle>
          <DialogDescription>
            Set a price to list your NFT on the marketplace.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={onSubmit}>
          {/* Form fields */}
          <DialogFooter>
            <Button type="submit">List for Sale</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

## Theme Customization

To maintain DCasks branding, we'll need to customize the shadcn/ui theme:

```typescript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
    },
  },
};
```

```css
/* globals.css */
:root {
  --background: 43 30% 95%;
  --foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --primary: 24 96% 39%;
  --primary-foreground: 60 9.1% 97.8%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --ring: 24 96% 39%;
}

.dark {
  --background: 20 14.3% 4.1%;
  --foreground: 60 9.1% 97.8%;
  --card: 20 14.3% 4.1%;
  --card-foreground: 60 9.1% 97.8%;
  --popover: 20 14.3% 4.1%;
  --popover-foreground: 60 9.1% 97.8%;
  --primary: 24 96% 39%;
  --primary-foreground: 60 9.1% 97.8%;
  --secondary: 12 6.5% 15.1%;
  --secondary-foreground: 60 9.1% 97.8%;
  --muted: 12 6.5% 15.1%;
  --muted-foreground: 24 5.4% 63.9%;
  --accent: 12 6.5% 15.1%;
  --accent-foreground: 60 9.1% 97.8%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 60 9.1% 97.8%;
  --border: 12 6.5% 15.1%;
  --input: 12 6.5% 15.1%;
  --ring: 24 96% 39%;
}
```

## Implementation Strategy

1. **Install shadcn/ui**: Set up the initial components
2. **Create Base Components**: Implement and style the basic UI components
3. **Build Complex Components**: Combine base components into more complex ones
4. **Migrate Page by Page**: Replace old components with new ones systematically

## Accessibility Improvements

shadcn/ui components are built with accessibility in mind, which will improve the overall accessibility of the DCasks frontend:

- Proper ARIA attributes
- Keyboard navigation
- Focus management
- Color contrast

## Performance Considerations

- Use dynamic imports for larger components
- Implement proper code splitting
- Optimize component re-renders

This mapping provides a clear path for migrating the current DCasks components to shadcn/ui equivalents while maintaining the existing functionality and improving the overall user experience.

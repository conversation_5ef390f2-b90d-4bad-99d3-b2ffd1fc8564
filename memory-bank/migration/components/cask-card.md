# CaskCard Component System

## Overview

The CaskCard component system provides a reusable, type-safe, and composable approach to displaying cask data throughout the application. It replaces the previously redundant implementations in `WholeCaskCard` and `MarketplaceCard` with a unified component architecture.

## Component Architecture

```mermaid
flowchart TD
    CaskCard[CaskCard<T>] --> CaskHoverDetail[CaskHoverDetail<T>]
    CaskCard --> CaskCardFooter[CaskCardFooter<T>]

    CaskHoverDetail --> DefaultCaskDetails[DefaultCaskDetails<T>]
    CaskHoverDetail --> Button[Button]

    Utils[Utility Functions] -.-> DefaultCaskDetails
    Types[TypeScript Types] -.-> CaskCard
    Types -.-> CaskHoverDetail
    Types -.-> CaskCardFooter
```

## Key Features

- **TypeScript Generics**: Uses generics for type safety across different cask data models
- **Composable Architecture**: Split into logical subcomponents for better maintainability
- **Renderless Props Pattern**: Uses render props for customizable content areas
- **Consistent Styling**: Maintains visual consistency while allowing data-specific displays
- **Animated Interactions**: Rich hover effects with staggered animations
- **Utility Functions**: Shared date formatting and age calculation logic

## Core Files

### Types (types.ts)
```typescript
export interface BaseCaskData {
  name: string
  cover: string
  ays: any // Date or string representation
}

export interface CaskCardProps<T extends BaseCaskData> {
  data: T
  className?: string
  cardType?: string
  renderDetails?: (data: T) => ReactNode
  renderPrice?: (data: T) => ReactNode
  buttonText?: string
  buttonProps?: Partial<ButtonProps>
  onButtonClick?: () => void
}
```

### Main Component (CaskCard.tsx)
```typescript
export function CaskCard<T extends BaseCaskData>({
  data,
  className,
  cardType,
  renderDetails,
  renderPrice = () => null,
  buttonText = 'See NFTs',
  buttonProps,
  onButtonClick,
}: CaskCardProps<T>) {
  // Implementation
}
```

### Utility Functions (utils.ts)
```typescript
// Format date to DD MMM YYYY (e.g., 03 Oct 1997)
export function formatDate(date: string | Date): string {
  return dayjs(date).format('DD MMM YYYY')
}

// Calculate years difference between now and a past date
export function calculateAge(date: string | Date): number {
  return dayjs().diff(dayjs(date), 'year')
}
```

## Implementation Examples

### WholeCaskCard
```typescript
const WholeCaskCard = ({ data, onButtonClick }: Props) => {
  // Render price display for whole cask card
  const renderPrice = (caskData: getWholeCasks_wholeCasks) => {
    return (
      <>
        <p className="transform text-xs text-muted-foreground">
          Whole Cask NFT
        </p>
        <p className="transform text-lg font-semibold text-black dark:text-white">
          {caskData.lastPrice} USD
        </p>
      </>
    )
  }

  return (
    <CaskCard
      data={data}
      cardType="Whole Cask NFT"
      renderPrice={renderPrice}
      onButtonClick={onButtonClick}
    />
  )
}
```

### MarketplaceCard
```typescript
const MarketplaceCard = ({ data, onButtonClick }: Props) => {
  // Render price display for marketplace card
  const renderPrice = (caskData: getCasks_casks) => {
    return (
      <>
        <p className="transform text-xs text-muted-foreground">
          {caskData.nftOnSaleAmount} NFTs on Sale
        </p>
        <p className="transform text-lg font-semibold text-black dark:text-white">
          from {caskData.minPrice} USDT
        </p>
      </>
    )
  }

  return (
    <CaskCard
      data={data}
      cardType={`${data.nftOnSaleAmount} NFTs on Sale`}
      renderPrice={renderPrice}
      onButtonClick={onButtonClick}
    />
  )
}
```

## Benefits

1. **Reduced Duplication**: Eliminated redundant card implementations
2. **Type Safety**: Added robust TypeScript typing with generics
3. **Flexibility**: Allows for data-specific rendering while maintaining consistent structure
4. **Consistent UI**: Ensures visual consistency across different card types
5. **Maintainability**: Easier to update card styling or behavior in one place
6. **Animation Consistency**: Standardized hover animations and transitions

# Component Inventory

This document provides a comprehensive inventory of all components in the current DCasks frontend that need to be migrated to the new architecture.

## Core Layout Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `BaseLayout.tsx` | Main layout wrapper | High | Medium |
| `Navbar.tsx` | Top navigation bar | High | Medium |
| `Footer.tsx` | Page footer | High | Low |

## Home Page Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `HeroSection.tsx` | Hero banner on home page | High | Medium |
| `SectionFeaturedCasks.tsx` | Featured casks showcase | High | Medium |
| `WhyInvestWhiskySection.tsx` | Marketing section | Medium | Low |
| `AboutUsSection.tsx` | Company information | Medium | Low |
| `OurTechSection.tsx` | Technology showcase | Medium | Low |
| `SellYourCasksSection.tsx` | CTA section | Medium | Low |
| `LatestBlogPostsSection.tsx` | Blog posts showcase | Low | Medium |
| `LiquidStacking.tsx` | Staking information | Medium | Medium |

## Marketplace Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `RegularCaskList.tsx` | Regular cask listings | High | High |
| `WholeCaskList.tsx` | Whole cask listings | High | High |
| `CaskCard.tsx` | Individual cask card | High | Medium |
| `CaskFilters.tsx` | Filtering options | High | Medium |
| `PriceRangeFilter.tsx` | Price range selector | Medium | Medium |
| `SortOptions.tsx` | Sorting controls | Medium | Low |
| `Pagination.tsx` | Pagination controls | Medium | Low |

## NFT Detail Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `CaskDetail.tsx` | Cask details display | High | High |
| `CaskImages.tsx` | Cask image gallery | High | Medium |
| `CaskInfo.tsx` | Cask information | High | Medium |
| `PriceHistory.tsx` | Price history chart | Medium | High |
| `AssetActivities.tsx` | Activity history | Medium | Medium |
| `BidList.tsx` | List of bids | Medium | Medium |
| `BadgeStatus.tsx` | Status indicator | Medium | Low |

## User Profile Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `ProfileHeader.tsx` | User profile header | High | Medium |
| `MyNFTs.tsx` | User's NFT collection | High | High |
| `TransactionHistory.tsx` | Transaction list | Medium | Medium |
| `Portfolio.tsx` | Portfolio overview | Medium | High |
| `Settings.tsx` | User settings | Low | Medium |

## Form Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `Input.tsx` | Text input field | High | Low |
| `Select.tsx` | Dropdown select | High | Low |
| `Checkbox.tsx` | Checkbox input | High | Low |
| `Radio.tsx` | Radio button | High | Low |
| `DatePicker.tsx` | Date selection | Medium | Medium |
| `FileUpload.tsx` | File upload | Medium | Medium |
| `FormError.tsx` | Error message | High | Low |

## UI Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `Button.tsx` | Button component | High | Low |
| `Card.tsx` | Card container | High | Low |
| `Badge.tsx` | Status badge | High | Low |
| `Tabs.tsx` | Tab navigation | High | Medium |
| `Modal.tsx` | Modal dialog | High | Medium |
| `Tooltip.tsx` | Tooltip | Medium | Low |
| `Dropdown.tsx` | Dropdown menu | Medium | Medium |
| `Spinner.tsx` | Loading spinner | Medium | Low |
| `Alert.tsx` | Alert message | Medium | Low |
| `Breadcrumb.tsx` | Breadcrumb navigation | Medium | Low |
| `Pagination.tsx` | Pagination controls | Medium | Low |
| `TagStatus.tsx` | Status tag | Medium | Low |

## Marketplace Modal Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `ModalCreateSell.tsx` | Create sell listing | High | High |
| `ModalMatchSell.tsx` | Match/buy listing | High | High |
| `ModalCancelSell.tsx` | Cancel sell listing | High | Medium |
| `ModalCreateAuction.tsx` | Create auction | High | High |
| `ModalBidAuction.tsx` | Bid on auction | High | High |
| `ModalEndAuction.tsx` | End auction | High | Medium |
| `ModalCancelAuction.tsx` | Cancel auction | High | Medium |
| `ModalCreateOffer.tsx` | Create offer | High | High |
| `ModalCancelOffer.tsx` | Cancel offer | High | Medium |
| `ModalAcceptRisk.tsx` | Risk acceptance | High | Medium |
| `ModalChangeProfit.tsx` | Change profit model | Medium | High |

## Blockchain Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `WalletConnect.tsx` | Wallet connection | High | Medium |
| `TransactionStatus.tsx` | Transaction status | High | Medium |
| `NetworkSwitch.tsx` | Network switcher | Medium | Medium |
| `AddressDisplay.tsx` | Address display | Medium | Low |
| `SignatureRequest.tsx` | Signature request | High | Medium |

## Chart Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `PriceChart.tsx` | Price history chart | Medium | High |
| `PortfolioChart.tsx` | Portfolio value chart | Medium | High |
| `DistributionChart.tsx` | Asset distribution | Low | Medium |

## Utility Components

| Component | Description | Priority | Complexity |
|-----------|-------------|----------|------------|
| `ErrorBoundary.tsx` | Error handling | High | Medium |
| `LazyLoad.tsx` | Lazy loading | Medium | Low |
| `SEO.tsx` | SEO metadata | High | Low |
| `ImageOptimized.tsx` | Optimized images | High | Medium |
| `CopyToClipboard.tsx` | Copy functionality | Medium | Low |
| `QRCode.tsx` | QR code generator | Low | Low |

## Total Component Count

- **Total Components**: ~70
- **High Priority**: ~30
- **Medium Priority**: ~30
- **Low Priority**: ~10

## Migration Approach

The migration will follow these principles:

1. **Prioritize Core Components**: Focus on high-priority components first
2. **Reuse Where Possible**: Leverage shadcn/ui for common UI patterns
3. **Maintain Consistency**: Ensure consistent styling and behavior
4. **Test Thoroughly**: Verify functionality after migration

## Next Steps

1. Map each component to its shadcn/ui equivalent (see `shadcn-mapping.md`)
2. Create a migration schedule based on priority
3. Track migration progress (see `migration-status.md`)

# GraphQL Endpoints

This document catalogs the GraphQL endpoints used in the current DCasks frontend that need to be supported in the new architecture.

## Overview

The current DCasks frontend uses Apollo Client to interact with a GraphQL API. The new architecture will initially continue to use these same GraphQL endpoints, with a plan to transition to PayloadJS API in the future.

## Apollo Client Setup

The current Apollo Client setup is as follows:

```typescript
// apollo/client.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
});

const authLink = setContext((_, { headers }) => {
  // Get the authentication token from local storage if it exists
  const token = localStorage.getItem('token');
  // Return the headers to the context so httpLink can read them
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    },
  };
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

export default client;
```

In the new architecture, we'll need to adapt this for the App Router pattern:

```typescript
// lib/apollo/client.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { registerApolloClient } from '@apollo/experimental-nextjs-app-support/rsc';

// Create a client-side only version
export function createApolloClient() {
  const httpLink = createHttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
  });

  const authLink = setContext((_, { headers }) => {
    // Get the authentication token from local storage if it exists
    // Note: This only works in client components
    let token = '';
    if (typeof window !== 'undefined') {
      token = localStorage.getItem('token') || '';
    }
    
    // Return the headers to the context so httpLink can read them
    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : '',
      },
    };
  });

  return new ApolloClient({
    link: authLink.concat(httpLink),
    cache: new InMemoryCache(),
  });
}

// Create a server-side version (for server components)
export function createServerApolloClient() {
  return new ApolloClient({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
    cache: new InMemoryCache(),
  });
}

// Export a singleton client for client components
export const { getClient } = registerApolloClient(() => {
  return createApolloClient();
});
```

## Core GraphQL Queries

### Casks Queries

#### 1. Get Casks

```graphql
query getCasks($limit: Int = 10, $skip: Int = 0, $sort: JSON, $where: JSON) {
  casks(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    creator
    cover
    name
    desc
    img
    info
    distillery
    commissionInfo
    projectId
    tokenId
    nftOnSaleAmount
    minPrice
    maxPrice
    region
    nfts
    maxAmount
    targetAmount
    nftOnSale
    nftOnAuction
    nftAmount
    updatedAt
    ays
    targetBottlingDate
  }
}
```

#### 2. Count Casks

```graphql
query countCasks($where: JSON) {
  countCasks(where: $where) {
    count
  }
}
```

#### 3. Get My Casks Issues

```graphql
query getMyCasksIssues(
  $limit: Int = 10
  $skip: Int = 0
  $sort: JSON
  $where: JSON
) {
  myCaskIssues(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    commissionInfo
    cover
    createdAt
    creator
    desc
    distillery
    info
    img
    maxAmount
    targetAmount
    name
    nonce
    region
    signature
    status
    updatedAt
    ays
    targetBottlingDate
  }
}
```

### NFT Queries

#### 1. Get NFTs

```graphql
query getNfts($limit: Int = 10, $skip: Int = 0, $sort: JSON, $where: JSON) {
  nfts(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    inMarket
    owner
    projectId
    tokenId
    lastPrice
    lastTrade
  }
}
```

#### 2. Count NFTs

```graphql
query countNFTs($where: JSON) {
  countNFTs(where: $where) {
    count
  }
}
```

#### 3. Get NFTs with Project

```graphql
query getNftsWithProject(
  $limit: Int = 10
  $skip: Int = 0
  $sort: JSON
  $where: JSON
) {
  nfts(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    inMarket
    owner
    projectId
    tokenId
    lastPrice
    lastTrade
    project {
      name
      img
      cover
      ays
    }
  }
}
```

#### 4. Get NFT Project

```graphql
query getAllNftForProjectId($where: JSON) {
  allNftProjects(where: $where) {
    inMarket
    owner
    projectId
    tokenId
    lastPrice
    lastTrade
  }
}
```

### Market Queries

#### 1. Get Markets

```graphql
query getMarket($limit: Int, $skip: Int = 0, $sort: JSON, $where: JSON) {
  markets(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    auctionHistories
    buyer
    endTime
    latestBidder
    latestPrice
    latestWeiPrice
    orderId
    owner
    paymentToken
    price
    projectId
    seller
    startPrice
    startTime
    startWeiPrice
    tokenId
    txHash
    type
    weiPrice
  }
}
```

#### 2. Get Offer Markets

```graphql
query getOfferMarket($limit: Int, $skip: Int = 0, $sort: JSON, $where: JSON) {
  markets(limit: $limit, skip: $skip, sort: $sort, where: $where) {
    orderId
    tokenId
    projectId
    project {
      name
      img
    }
    nftOwner
    owner
    price
    weiPrice
    paymentToken
    type
    createdAt
    updatedAt
  }

  countMarkets(where: $where) {
    count
  }
}
```

### User Queries

#### 1. Get My Profile

```graphql
query getMyProfile {
  myProfile {
    _id
    address
    email
    name
    avatar
    role
    status
    createdAt
    updatedAt
  }
}
```

#### 2. Get Portfolio

```graphql
query getPortfolio {
  getPortfolio {
    totalValue
    totalProfit
    totalNfts
    history {
      date
      value
    }
  }
}
```

### System Queries

#### 1. Get System Config

```graphql
query getSystemConfig($input: GetConfigurationSystemInput!) {
  configurationSystem(input: $input) {
    key
    value
  }
}
```

## Core GraphQL Mutations

### User Mutations

#### 1. Update Profile

```graphql
mutation updateProfile($input: UpdateProfileInput!) {
  updateProfile(input: $input) {
    _id
    address
    email
    name
    avatar
  }
}
```

### Cask Mutations

#### 1. Create Cask Issue

```graphql
mutation createCaskIssue($input: CreateCaskIssueInput!) {
  createCaskIssue(input: $input) {
    _id
    name
    desc
    info
    distillery
    region
    maxAmount
    targetAmount
    img
    cover
    commissionInfo
    creator
    status
    createdAt
    updatedAt
  }
}
```

#### 2. Update Cask Issue

```graphql
mutation updateCaskIssue($input: UpdateCaskIssueInput!) {
  updateCaskIssue(input: $input) {
    _id
    name
    desc
    info
    distillery
    region
    maxAmount
    targetAmount
    img
    cover
    commissionInfo
    creator
    status
    createdAt
    updatedAt
  }
}
```

### Market Mutations

#### 1. Create Market

```graphql
mutation createMarket($input: CreateMarketInput!) {
  createMarket(input: $input) {
    _id
    orderId
    tokenId
    projectId
    type
    price
    weiPrice
    paymentToken
    seller
    buyer
    txHash
    createdAt
    updatedAt
  }
}
```

#### 2. Update Market

```graphql
mutation updateMarket($input: UpdateMarketInput!) {
  updateMarket(input: $input) {
    _id
    orderId
    tokenId
    projectId
    type
    price
    weiPrice
    paymentToken
    seller
    buyer
    txHash
    createdAt
    updatedAt
  }
}
```

## Data Fetching Strategy

In the new architecture, we'll implement a data fetching layer that abstracts the GraphQL queries:

```typescript
// lib/api/casks.ts
import { apolloClient } from '@/lib/apollo/client';
import { GET_CASKS, COUNT_CASKS } from '@/lib/apollo/queries';

export async function fetchCasks({ limit = 10, skip = 0, filters = {}, sort = {} }) {
  try {
    const { data } = await apolloClient.query({
      query: GET_CASKS,
      variables: {
        limit,
        skip,
        where: filters,
        sort,
      },
    });
    
    return data.casks || [];
  } catch (error) {
    console.error('Error fetching casks:', error);
    return [];
  }
}

export async function fetchCasksCount(filters = {}) {
  try {
    const { data } = await apolloClient.query({
      query: COUNT_CASKS,
      variables: {
        where: filters,
      },
    });
    
    return data.countCasks?.count || 0;
  } catch (error) {
    console.error('Error fetching casks count:', error);
    return 0;
  }
}
```

## Server Components vs. Client Components

For the new architecture, we'll use a combination of server components and client components:

### Server Components

Server components will fetch data directly from the API:

```typescript
// app/(frontend)/marketplace/page.tsx
import { fetchCasks, fetchCasksCount } from '@/lib/api/casks';

export default async function MarketplacePage({ searchParams }) {
  // Parse search params
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const limit = 9; // Items per page
  
  // Build filter object for API
  const filters = {
    // ...filter logic
  };
  
  // Fetch casks and total count
  const [casks, totalCount] = await Promise.all([
    fetchCasks({
      limit,
      skip: (page - 1) * limit,
      filters,
    }),
    fetchCasksCount(filters),
  ]);
  
  // Render page with data
  return (
    <div>
      {/* Page content */}
    </div>
  );
}
```

### Client Components

Client components will use React Query for data fetching:

```typescript
// components/marketplace/CaskFilters.tsx
'use client';

import { useQuery } from '@tanstack/react-query';
import { fetchFilterOptions } from '@/lib/api/casks';

export function CaskFilters() {
  const { data, isLoading } = useQuery({
    queryKey: ['caskFilters'],
    queryFn: fetchFilterOptions,
  });
  
  if (isLoading) return <div>Loading filters...</div>;
  
  return (
    <div>
      {/* Filter UI */}
    </div>
  );
}
```

## Authentication and Authorization

The current DCasks frontend uses wallet-based authentication. In the new architecture, we'll continue to use this approach:

```typescript
// lib/apollo/client.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

export function createApolloClient(token = '') {
  const httpLink = createHttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
  });

  const authLink = setContext((_, { headers }) => {
    // Use the provided token or get it from localStorage
    let authToken = token;
    if (!authToken && typeof window !== 'undefined') {
      authToken = localStorage.getItem('token') || '';
    }
    
    return {
      headers: {
        ...headers,
        authorization: authToken ? `Bearer ${authToken}` : '',
      },
    };
  });

  return new ApolloClient({
    link: authLink.concat(httpLink),
    cache: new InMemoryCache(),
  });
}
```

## Error Handling

We'll implement consistent error handling across the application:

```typescript
// lib/api/error.ts
export class ApiError extends Error {
  constructor(message, public statusCode = 500, public data = {}) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error) {
  console.error('API Error:', error);
  
  if (error.graphQLErrors) {
    // Handle GraphQL errors
    const message = error.graphQLErrors[0]?.message || 'An error occurred';
    return new ApiError(message, 400, error.graphQLErrors[0]);
  }
  
  if (error.networkError) {
    // Handle network errors
    return new ApiError('Network error', 503);
  }
  
  // Handle other errors
  return new ApiError(error.message || 'An unknown error occurred');
}
```

## Future Migration to PayloadJS

In the future, we'll migrate from GraphQL to PayloadJS API. To facilitate this transition, we'll implement an abstraction layer:

```typescript
// lib/api/casks.ts
import { apolloClient } from '@/lib/apollo/client';
import { GET_CASKS, COUNT_CASKS } from '@/lib/apollo/queries';

export async function fetchCasks({ limit = 10, skip = 0, filters = {}, sort = {} }) {
  // Current implementation using GraphQL
  try {
    const { data } = await apolloClient.query({
      query: GET_CASKS,
      variables: {
        limit,
        skip,
        where: filters,
        sort,
      },
    });
    
    return data.casks || [];
  } catch (error) {
    console.error('Error fetching casks:', error);
    return [];
  }
  
  // Future implementation using PayloadJS
  // const payload = await getPayload();
  // return payload.find({
  //   collection: 'casks',
  //   limit,
  //   page: Math.floor(skip / limit) + 1,
  //   where: convertFilters(filters),
  //   sort: convertSort(sort),
  // });
}
```

This abstraction layer will allow us to switch from GraphQL to PayloadJS with minimal changes to the rest of the application.

## Testing Strategy

1. **Unit Testing**
   - Test API utility functions
   - Test error handling
   - Test data transformation

2. **Integration Testing**
   - Test API calls with mock responses
   - Test authentication flow
   - Test error scenarios

3. **End-to-End Testing**
   - Test complete data flow from API to UI
   - Test authentication and authorization
   - Test error handling and recovery

## Migration Steps

1. Set up Apollo Client for App Router
2. Implement API utility functions
3. Create data fetching abstraction layer
4. Integrate with server and client components
5. Test and optimize

## Acceptance Criteria

- All GraphQL queries and mutations are supported
- Authentication works correctly
- Error handling is robust
- Performance is acceptable
- Abstraction layer facilitates future migration to PayloadJS

This document provides a comprehensive catalog of the GraphQL endpoints used in the current DCasks frontend and a plan for supporting them in the new architecture.

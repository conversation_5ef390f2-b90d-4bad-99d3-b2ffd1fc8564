# API Connector Strategy

This document outlines the strategy for creating a flexible API connector that can be easily switched from the current GraphQL API to the future PayloadJS API.

## Goals

1. **Abstraction**: Create an abstraction layer that hides the underlying API implementation
2. **Consistency**: Provide a consistent interface for data fetching across the application
3. **Flexibility**: Allow for easy switching between different API implementations
4. **Type Safety**: Ensure type safety throughout the data flow
5. **Performance**: Optimize for performance with caching and batching
6. **Error Handling**: Implement consistent error handling

## Architecture Overview

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Components │────►│ API Clients │────►│ API Sources │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
                          │
                          │
                          ▼
                    ┌─────────────┐
                    │             │
                    │   Models    │
                    │             │
                    └─────────────┘
```

### 1. API Sources

These are the actual API implementations:

- **GraphQL API**: The current API implementation
- **PayloadJS API**: The future API implementation

### 2. API Clients

These are the clients that interact with the API sources:

- **Apollo Client**: For GraphQL API
- **PayloadJS Client**: For PayloadJS API

### 3. API Connectors

These are the abstraction layers that provide a consistent interface:

- **Data Fetching**: Functions for fetching data
- **Data Mutation**: Functions for mutating data
- **Authentication**: Functions for authentication
- **Error Handling**: Functions for handling errors

### 4. Models

These are the data models that define the shape of the data:

- **Entity Models**: Define the shape of entities (Casks, NFTs, etc.)
- **Request Models**: Define the shape of API requests
- **Response Models**: Define the shape of API responses

## Implementation Strategy

### 1. Define Data Models

```typescript
// types/models/cask.ts
export interface Cask {
  id: string;
  name: string;
  description: string;
  distillery: string;
  region: string;
  ays: number;
  targetBottlingDate: string;
  price: number;
  projectId: string;
  tokenId: string;
  cover: string;
  images: string[];
  creator: string;
  createdAt: string;
  updatedAt: string;
}

// types/models/nft.ts
export interface NFT {
  id: string;
  tokenId: string;
  projectId: string;
  owner: string;
  inMarket: boolean;
  lastPrice: number;
  lastTrade: string;
  createdAt: string;
  updatedAt: string;
}

// types/models/market.ts
export interface Market {
  id: string;
  orderId: string;
  tokenId: string;
  projectId: string;
  type: 'sell' | 'auction' | 'offer';
  price: number;
  weiPrice: string;
  paymentToken: string;
  seller: string;
  buyer: string;
  txHash: string;
  createdAt: string;
  updatedAt: string;
}

// types/models/user.ts
export interface User {
  id: string;
  address: string;
  email: string;
  name: string;
  avatar: string;
  role: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}
```

### 2. Define API Request and Response Types

```typescript
// types/api/requests.ts
export interface PaginationParams {
  limit?: number;
  skip?: number;
  page?: number;
}

export interface FilterParams {
  [key: string]: any;
}

export interface SortParams {
  [key: string]: 1 | -1;
}

export interface FetchCasksParams extends PaginationParams {
  filters?: FilterParams;
  sort?: SortParams;
}

// types/api/responses.ts
export interface ApiResponse<T> {
  data: T;
  error?: ApiError;
}

export interface ApiError {
  message: string;
  code: string;
  details?: any;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}
```

### 3. Create API Connector Interface

```typescript
// lib/api/connector/types.ts
import { 
  Cask, NFT, Market, User,
  FetchCasksParams, ApiResponse, PaginatedResponse
} from '@/types';

export interface ApiConnector {
  // Casks
  fetchCasks(params: FetchCasksParams): Promise<ApiResponse<PaginatedResponse<Cask>>>;
  fetchCaskById(id: string): Promise<ApiResponse<Cask>>;
  createCask(cask: Partial<Cask>): Promise<ApiResponse<Cask>>;
  updateCask(id: string, cask: Partial<Cask>): Promise<ApiResponse<Cask>>;
  
  // NFTs
  fetchNFTs(params: FetchCasksParams): Promise<ApiResponse<PaginatedResponse<NFT>>>;
  fetchNFTById(id: string): Promise<ApiResponse<NFT>>;
  
  // Markets
  fetchMarkets(params: FetchCasksParams): Promise<ApiResponse<PaginatedResponse<Market>>>;
  fetchMarketById(id: string): Promise<ApiResponse<Market>>;
  createMarket(market: Partial<Market>): Promise<ApiResponse<Market>>;
  updateMarket(id: string, market: Partial<Market>): Promise<ApiResponse<Market>>;
  
  // Users
  fetchCurrentUser(): Promise<ApiResponse<User>>;
  updateUser(user: Partial<User>): Promise<ApiResponse<User>>;
  
  // Authentication
  login(address: string, signature: string): Promise<ApiResponse<{ token: string }>>;
  logout(): Promise<ApiResponse<void>>;
}
```

### 4. Implement GraphQL Connector

```typescript
// lib/api/connector/graphql.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { 
  ApiConnector, Cask, NFT, Market, User,
  FetchCasksParams, ApiResponse, PaginatedResponse
} from '@/types';
import { 
  GET_CASKS, GET_CASK_BY_ID, CREATE_CASK, UPDATE_CASK,
  GET_NFTS, GET_NFT_BY_ID,
  GET_MARKETS, GET_MARKET_BY_ID, CREATE_MARKET, UPDATE_MARKET,
  GET_CURRENT_USER, UPDATE_USER,
  LOGIN, LOGOUT
} from './graphql/queries';

export class GraphQLConnector implements ApiConnector {
  private client: ApolloClient<any>;
  
  constructor(token?: string) {
    const httpLink = createHttpLink({
      uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
    });
    
    const authLink = setContext((_, { headers }) => {
      // Use the provided token or get it from localStorage
      let authToken = token;
      if (!authToken && typeof window !== 'undefined') {
        authToken = localStorage.getItem('token') || '';
      }
      
      return {
        headers: {
          ...headers,
          authorization: authToken ? `Bearer ${authToken}` : '',
        },
      };
    });
    
    this.client = new ApolloClient({
      link: authLink.concat(httpLink),
      cache: new InMemoryCache(),
    });
  }
  
  // Casks
  async fetchCasks(params: FetchCasksParams): Promise<ApiResponse<PaginatedResponse<Cask>>> {
    try {
      const { data } = await this.client.query({
        query: GET_CASKS,
        variables: {
          limit: params.limit || 10,
          skip: params.skip || 0,
          where: params.filters || {},
          sort: params.sort || {},
        },
      });
      
      const { data: countData } = await this.client.query({
        query: COUNT_CASKS,
        variables: {
          where: params.filters || {},
        },
      });
      
      return {
        data: {
          items: data.casks.map(this.mapCask),
          total: countData.countCasks.count,
          page: Math.floor((params.skip || 0) / (params.limit || 10)) + 1,
          limit: params.limit || 10,
          pages: Math.ceil(countData.countCasks.count / (params.limit || 10)),
        },
      };
    } catch (error) {
      return {
        data: {
          items: [],
          total: 0,
          page: 1,
          limit: params.limit || 10,
          pages: 0,
        },
        error: this.handleError(error),
      };
    }
  }
  
  async fetchCaskById(id: string): Promise<ApiResponse<Cask>> {
    try {
      const { data } = await this.client.query({
        query: GET_CASK_BY_ID,
        variables: {
          id,
        },
      });
      
      return {
        data: this.mapCask(data.cask),
      };
    } catch (error) {
      return {
        data: null,
        error: this.handleError(error),
      };
    }
  }
  
  // ... implement other methods
  
  // Helper methods
  private mapCask(cask: any): Cask {
    return {
      id: cask._id,
      name: cask.name,
      description: cask.desc,
      distillery: cask.distillery,
      region: cask.region,
      ays: cask.ays,
      targetBottlingDate: cask.targetBottlingDate,
      price: cask.price,
      projectId: cask.projectId,
      tokenId: cask.tokenId,
      cover: cask.cover,
      images: cask.img,
      creator: cask.creator,
      createdAt: cask.createdAt,
      updatedAt: cask.updatedAt,
    };
  }
  
  private handleError(error: any): ApiError {
    console.error('GraphQL Error:', error);
    
    if (error.graphQLErrors) {
      return {
        message: error.graphQLErrors[0]?.message || 'An error occurred',
        code: error.graphQLErrors[0]?.extensions?.code || 'UNKNOWN_ERROR',
        details: error.graphQLErrors[0],
      };
    }
    
    if (error.networkError) {
      return {
        message: 'Network error',
        code: 'NETWORK_ERROR',
      };
    }
    
    return {
      message: error.message || 'An unknown error occurred',
      code: 'UNKNOWN_ERROR',
    };
  }
}
```

### 5. Implement PayloadJS Connector (Future)

```typescript
// lib/api/connector/payload.ts
import { 
  ApiConnector, Cask, NFT, Market, User,
  FetchCasksParams, ApiResponse, PaginatedResponse
} from '@/types';

export class PayloadConnector implements ApiConnector {
  private payload: any;
  
  constructor() {
    // Initialize PayloadJS client
    // This will be implemented in the future
  }
  
  // Casks
  async fetchCasks(params: FetchCasksParams): Promise<ApiResponse<PaginatedResponse<Cask>>> {
    try {
      const page = params.page || Math.floor((params.skip || 0) / (params.limit || 10)) + 1;
      const limit = params.limit || 10;
      
      const response = await this.payload.find({
        collection: 'casks',
        page,
        limit,
        where: this.convertFilters(params.filters),
        sort: this.convertSort(params.sort),
      });
      
      return {
        data: {
          items: response.docs.map(this.mapCask),
          total: response.totalDocs,
          page: response.page,
          limit: response.limit,
          pages: response.totalPages,
        },
      };
    } catch (error) {
      return {
        data: {
          items: [],
          total: 0,
          page: 1,
          limit: params.limit || 10,
          pages: 0,
        },
        error: this.handleError(error),
      };
    }
  }
  
  // ... implement other methods
  
  // Helper methods
  private mapCask(cask: any): Cask {
    return {
      id: cask.id,
      name: cask.name,
      description: cask.description,
      distillery: cask.distillery,
      region: cask.region,
      ays: cask.ays,
      targetBottlingDate: cask.targetBottlingDate,
      price: cask.price,
      projectId: cask.projectId,
      tokenId: cask.tokenId,
      cover: cask.cover?.url,
      images: cask.images?.map(img => img.image.url) || [],
      creator: cask.creator,
      createdAt: cask.createdAt,
      updatedAt: cask.updatedAt,
    };
  }
  
  private convertFilters(filters: any): any {
    // Convert GraphQL filters to PayloadJS filters
    // This will be implemented in the future
    return filters;
  }
  
  private convertSort(sort: any): any {
    // Convert GraphQL sort to PayloadJS sort
    // This will be implemented in the future
    return sort;
  }
  
  private handleError(error: any): ApiError {
    console.error('PayloadJS Error:', error);
    
    return {
      message: error.message || 'An unknown error occurred',
      code: error.code || 'UNKNOWN_ERROR',
    };
  }
}
```

### 6. Create API Factory

```typescript
// lib/api/connector/factory.ts
import { ApiConnector } from '@/types';
import { GraphQLConnector } from './graphql';
import { PayloadConnector } from './payload';

export enum ApiType {
  GraphQL = 'graphql',
  Payload = 'payload',
}

export class ApiFactory {
  private static instance: ApiFactory;
  private connectors: Map<ApiType, ApiConnector>;
  private activeConnector: ApiType;
  
  private constructor() {
    this.connectors = new Map();
    this.activeConnector = ApiType.GraphQL; // Default to GraphQL
    
    // Initialize connectors
    this.connectors.set(ApiType.GraphQL, new GraphQLConnector());
    // PayloadJS connector will be added in the future
    // this.connectors.set(ApiType.Payload, new PayloadConnector());
  }
  
  public static getInstance(): ApiFactory {
    if (!ApiFactory.instance) {
      ApiFactory.instance = new ApiFactory();
    }
    
    return ApiFactory.instance;
  }
  
  public getConnector(): ApiConnector {
    return this.connectors.get(this.activeConnector);
  }
  
  public setActiveConnector(type: ApiType): void {
    if (!this.connectors.has(type)) {
      throw new Error(`Connector type ${type} not found`);
    }
    
    this.activeConnector = type;
  }
  
  public createConnector(type: ApiType, token?: string): ApiConnector {
    switch (type) {
      case ApiType.GraphQL:
        return new GraphQLConnector(token);
      case ApiType.Payload:
        // PayloadJS connector will be added in the future
        // return new PayloadConnector();
        throw new Error('PayloadJS connector not implemented yet');
      default:
        throw new Error(`Unknown connector type: ${type}`);
    }
  }
}

// Export a singleton instance
export const apiFactory = ApiFactory.getInstance();

// Export a helper function to get the active connector
export function getApiConnector(): ApiConnector {
  return apiFactory.getConnector();
}
```

### 7. Create API Hooks

```typescript
// hooks/api/useCasks.ts
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getApiConnector } from '@/lib/api/connector/factory';
import { FetchCasksParams, Cask } from '@/types';

export function useCasks(params: FetchCasksParams) {
  const connector = getApiConnector();
  
  return useQuery({
    queryKey: ['casks', params],
    queryFn: () => connector.fetchCasks(params),
  });
}

export function useCask(id: string) {
  const connector = getApiConnector();
  
  return useQuery({
    queryKey: ['cask', id],
    queryFn: () => connector.fetchCaskById(id),
  });
}

export function useCreateCask() {
  const connector = getApiConnector();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (cask: Partial<Cask>) => connector.createCask(cask),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['casks'] });
    },
  });
}

export function useUpdateCask() {
  const connector = getApiConnector();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, cask }: { id: string; cask: Partial<Cask> }) => 
      connector.updateCask(id, cask),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['casks'] });
      queryClient.invalidateQueries({ queryKey: ['cask', variables.id] });
    },
  });
}
```

### 8. Create Server-Side API Functions

```typescript
// lib/api/server/casks.ts
import { getApiConnector, ApiFactory, ApiType } from '@/lib/api/connector/factory';
import { FetchCasksParams } from '@/types';

export async function fetchCasks(params: FetchCasksParams) {
  const connector = getApiConnector();
  const response = await connector.fetchCasks(params);
  
  if (response.error) {
    console.error('Error fetching casks:', response.error);
    return {
      items: [],
      total: 0,
      page: 1,
      limit: params.limit || 10,
      pages: 0,
    };
  }
  
  return response.data;
}

export async function fetchCaskById(id: string) {
  const connector = getApiConnector();
  const response = await connector.fetchCaskById(id);
  
  if (response.error) {
    console.error('Error fetching cask:', response.error);
    return null;
  }
  
  return response.data;
}
```

### 9. Usage in Components

#### Server Component

```tsx
// app/(frontend)/marketplace/page.tsx
import { fetchCasks, fetchCasksCount } from '@/lib/api/server/casks';
import { CaskList } from '@/components/marketplace/CaskList';

interface MarketplacePageProps {
  searchParams: {
    page?: string;
    limit?: string;
    // ... other params
  };
}

export default async function MarketplacePage({ searchParams }: MarketplacePageProps) {
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const limit = searchParams.limit ? parseInt(searchParams.limit) : 10;
  
  const casksData = await fetchCasks({
    page,
    limit,
    // ... other params
  });
  
  return (
    <div>
      <h1>Marketplace</h1>
      <CaskList casks={casksData.items} pagination={casksData} />
    </div>
  );
}
```

#### Client Component

```tsx
// components/marketplace/CaskFilters.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCasks } from '@/hooks/api/useCasks';

export function CaskFilters() {
  const router = useRouter();
  const [filters, setFilters] = useState({
    // ... filter state
  });
  
  const { data, isLoading } = useCasks({
    limit: 0, // Just get the filter options, not the actual casks
    filters: {
      // ... filter options
    },
  });
  
  const handleApplyFilters = () => {
    // Update URL with filters
    const params = new URLSearchParams();
    // ... add filter params
    router.push(`/marketplace?${params.toString()}`);
  };
  
  if (isLoading) {
    return <div>Loading filters...</div>;
  }
  
  return (
    <div>
      {/* Filter UI */}
      <button onClick={handleApplyFilters}>Apply Filters</button>
    </div>
  );
}
```

## Switching Between API Implementations

To switch between API implementations, we simply need to change the active connector:

```typescript
// lib/api/config.ts
import { apiFactory, ApiType } from '@/lib/api/connector/factory';

// Switch to PayloadJS API
export function switchToPayloadAPI() {
  apiFactory.setActiveConnector(ApiType.Payload);
}

// Switch to GraphQL API
export function switchToGraphQLAPI() {
  apiFactory.setActiveConnector(ApiType.GraphQL);
}
```

This can be controlled by an environment variable or a feature flag:

```typescript
// lib/api/init.ts
import { apiFactory, ApiType } from '@/lib/api/connector/factory';

export function initializeAPI() {
  const apiType = process.env.NEXT_PUBLIC_API_TYPE as ApiType;
  
  if (apiType && Object.values(ApiType).includes(apiType)) {
    apiFactory.setActiveConnector(apiType);
  }
}
```

## Error Handling

We'll implement consistent error handling across the application:

```typescript
// lib/api/error.ts
import { ApiError } from '@/types';

export function handleApiError(error: ApiError): string {
  // Log error
  console.error('API Error:', error);
  
  // Return user-friendly message
  switch (error.code) {
    case 'UNAUTHORIZED':
      return 'You are not authorized to perform this action. Please log in.';
    case 'FORBIDDEN':
      return 'You do not have permission to perform this action.';
    case 'NOT_FOUND':
      return 'The requested resource was not found.';
    case 'VALIDATION_ERROR':
      return 'There was an error with your request. Please check your input.';
    case 'NETWORK_ERROR':
      return 'There was a network error. Please check your connection.';
    default:
      return 'An unexpected error occurred. Please try again later.';
  }
}
```

## Caching Strategy

We'll implement a caching strategy using React Query:

```typescript
// lib/api/cache.ts
import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 30, // 30 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Cache invalidation helpers
export function invalidateCasks() {
  queryClient.invalidateQueries({ queryKey: ['casks'] });
}

export function invalidateCask(id: string) {
  queryClient.invalidateQueries({ queryKey: ['cask', id] });
}

export function invalidateMarkets() {
  queryClient.invalidateQueries({ queryKey: ['markets'] });
}

export function invalidateNFTs() {
  queryClient.invalidateQueries({ queryKey: ['nfts'] });
}

export function invalidateUser() {
  queryClient.invalidateQueries({ queryKey: ['user'] });
}
```

## Testing Strategy

1. **Unit Testing**
   - Test API connector implementations
   - Test data mapping functions
   - Test error handling

2. **Integration Testing**
   - Test API hooks with mock data
   - Test server-side API functions
   - Test switching between API implementations

3. **End-to-End Testing**
   - Test complete data flow from API to UI
   - Test error scenarios
   - Test caching behavior

## Migration Steps

1. Define data models and API interfaces
2. Implement GraphQL connector
3. Create API factory and hooks
4. Integrate with server and client components
5. Test and optimize
6. Prepare for future PayloadJS integration

## Acceptance Criteria

- API connector abstraction is implemented
- GraphQL API is fully supported
- Error handling is consistent
- Caching strategy is implemented
- Switching between API implementations is possible
- Type safety is maintained throughout the data flow

This connector strategy provides a flexible and maintainable approach to API integration that will facilitate the future migration from GraphQL to PayloadJS.

# Marketplace Feature Migration

This document outlines the plan for migrating the DCasks marketplace feature from the current Next.js 12 Pages Router implementation to the new Next.js 15 App Router architecture.

## Current Implementation

The current marketplace is implemented in `pages/marketplace/index.tsx` and consists of several components:

```tsx
// pages/marketplace/index.tsx
const Marketplace: NextPage = () => {
  return (
    <>
      <div className="pageMarketplace relative">
        <div className="container relative">
          <div
            className={classNames(classes.bgMarket)}
            style={{ maxWidth: 1748 }}
          />
        </div>
        <div className="container pt-8 pb-16 sm:pt-24 sm:pb-28">
          {/* Regular Cask Marketplace */}
          <RegularCaskList />

          {/* Whole Cask Marketplace */}
          <WholeCaskList />
        </div>
        <SellYourCasksSection />
      </div>
      <Footer />
    </>
  );
};
```

The marketplace includes:
- Regular cask listings
- Whole cask listings
- Filtering and sorting options
- Pagination
- Call-to-action section for sellers

## Target Implementation

The new marketplace will be implemented using the App Router pattern with improved data fetching and filtering capabilities:

```tsx
// app/(frontend)/marketplace/page.tsx
import { Metadata } from 'next';
import { Suspense } from 'react';
import { MarketplaceHeader } from '@/components/marketplace/MarketplaceHeader';
import { CaskFilters } from '@/components/marketplace/CaskFilters';
import { RegularCaskList } from '@/components/marketplace/RegularCaskList';
import { WholeCaskList } from '@/components/marketplace/WholeCaskList';
import { SellYourCasksSection } from '@/components/home/<USER>';
import { MarketplaceLoading } from '@/components/marketplace/MarketplaceLoading';
import { fetchInitialFilters } from '@/lib/api/marketplace';

export const metadata: Metadata = {
  title: 'Marketplace - DCasks',
  description: 'Browse and purchase premium whisky cask NFTs',
};

interface MarketplacePageProps {
  searchParams: {
    minPrice?: string;
    maxPrice?: string;
    region?: string;
    distillery?: string;
    sort?: string;
    page?: string;
    type?: 'regular' | 'whole';
  };
}

export default async function MarketplacePage({ searchParams }: MarketplacePageProps) {
  // Fetch initial filter options
  const filterOptions = await fetchInitialFilters();
  
  // Default to showing both types if not specified
  const showType = searchParams.type || 'both';
  
  return (
    <div className="relative">
      <MarketplaceHeader />
      
      <div className="container py-8 md:py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <CaskFilters 
              initialFilters={searchParams} 
              filterOptions={filterOptions} 
            />
          </div>
          
          <div className="lg:col-span-3">
            {(showType === 'both' || showType === 'regular') && (
              <Suspense fallback={<MarketplaceLoading />}>
                <RegularCaskList searchParams={searchParams} />
              </Suspense>
            )}
            
            {(showType === 'both' || showType === 'whole') && (
              <Suspense fallback={<MarketplaceLoading />}>
                <WholeCaskList searchParams={searchParams} />
              </Suspense>
            )}
          </div>
        </div>
      </div>
      
      <SellYourCasksSection />
    </div>
  );
}
```

## Component Migration Plan

### 1. MarketplaceHeader

```tsx
// components/marketplace/MarketplaceHeader.tsx
export function MarketplaceHeader() {
  return (
    <div className="bg-muted/30 py-12 md:py-16">
      <div className="container">
        <h1 className="text-3xl md:text-4xl font-bold mb-4">Marketplace</h1>
        <p className="text-lg text-muted-foreground max-w-2xl">
          Browse our curated selection of premium whisky casks available as NFTs. 
          Filter by price, region, distillery, and more to find your perfect investment.
        </p>
      </div>
    </div>
  );
}
```

### 2. CaskFilters

```tsx
// components/marketplace/CaskFilters.tsx
'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface CaskFiltersProps {
  initialFilters: {
    minPrice?: string;
    maxPrice?: string;
    region?: string;
    distillery?: string;
    sort?: string;
    page?: string;
    type?: string;
  };
  filterOptions: {
    regions: string[];
    distilleries: string[];
    minPrice: number;
    maxPrice: number;
  };
}

export function CaskFilters({ initialFilters, filterOptions }: CaskFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  
  // Initialize state from URL params
  const [filters, setFilters] = useState({
    minPrice: initialFilters.minPrice ? parseInt(initialFilters.minPrice) : filterOptions.minPrice,
    maxPrice: initialFilters.maxPrice ? parseInt(initialFilters.maxPrice) : filterOptions.maxPrice,
    region: initialFilters.region?.split(',') || [],
    distillery: initialFilters.distillery?.split(',') || [],
    sort: initialFilters.sort || 'newest',
    type: initialFilters.type || 'both',
  });
  
  // Update URL when filters change
  const applyFilters = () => {
    const params = new URLSearchParams();
    
    if (filters.minPrice !== filterOptions.minPrice) {
      params.set('minPrice', filters.minPrice.toString());
    }
    
    if (filters.maxPrice !== filterOptions.maxPrice) {
      params.set('maxPrice', filters.maxPrice.toString());
    }
    
    if (filters.region.length > 0) {
      params.set('region', filters.region.join(','));
    }
    
    if (filters.distillery.length > 0) {
      params.set('distillery', filters.distillery.join(','));
    }
    
    if (filters.sort !== 'newest') {
      params.set('sort', filters.sort);
    }
    
    if (filters.type !== 'both') {
      params.set('type', filters.type);
    }
    
    // Reset to page 1 when filters change
    params.set('page', '1');
    
    router.push(`${pathname}?${params.toString()}`);
  };
  
  // Reset filters
  const resetFilters = () => {
    setFilters({
      minPrice: filterOptions.minPrice,
      maxPrice: filterOptions.maxPrice,
      region: [],
      distillery: [],
      sort: 'newest',
      type: 'both',
    });
  };
  
  // Filter UI implementation
  return (
    <div className="bg-card rounded-lg border p-4">
      <h2 className="text-xl font-semibold mb-4">Filters</h2>
      
      {/* Price Range */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-2">Price Range</h3>
        <Slider
          value={[filters.minPrice, filters.maxPrice]}
          min={filterOptions.minPrice}
          max={filterOptions.maxPrice}
          step={100}
          onValueChange={(value) => {
            setFilters({
              ...filters,
              minPrice: value[0],
              maxPrice: value[1],
            });
          }}
          className="mb-2"
        />
        <div className="flex items-center justify-between">
          <span className="text-sm">${filters.minPrice}</span>
          <span className="text-sm">${filters.maxPrice}</span>
        </div>
      </div>
      
      {/* Regions */}
      <Accordion type="single" collapsible defaultValue="regions">
        <AccordionItem value="regions">
          <AccordionTrigger className="text-sm font-medium">Regions</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {filterOptions.regions.map((region) => (
                <div key={region} className="flex items-center">
                  <Checkbox
                    id={`region-${region}`}
                    checked={filters.region.includes(region)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters({
                          ...filters,
                          region: [...filters.region, region],
                        });
                      } else {
                        setFilters({
                          ...filters,
                          region: filters.region.filter((r) => r !== region),
                        });
                      }
                    }}
                  />
                  <Label htmlFor={`region-${region}`} className="ml-2 text-sm">
                    {region}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      
      {/* Distilleries */}
      <Accordion type="single" collapsible className="mt-2">
        <AccordionItem value="distilleries">
          <AccordionTrigger className="text-sm font-medium">Distilleries</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {filterOptions.distilleries.map((distillery) => (
                <div key={distillery} className="flex items-center">
                  <Checkbox
                    id={`distillery-${distillery}`}
                    checked={filters.distillery.includes(distillery)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters({
                          ...filters,
                          distillery: [...filters.distillery, distillery],
                        });
                      } else {
                        setFilters({
                          ...filters,
                          distillery: filters.distillery.filter((d) => d !== distillery),
                        });
                      }
                    }}
                  />
                  <Label htmlFor={`distillery-${distillery}`} className="ml-2 text-sm">
                    {distillery}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      
      {/* Sort Options */}
      <div className="mt-6">
        <h3 className="text-sm font-medium mb-2">Sort By</h3>
        <RadioGroup
          value={filters.sort}
          onValueChange={(value) => {
            setFilters({
              ...filters,
              sort: value,
            });
          }}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="newest" id="sort-newest" />
            <Label htmlFor="sort-newest">Newest</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="price-low" id="sort-price-low" />
            <Label htmlFor="sort-price-low">Price: Low to High</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="price-high" id="sort-price-high" />
            <Label htmlFor="sort-price-high">Price: High to Low</Label>
          </div>
        </RadioGroup>
      </div>
      
      {/* Type Filter */}
      <div className="mt-6">
        <h3 className="text-sm font-medium mb-2">Cask Type</h3>
        <RadioGroup
          value={filters.type}
          onValueChange={(value) => {
            setFilters({
              ...filters,
              type: value,
            });
          }}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="both" id="type-both" />
            <Label htmlFor="type-both">All Casks</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="regular" id="type-regular" />
            <Label htmlFor="type-regular">Regular Casks</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="whole" id="type-whole" />
            <Label htmlFor="type-whole">Whole Casks</Label>
          </div>
        </RadioGroup>
      </div>
      
      {/* Action Buttons */}
      <div className="mt-6 space-y-2">
        <Button onClick={applyFilters} className="w-full">Apply Filters</Button>
        <Button onClick={resetFilters} variant="outline" className="w-full">Reset</Button>
      </div>
    </div>
  );
}
```

### 3. RegularCaskList

```tsx
// components/marketplace/RegularCaskList.tsx
import { CaskCard } from '@/components/marketplace/CaskCard';
import { Pagination } from '@/components/ui/pagination';
import { fetchRegularCasks, fetchRegularCasksCount } from '@/lib/api/marketplace';

interface RegularCaskListProps {
  searchParams: {
    minPrice?: string;
    maxPrice?: string;
    region?: string;
    distillery?: string;
    sort?: string;
    page?: string;
  };
}

export async function RegularCaskList({ searchParams }: RegularCaskListProps) {
  // Parse search params
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const limit = 9; // Items per page
  
  // Build filter object for API
  const filters = {
    minPrice: searchParams.minPrice ? parseInt(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseInt(searchParams.maxPrice) : undefined,
    region: searchParams.region?.split(',') || undefined,
    distillery: searchParams.distillery?.split(',') || undefined,
  };
  
  // Determine sort order
  const sort = (() => {
    switch (searchParams.sort) {
      case 'price-low':
        return { price: 1 };
      case 'price-high':
        return { price: -1 };
      case 'newest':
      default:
        return { createdAt: -1 };
    }
  })();
  
  // Fetch casks and total count
  const [casks, totalCount] = await Promise.all([
    fetchRegularCasks({
      limit,
      skip: (page - 1) * limit,
      filters,
      sort,
    }),
    fetchRegularCasksCount(filters),
  ]);
  
  const totalPages = Math.ceil(totalCount / limit);
  
  if (casks.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-2">No Regular Casks Found</h2>
        <p className="text-muted-foreground">
          Try adjusting your filters to see more results.
        </p>
      </div>
    );
  }
  
  return (
    <div className="mb-12">
      <h2 className="text-2xl font-semibold mb-6">Regular Casks</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {casks.map((cask) => (
          <CaskCard key={cask.id} cask={cask} />
        ))}
      </div>
      
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <Pagination totalPages={totalPages} currentPage={page} />
        </div>
      )}
    </div>
  );
}
```

### 4. Data Fetching Functions

```tsx
// lib/api/marketplace.ts
import { apolloClient } from '@/lib/apollo';
import { 
  GET_CASKS, 
  GET_WHOLE_CASKS,
  COUNT_CASKS,
  COUNT_WHOLE_CASKS,
  GET_MARKETPLACE_FILTERS
} from '@/lib/apollo/queries';

export async function fetchInitialFilters() {
  try {
    const { data } = await apolloClient.query({
      query: GET_MARKETPLACE_FILTERS,
    });
    
    return {
      regions: data.regions || [],
      distilleries: data.distilleries || [],
      minPrice: data.minPrice || 0,
      maxPrice: data.maxPrice || 100000,
    };
  } catch (error) {
    console.error('Error fetching marketplace filters:', error);
    return {
      regions: [],
      distilleries: [],
      minPrice: 0,
      maxPrice: 100000,
    };
  }
}

export async function fetchRegularCasks({ limit = 9, skip = 0, filters = {}, sort = {} }) {
  try {
    const { data } = await apolloClient.query({
      query: GET_CASKS,
      variables: {
        limit,
        skip,
        where: buildWhereClause(filters),
        sort,
      },
    });
    
    return data.casks || [];
  } catch (error) {
    console.error('Error fetching regular casks:', error);
    return [];
  }
}

export async function fetchRegularCasksCount(filters = {}) {
  try {
    const { data } = await apolloClient.query({
      query: COUNT_CASKS,
      variables: {
        where: buildWhereClause(filters),
      },
    });
    
    return data.countCasks?.count || 0;
  } catch (error) {
    console.error('Error fetching regular casks count:', error);
    return 0;
  }
}

export async function fetchWholeCasks({ limit = 9, skip = 0, filters = {}, sort = {} }) {
  try {
    const { data } = await apolloClient.query({
      query: GET_WHOLE_CASKS,
      variables: {
        limit,
        skip,
        where: buildWhereClause(filters),
        sort,
      },
    });
    
    return data.wholeCasks || [];
  } catch (error) {
    console.error('Error fetching whole casks:', error);
    return [];
  }
}

export async function fetchWholeCasksCount(filters = {}) {
  try {
    const { data } = await apolloClient.query({
      query: COUNT_WHOLE_CASKS,
      variables: {
        where: buildWhereClause(filters),
      },
    });
    
    return data.countWholeCasks?.count || 0;
  } catch (error) {
    console.error('Error fetching whole casks count:', error);
    return 0;
  }
}

// Helper function to build GraphQL where clause
function buildWhereClause(filters) {
  const where = {};
  
  if (filters.minPrice !== undefined) {
    where.minPrice = { $gte: filters.minPrice };
  }
  
  if (filters.maxPrice !== undefined) {
    where.maxPrice = { $lte: filters.maxPrice };
  }
  
  if (filters.region && filters.region.length > 0) {
    where.region = { $in: filters.region };
  }
  
  if (filters.distillery && filters.distillery.length > 0) {
    where.distillery = { $in: filters.distillery };
  }
  
  return where;
}
```

## URL-Based Filtering

The new implementation uses URL parameters for filtering, which provides several benefits:

1. **Shareable URLs**: Users can share specific filtered views
2. **Bookmarkable**: Users can bookmark specific filtered views
3. **SEO-friendly**: Search engines can index specific filtered views
4. **Browser history**: Users can use browser back/forward buttons to navigate between filtered views

Example URL: `/marketplace?minPrice=1000&maxPrice=5000&region=Speyside,Highland&sort=price-low&page=2`

## Server-Side Rendering and Streaming

The new implementation leverages Next.js 15's server components and streaming:

1. **Server Components**: The main page and lists are server components, which fetch data on the server
2. **Suspense**: Lists are wrapped in Suspense boundaries for streaming
3. **Loading UI**: Loading states are shown while data is being fetched

This approach provides several benefits:

1. **Faster Initial Load**: Server-rendered HTML is sent to the client immediately
2. **Progressive Rendering**: Content streams to the client as it becomes available
3. **Reduced Client-Side JavaScript**: Less JavaScript is sent to the client

## Client-Side Interactivity

While the main data fetching happens on the server, client-side interactivity is maintained:

1. **Filter UI**: The filter UI is a client component that updates the URL
2. **Pagination**: Pagination links update the URL to fetch the next page
3. **Sort Options**: Sort options update the URL to change the sort order

## SEO Optimization

The new implementation includes improved SEO:

```tsx
// app/(frontend)/marketplace/page.tsx
export const metadata: Metadata = {
  title: 'Marketplace - DCasks',
  description: 'Browse and purchase premium whisky cask NFTs',
  openGraph: {
    title: 'Marketplace - DCasks',
    description: 'Browse and purchase premium whisky cask NFTs',
    images: [
      {
        url: '/images/marketplace-og.jpg',
        width: 1200,
        height: 630,
        alt: 'DCasks Marketplace',
      },
    ],
  },
};
```

## Testing Strategy

1. **Functional Testing**
   - Verify all filters work correctly
   - Test pagination
   - Test sorting options
   - Verify cask cards display correctly

2. **Performance Testing**
   - Measure server response time
   - Test streaming with slow connections
   - Measure Time to First Byte (TTFB)
   - Measure Largest Contentful Paint (LCP)

3. **SEO Testing**
   - Verify metadata is correctly generated
   - Test structured data
   - Check indexability of filtered views

## Migration Steps

1. Create the basic page structure in `app/(frontend)/marketplace/page.tsx`
2. Implement the data fetching functions
3. Create the filter components
4. Implement the cask list components
5. Add pagination and sorting
6. Test and optimize performance

## Acceptance Criteria

- Marketplace visually matches the current implementation
- All filters work correctly
- Pagination works correctly
- Sorting options work correctly
- Cask cards display correctly
- Performance meets or exceeds current implementation
- SEO is properly configured

This migration plan provides a comprehensive approach to moving the DCasks marketplace to the new architecture while maintaining its functionality and improving its performance and SEO.

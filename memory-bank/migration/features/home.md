# Home Page Migration

This document outlines the plan for migrating the DCasks home page from the current Next.js 12 Pages Router implementation to the new Next.js 15 App Router architecture.

## Current Implementation

The current home page is implemented in `pages/index.tsx` and consists of several sections:

```tsx
// pages/index.tsx
const Home: NextPage = () => {
  const isMobile = false;

  return (
    <div className="z-0 overflow-hidden">
      <HeroSection />
      <SectionFeaturedCasks />
      <video
        autoPlay={!isMobile}
        muted={!isMobile}
        loop={!isMobile}
        playsInline={!isMobile}
        controls
        preload="auto"
        poster="/images/video-capture-6860.jpeg"
        className="w-full aspect-[16/9]">
        <source
          src="https://dcasks.s3.ap-southeast-1.amazonaws.com/dcasks-introduction-video.mp4"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>
      <WhyInvestWhiskySection />
      <LiquildStacking />
      <div className="relative -z-10">
        <div className="absolute top-1/2 -translate-y-1/2 left-0 -translate-x-1/4 opacity-5 xl:opacity-10 text-[#3C3C3B]">
          <BgSvgIcon2 />
        </div>
      </div>
      <OurTechSection />
      <AboutUsSection />
      {/* <PartnersSection /> */}
      <SellYourCasksSection />
      {/* <NewtoDCasksSection /> */}
      <LatestBlogPostsSection />
      <Footer />
    </div>
  );
};
```

## Target Implementation

The new home page will be implemented using the App Router pattern:

```tsx
// app/(frontend)/page.tsx
import { Metadata } from 'next';
import { HeroSection } from '@/components/home/<USER>';
import { FeaturedCasks } from '@/components/home/<USER>';
import { VideoSection } from '@/components/home/<USER>';
import { WhyInvestSection } from '@/components/home/<USER>';
import { LiquidStacking } from '@/components/home/<USER>';
import { BackgroundDecoration } from '@/components/home/<USER>';
import { OurTechSection } from '@/components/home/<USER>';
import { AboutUsSection } from '@/components/home/<USER>';
import { SellYourCasksSection } from '@/components/home/<USER>';
import { LatestBlogPosts } from '@/components/home/<USER>';

export const metadata: Metadata = {
  title: 'DCasks - Whisky Cask NFT Marketplace',
  description: 'Invest in premium whisky casks through blockchain technology',
};

export default async function HomePage() {
  // Fetch featured casks data
  const featuredCasks = await fetchFeaturedCasks();
  
  // Fetch latest blog posts
  const latestPosts = await fetchLatestPosts();
  
  return (
    <div className="overflow-hidden">
      <HeroSection />
      <FeaturedCasks casks={featuredCasks} />
      <VideoSection 
        videoUrl="https://dcasks.s3.ap-southeast-1.amazonaws.com/dcasks-introduction-video.mp4"
        posterUrl="/images/video-capture-6860.jpeg"
      />
      <WhyInvestSection />
      <LiquidStacking />
      <BackgroundDecoration />
      <OurTechSection />
      <AboutUsSection />
      <SellYourCasksSection />
      <LatestBlogPosts posts={latestPosts} />
    </div>
  );
}
```

## Component Migration Plan

### 1. HeroSection

**Current:**
```tsx
// components/layouts/components/HeroSection.tsx
const HeroSection = () => {
  // Implementation details
};
```

**Target:**
```tsx
// components/home/<USER>
'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';
import Image from 'next/image';

export function HeroSection() {
  return (
    <section className="relative h-screen flex items-center">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Invest in Premium Whisky Casks through Blockchain Technology
          </h1>
          <p className="text-lg md:text-xl mb-8">
            DCasks brings whisky investment to the digital age with NFT-backed cask ownership.
          </p>
          <div className="flex flex-wrap gap-4">
            <Button asChild size="lg">
              <Link href="/marketplace">Explore Marketplace</Link>
            </Button>
            <Button variant="outline" size="lg">
              <Link href="/about">Learn More</Link>
            </Button>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 -z-10">
        <Image
          src="/images/hero-background.jpg"
          alt="Whisky casks in a warehouse"
          fill
          className="object-cover opacity-20"
          priority
        />
      </div>
    </section>
  );
}
```

### 2. FeaturedCasks

**Current:**
```tsx
// components/layouts/components/SectionFeaturedCasks.tsx
const SectionFeaturedCasks = () => {
  // Implementation details
};
```

**Target:**
```tsx
// components/home/<USER>
import { CaskCard } from '@/components/marketplace/CaskCard';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface FeaturedCasksProps {
  casks: any[]; // Replace with proper type
}

export function FeaturedCasks({ casks }: FeaturedCasksProps) {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">Featured Casks</h2>
          <Button asChild variant="outline">
            <Link href="/marketplace">View All</Link>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {casks.map((cask) => (
            <CaskCard key={cask.id} cask={cask} />
          ))}
        </div>
      </div>
    </section>
  );
}
```

### 3. VideoSection

**Current:** Inline in the home page component

**Target:**
```tsx
// components/home/<USER>
'use client';

import { useState, useEffect } from 'react';

interface VideoSectionProps {
  videoUrl: string;
  posterUrl: string;
}

export function VideoSection({ videoUrl, posterUrl }: VideoSectionProps) {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <section className="w-full">
      <video
        autoPlay={!isMobile}
        muted={!isMobile}
        loop={!isMobile}
        playsInline={!isMobile}
        controls
        preload="auto"
        poster={posterUrl}
        className="w-full aspect-[16/9]"
      >
        <source src={videoUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </section>
  );
}
```

### 4. Data Fetching Functions

```tsx
// lib/api/casks.ts
import { apolloClient } from '@/lib/apollo';
import { GET_FEATURED_CASKS } from '@/lib/apollo/queries';

export async function fetchFeaturedCasks() {
  try {
    const { data } = await apolloClient.query({
      query: GET_FEATURED_CASKS,
      variables: { limit: 3 },
    });
    
    return data.casks || [];
  } catch (error) {
    console.error('Error fetching featured casks:', error);
    return [];
  }
}

// lib/api/posts.ts
import { apolloClient } from '@/lib/apollo';
import { GET_LATEST_POSTS } from '@/lib/apollo/queries';

export async function fetchLatestPosts() {
  try {
    const { data } = await apolloClient.query({
      query: GET_LATEST_POSTS,
      variables: { limit: 3 },
    });
    
    return data.posts || [];
  } catch (error) {
    console.error('Error fetching latest posts:', error);
    return [];
  }
}
```

## SEO Optimization

The new implementation will leverage Next.js 15's Metadata API for improved SEO:

```tsx
// app/(frontend)/page.tsx
export const metadata: Metadata = {
  title: 'DCasks - Whisky Cask NFT Marketplace',
  description: 'Invest in premium whisky casks through blockchain technology',
  openGraph: {
    title: 'DCasks - Whisky Cask NFT Marketplace',
    description: 'Invest in premium whisky casks through blockchain technology',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'DCasks Marketplace',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DCasks - Whisky Cask NFT Marketplace',
    description: 'Invest in premium whisky casks through blockchain technology',
    images: ['/images/og-image.jpg'],
  },
};
```

## Performance Optimizations

1. **Image Optimization**
   - Use Next.js Image component for optimized image loading
   - Implement proper sizing and responsive images

2. **Component Splitting**
   - Split large sections into smaller components
   - Use dynamic imports for non-critical components

3. **Font Optimization**
   - Use Next.js Font optimization
   - Implement font subsetting

4. **Lazy Loading**
   - Implement lazy loading for below-the-fold content
   - Use Intersection Observer for visibility detection

## Testing Strategy

1. **Visual Comparison**
   - Compare the visual appearance with the current implementation
   - Ensure responsive behavior matches

2. **Functional Testing**
   - Verify all links and buttons work correctly
   - Test video playback functionality

3. **Performance Testing**
   - Measure and compare Core Web Vitals
   - Test loading performance on various devices

## Migration Steps

1. Create the basic page structure in `app/(frontend)/page.tsx`
2. Implement the data fetching functions
3. Migrate each section component one by one
4. Implement SEO metadata
5. Test and optimize performance
6. Conduct visual and functional testing

## Acceptance Criteria

- Home page visually matches the current implementation
- All sections are properly implemented
- Data is correctly fetched and displayed
- Video plays correctly on both mobile and desktop
- SEO metadata is properly configured
- Performance metrics meet or exceed current implementation

This migration plan provides a comprehensive approach to moving the DCasks home page to the new architecture while maintaining its functionality and improving its performance and SEO.

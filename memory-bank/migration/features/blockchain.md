# Blockchain Integration Migration

This document outlines the plan for migrating the blockchain integration from the current DCasks frontend to the new architecture with Next.js 15 App Router.

## Current Implementation

The current DCasks frontend uses Reown AppKit with Ethers.js for blockchain integration. Key components include:

1. **Wallet Connection**: Using Reown AppKit for wallet connection and authentication
2. **Smart Contract Interaction**: Using Ethers.js for interacting with smart contracts
3. **Transaction Handling**: Managing transaction status and notifications
4. **Event Listening**: Listening for blockchain events

## Target Implementation

The target implementation will continue to use Reown AppKit but with updated patterns compatible with Next.js 15 App Router:

1. **Client Components**: All blockchain interactions will be in client components
2. **React Context**: Using React Context for sharing blockchain state
3. **Custom Hooks**: Creating reusable hooks for common blockchain interactions
4. **Server Actions**: Using server actions for backend interactions related to blockchain events

## Blockchain Provider Setup

```tsx
// providers/BlockchainProvider.tsx
'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAppKit, useAppKitAccount } from '@reown/appkit/react';
import { ethers } from 'ethers';
import { toast } from 'react-toastify';

// Contract ABIs
import { 
  dcaskBottleAbi, 
  dcaskMarketplaceAbi, 
  dcaskMembershipAbi,
  dcaskNftAbi,
  dcaskProfitModel,
  dcaskTulipAbi,
  usdtAbi,
  wokensAbi
} from '@/blockchain/abis';

// Types
import type { TransactionStatus } from '@/types/blockchain';

interface BlockchainContextType {
  isConnected: boolean;
  address: string | undefined;
  connect: () => void;
  disconnect: () => void;
  contracts: {
    bottle: ethers.Contract | null;
    marketplace: ethers.Contract | null;
    membership: ethers.Contract | null;
    nft: ethers.Contract | null;
    profitModel: ethers.Contract | null;
    tulip: ethers.Contract | null;
    usdt: ethers.Contract | null;
    wokens: ethers.Contract | null;
  };
  transactionStatus: TransactionStatus;
}

const BlockchainContext = createContext<BlockchainContextType | undefined>(undefined);

export function BlockchainProvider({ children }: { children: ReactNode }) {
  const { open } = useAppKit();
  const { address, isConnected, connector } = useAppKitAccount();
  
  const [contracts, setContracts] = useState({
    bottle: null,
    marketplace: null,
    membership: null,
    nft: null,
    profitModel: null,
    tulip: null,
    usdt: null,
    wokens: null,
  });
  
  const [transactionStatus, setTransactionStatus] = useState<TransactionStatus>('idle');
  
  // Initialize contracts when connected
  useEffect(() => {
    if (isConnected && connector) {
      initializeContracts();
    } else {
      resetContracts();
    }
  }, [isConnected, connector]);
  
  // Initialize contracts
  const initializeContracts = async () => {
    try {
      if (!connector) return;
      
      const provider = new ethers.BrowserProvider(await connector.getProvider());
      const signer = await provider.getSigner();
      
      // Initialize contracts with signer
      setContracts({
        bottle: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_BOTTLE!,
          dcaskBottleAbi,
          signer
        ),
        marketplace: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_MARKETPLACE!,
          dcaskMarketplaceAbi,
          signer
        ),
        membership: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_MEMBERSHIP!,
          dcaskMembershipAbi,
          signer
        ),
        nft: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_WHOLE_CASK!,
          dcaskNftAbi,
          signer
        ),
        profitModel: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_PROFIT_MODEL!,
          dcaskProfitModel,
          signer
        ),
        tulip: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_TULIP!,
          dcaskTulipAbi,
          signer
        ),
        usdt: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_USDT!,
          usdtAbi,
          signer
        ),
        wokens: new ethers.Contract(
          process.env.NEXT_PUBLIC_CONTRACT_WOKENS!,
          wokensAbi,
          signer
        ),
      });
    } catch (error) {
      console.error('Error initializing contracts:', error);
      toast.error('Failed to initialize blockchain contracts');
    }
  };
  
  // Reset contracts
  const resetContracts = () => {
    setContracts({
      bottle: null,
      marketplace: null,
      membership: null,
      nft: null,
      profitModel: null,
      tulip: null,
      usdt: null,
      wokens: null,
    });
  };
  
  // Connect wallet
  const connect = () => {
    open({ view: 'Connect' });
  };
  
  // Disconnect wallet
  const disconnect = () => {
    open({ view: 'Account' });
  };
  
  return (
    <BlockchainContext.Provider
      value={{
        isConnected,
        address,
        connect,
        disconnect,
        contracts,
        transactionStatus,
      }}
    >
      {children}
    </BlockchainContext.Provider>
  );
}

export function useBlockchain() {
  const context = useContext(BlockchainContext);
  
  if (context === undefined) {
    throw new Error('useBlockchain must be used within a BlockchainProvider');
  }
  
  return context;
}
```

## Contract Interaction Hooks

### 1. Marketplace Hooks

```tsx
// hooks/blockchain/useMarketplace.ts
'use client';

import { useState } from 'react';
import { ethers } from 'ethers';
import { toast } from 'react-toastify';
import { useBlockchain } from '@/providers/BlockchainProvider';
import { waitForTransaction } from '@/lib/blockchain/utils';

export function useMarketplace() {
  const { contracts } = useBlockchain();
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  
  // Create sell order
  const createSell = async (tokenId: string, price: string) => {
    if (!contracts.marketplace) {
      toast.error('Marketplace contract not initialized');
      return null;
    }
    
    try {
      setStatus('loading');
      
      // Check USDT approval
      const isApproved = await contracts.usdt.allowance(
        await contracts.marketplace.getAddress(),
        ethers.parseUnits(price, 6)
      );
      
      if (!isApproved) {
        // Approve USDT
        const approveTx = await contracts.usdt.approve(
          await contracts.marketplace.getAddress(),
          ethers.parseUnits(price, 6)
        );
        
        await waitForTransaction(approveTx.hash);
      }
      
      // Create sell order
      const tx = await contracts.marketplace.createSell(
        tokenId,
        ethers.parseUnits(price, 6)
      );
      
      const receipt = await waitForTransaction(tx.hash);
      
      setStatus('success');
      toast.success('NFT listed for sale successfully');
      
      return receipt;
    } catch (error) {
      console.error('Error creating sell order:', error);
      setStatus('error');
      toast.error('Failed to list NFT for sale');
      return null;
    }
  };
  
  // Match sell order
  const matchSell = async (orderId: string, price: string) => {
    if (!contracts.marketplace) {
      toast.error('Marketplace contract not initialized');
      return null;
    }
    
    try {
      setStatus('loading');
      
      // Check USDT approval
      const isApproved = await contracts.usdt.allowance(
        await contracts.marketplace.getAddress(),
        ethers.parseUnits(price, 6)
      );
      
      if (!isApproved) {
        // Approve USDT
        const approveTx = await contracts.usdt.approve(
          await contracts.marketplace.getAddress(),
          ethers.parseUnits(price, 6)
        );
        
        await waitForTransaction(approveTx.hash);
      }
      
      // Match sell order
      const tx = await contracts.marketplace.matchSell(orderId);
      
      const receipt = await waitForTransaction(tx.hash);
      
      setStatus('success');
      toast.success('NFT purchased successfully');
      
      return receipt;
    } catch (error) {
      console.error('Error matching sell order:', error);
      setStatus('error');
      toast.error('Failed to purchase NFT');
      return null;
    }
  };
  
  // Cancel sell order
  const cancelSell = async (orderId: string) => {
    if (!contracts.marketplace) {
      toast.error('Marketplace contract not initialized');
      return null;
    }
    
    try {
      setStatus('loading');
      
      const tx = await contracts.marketplace.cancelSell(orderId);
      
      const receipt = await waitForTransaction(tx.hash);
      
      setStatus('success');
      toast.success('Listing cancelled successfully');
      
      return receipt;
    } catch (error) {
      console.error('Error cancelling sell order:', error);
      setStatus('error');
      toast.error('Failed to cancel listing');
      return null;
    }
  };
  
  return {
    createSell,
    matchSell,
    cancelSell,
    status,
  };
}
```

### 2. NFT Hooks

```tsx
// hooks/blockchain/useNFT.ts
'use client';

import { useState } from 'react';
import { ethers } from 'ethers';
import { toast } from 'react-toastify';
import { useBlockchain } from '@/providers/BlockchainProvider';
import { waitForTransaction } from '@/lib/blockchain/utils';

export function useNFT() {
  const { contracts } = useBlockchain();
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  
  // Get NFT balance
  const getBalance = async (address: string) => {
    if (!contracts.nft) {
      toast.error('NFT contract not initialized');
      return 0;
    }
    
    try {
      const balance = await contracts.nft.balanceOf(address);
      return Number(balance);
    } catch (error) {
      console.error('Error getting NFT balance:', error);
      return 0;
    }
  };
  
  // Get NFT owner
  const getOwner = async (tokenId: string) => {
    if (!contracts.nft) {
      toast.error('NFT contract not initialized');
      return null;
    }
    
    try {
      const owner = await contracts.nft.ownerOf(tokenId);
      return owner;
    } catch (error) {
      console.error('Error getting NFT owner:', error);
      return null;
    }
  };
  
  // Approve NFT for marketplace
  const approve = async (tokenId: string) => {
    if (!contracts.nft) {
      toast.error('NFT contract not initialized');
      return null;
    }
    
    try {
      setStatus('loading');
      
      const tx = await contracts.nft.approve(
        await contracts.marketplace.getAddress(),
        tokenId
      );
      
      const receipt = await waitForTransaction(tx.hash);
      
      setStatus('success');
      toast.success('NFT approved for marketplace');
      
      return receipt;
    } catch (error) {
      console.error('Error approving NFT:', error);
      setStatus('error');
      toast.error('Failed to approve NFT');
      return null;
    }
  };
  
  // Transfer NFT
  const transfer = async (to: string, tokenId: string) => {
    if (!contracts.nft) {
      toast.error('NFT contract not initialized');
      return null;
    }
    
    try {
      setStatus('loading');
      
      const tx = await contracts.nft.transferFrom(
        await contracts.nft.ownerOf(tokenId),
        to,
        tokenId
      );
      
      const receipt = await waitForTransaction(tx.hash);
      
      setStatus('success');
      toast.success('NFT transferred successfully');
      
      return receipt;
    } catch (error) {
      console.error('Error transferring NFT:', error);
      setStatus('error');
      toast.error('Failed to transfer NFT');
      return null;
    }
  };
  
  return {
    getBalance,
    getOwner,
    approve,
    transfer,
    status,
  };
}
```

## Wallet Connection Component

```tsx
// components/blockchain/WalletConnect.tsx
'use client';

import { Button } from '@/components/ui/button';
import { useBlockchain } from '@/providers/BlockchainProvider';
import { shortenAddress } from '@/lib/blockchain/utils';

export function WalletConnect() {
  const { isConnected, address, connect, disconnect } = useBlockchain();
  
  if (isConnected && address) {
    return (
      <Button
        variant="outline"
        onClick={disconnect}
      >
        {shortenAddress(address)}
      </Button>
    );
  }
  
  return (
    <Button onClick={connect}>
      Connect Wallet
    </Button>
  );
}
```

## Transaction Status Component

```tsx
// components/blockchain/TransactionStatus.tsx
'use client';

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useBlockchain } from '@/providers/BlockchainProvider';

export function TransactionStatus() {
  const { transactionStatus } = useBlockchain();
  
  if (transactionStatus === 'idle') {
    return null;
  }
  
  if (transactionStatus === 'loading') {
    return (
      <Alert>
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
        <AlertTitle>Transaction in Progress</AlertTitle>
        <AlertDescription>
          Please wait while your transaction is being processed.
        </AlertDescription>
      </Alert>
    );
  }
  
  if (transactionStatus === 'success') {
    return (
      <Alert className="bg-green-50 border-green-200">
        <AlertTitle>Transaction Successful</AlertTitle>
        <AlertDescription>
          Your transaction has been successfully processed.
        </AlertDescription>
      </Alert>
    );
  }
  
  if (transactionStatus === 'error') {
    return (
      <Alert variant="destructive">
        <AlertTitle>Transaction Failed</AlertTitle>
        <AlertDescription>
          There was an error processing your transaction. Please try again.
        </AlertDescription>
      </Alert>
    );
  }
  
  return null;
}
```

## Blockchain Utilities

```tsx
// lib/blockchain/utils.ts
import { ethers } from 'ethers';

// Shorten Ethereum address
export function shortenAddress(address: string, chars = 4): string {
  if (!address) return '';
  return `${address.substring(0, chars + 2)}...${address.substring(42 - chars)}`;
}

// Wait for transaction to be mined
export async function waitForTransaction(txHash: string): Promise<ethers.TransactionReceipt> {
  const provider = new ethers.JsonRpcProvider(process.env.NEXT_PUBLIC_NETWORK_RPC);
  return provider.waitForTransaction(txHash);
}

// Format price from wei to USDT
export function formatPrice(weiPrice: string): string {
  if (!weiPrice) return '0';
  return ethers.formatUnits(weiPrice, 6);
}

// Parse price from USDT to wei
export function parsePrice(price: string): string {
  if (!price) return '0';
  return ethers.parseUnits(price, 6).toString();
}

// Get transaction explorer URL
export function getExplorerUrl(txHash: string): string {
  return `${process.env.NEXT_PUBLIC_NETWORK_BLOCK_EXPLORE_URL}/tx/${txHash}`;
}
```

## Integration with App Router

### 1. Provider Setup

```tsx
// app/(frontend)/layout.tsx
import { BlockchainProvider } from '@/providers/BlockchainProvider';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <BlockchainProvider>
          {/* Other providers */}
          {children}
        </BlockchainProvider>
      </body>
    </html>
  );
}
```

### 2. Protected Routes

```tsx
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // For now, we're not implementing server-side authentication checks
  // since we're using client-side wallet authentication
  // This will be expanded in the future when we integrate with PayloadJS
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Add protected routes here if needed
  ],
};
```

### 3. Example Usage in a Page

```tsx
// app/(frontend)/nft/[id]/page.tsx
import { Suspense } from 'react';
import { NFTDetails } from '@/components/nft/NFTDetails';
import { NFTActions } from '@/components/nft/NFTActions';
import { NFTLoading } from '@/components/nft/NFTLoading';
import { fetchNFTDetails } from '@/lib/api/nft';

interface NFTPageProps {
  params: {
    id: string;
  };
}

export default async function NFTPage({ params }: NFTPageProps) {
  const nftDetails = await fetchNFTDetails(params.id);
  
  if (!nftDetails) {
    return <div>NFT not found</div>;
  }
  
  return (
    <div className="container py-8">
      <NFTDetails nft={nftDetails} />
      
      {/* Client component for blockchain interactions */}
      <Suspense fallback={<NFTLoading />}>
        <NFTActions nftId={params.id} nftDetails={nftDetails} />
      </Suspense>
    </div>
  );
}
```

```tsx
// components/nft/NFTActions.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useBlockchain } from '@/providers/BlockchainProvider';
import { useMarketplace } from '@/hooks/blockchain/useMarketplace';
import { useNFT } from '@/hooks/blockchain/useNFT';
import { TransactionStatus } from '@/components/blockchain/TransactionStatus';

interface NFTActionsProps {
  nftId: string;
  nftDetails: any; // Replace with proper type
}

export function NFTActions({ nftId, nftDetails }: NFTActionsProps) {
  const { isConnected, address } = useBlockchain();
  const { approve, getOwner, status: nftStatus } = useNFT();
  const { createSell, matchSell, cancelSell, status: marketStatus } = useMarketplace();
  
  const [price, setPrice] = useState('');
  
  // Check if current user is the owner
  const isOwner = async () => {
    if (!isConnected || !address) return false;
    
    const owner = await getOwner(nftId);
    return owner?.toLowerCase() === address.toLowerCase();
  };
  
  // Handle sell action
  const handleSell = async () => {
    if (!isConnected) {
      // Show connect wallet modal
      return;
    }
    
    // First approve the NFT for marketplace
    await approve(nftId);
    
    // Then create sell order
    await createSell(nftId, price);
  };
  
  // Handle buy action
  const handleBuy = async () => {
    if (!isConnected) {
      // Show connect wallet modal
      return;
    }
    
    await matchSell(nftDetails.orderId, nftDetails.price);
  };
  
  // Handle cancel action
  const handleCancel = async () => {
    if (!isConnected) {
      // Show connect wallet modal
      return;
    }
    
    await cancelSell(nftDetails.orderId);
  };
  
  // Render different actions based on NFT status
  // This is a simplified example
  return (
    <div className="mt-8">
      <TransactionStatus />
      
      {/* Different action buttons based on NFT status */}
      {/* This is just an example, actual implementation would be more complex */}
      {nftDetails.status === 'forSale' && !isOwner() && (
        <Button onClick={handleBuy} disabled={marketStatus === 'loading'}>
          Buy Now
        </Button>
      )}
      
      {nftDetails.status === 'owned' && isOwner() && (
        <div className="space-y-4">
          <input
            type="text"
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            placeholder="Enter price in USDT"
            className="w-full p-2 border rounded"
          />
          <Button onClick={handleSell} disabled={nftStatus === 'loading'}>
            Sell NFT
          </Button>
        </div>
      )}
      
      {nftDetails.status === 'forSale' && isOwner() && (
        <Button onClick={handleCancel} disabled={marketStatus === 'loading'}>
          Cancel Listing
        </Button>
      )}
    </div>
  );
}
```

## Event Listening

For blockchain events, we'll implement a custom hook that listens for events and updates the UI:

```tsx
// hooks/blockchain/useContractEvents.ts
'use client';

import { useEffect, useState } from 'react';
import { ethers } from 'ethers';
import { useBlockchain } from '@/providers/BlockchainProvider';

interface EventListener {
  contract: ethers.Contract;
  eventName: string;
  callback: (event: any) => void;
}

export function useContractEvents(listeners: EventListener[]) {
  const { isConnected } = useBlockchain();
  const [isListening, setIsListening] = useState(false);
  
  useEffect(() => {
    if (!isConnected) return;
    
    // Set up event listeners
    listeners.forEach(({ contract, eventName, callback }) => {
      if (contract) {
        contract.on(eventName, callback);
      }
    });
    
    setIsListening(true);
    
    // Clean up event listeners
    return () => {
      listeners.forEach(({ contract, eventName, callback }) => {
        if (contract) {
          contract.off(eventName, callback);
        }
      });
      
      setIsListening(false);
    };
  }, [isConnected, listeners]);
  
  return { isListening };
}
```

Example usage:

```tsx
// components/marketplace/MarketplaceEvents.tsx
'use client';

import { useEffect } from 'react';
import { useBlockchain } from '@/providers/BlockchainProvider';
import { useContractEvents } from '@/hooks/blockchain/useContractEvents';
import { toast } from 'react-toastify';

export function MarketplaceEvents() {
  const { contracts } = useBlockchain();
  
  // Set up event listeners
  const { isListening } = useContractEvents([
    {
      contract: contracts.marketplace,
      eventName: 'SellCreated',
      callback: (seller, tokenId, price) => {
        toast.info(`New listing: Token #${tokenId} for ${ethers.formatUnits(price, 6)} USDT`);
        // Update UI or fetch new data
      },
    },
    {
      contract: contracts.marketplace,
      eventName: 'SellMatched',
      callback: (buyer, seller, tokenId, price) => {
        toast.success(`Token #${tokenId} sold for ${ethers.formatUnits(price, 6)} USDT`);
        // Update UI or fetch new data
      },
    },
    {
      contract: contracts.marketplace,
      eventName: 'SellCancelled',
      callback: (seller, tokenId) => {
        toast.info(`Listing cancelled for Token #${tokenId}`);
        // Update UI or fetch new data
      },
    },
  ]);
  
  return null; // This component doesn't render anything
}
```

## Testing Strategy

1. **Unit Testing**
   - Test blockchain utility functions
   - Test custom hooks with mock contracts
   - Test UI components with mock blockchain state

2. **Integration Testing**
   - Test wallet connection flow
   - Test contract interactions with test networks
   - Test event listening and UI updates

3. **End-to-End Testing**
   - Test complete user flows (connect wallet, buy NFT, sell NFT, etc.)
   - Test error handling and recovery

## Migration Steps

1. Set up the BlockchainProvider
2. Migrate utility functions
3. Create custom hooks for contract interactions
4. Implement UI components for wallet connection and transaction status
5. Integrate blockchain functionality into pages
6. Test and optimize

## Acceptance Criteria

- Wallet connection works correctly
- Contract interactions work correctly
- Transaction status is displayed correctly
- Event listening works correctly
- Error handling is robust
- Performance is acceptable

This migration plan provides a comprehensive approach to moving the DCasks blockchain integration to the new architecture while maintaining its functionality and improving its organization and maintainability.

# Current DCasks Frontend Architecture

## Overview

The current DCasks frontend is built with Next.js 12 using the Pages Router pattern. It's a complex application focused on NFT marketplace functionality for whisky casks, with blockchain integration for authentication and transactions.

## Tech Stack

- **Framework**: Next.js 12 (Pages Router)
- **Language**: TypeScript
- **State Management**: <PERSON><PERSON>
- **Data Fetching**: Apollo Client (GraphQL)
- **Styling**: Tailwind CSS
- **Form Handling**: React Hook Form with Yup validation
- **Blockchain Integration**: Reown AppKit with Ethers.js
- **Authentication**: Wallet-based (via Reown AppKit)

## Project Structure

```
apps/dcasks-frontend/
├── apollo/                # GraphQL client setup and queries
├── components/            # Reusable UI components
│   ├── base/              # Basic UI components (Button, Card, etc.)
│   ├── hook-form/         # Form components
│   ├── layouts/           # Layout components
│   └── market-modal/      # Marketplace-specific modals
├── hooks/                 # Custom React hooks
├── pages/                 # Next.js pages
│   ├── _app.tsx           # App entry point
│   ├── index.tsx          # Home page
│   ├── marketplace/       # Marketplace pages
│   ├── nft-detail/        # NFT detail pages
│   ├── profile/           # User profile pages
│   └── api/               # API routes
├── public/                # Static assets
├── sections/              # Page sections
├── smc/                   # Smart contract integration
│   ├── abis/              # Contract ABIs
│   └── types/             # TypeScript types for contracts
├── styles/                # Global styles
├── types/                 # TypeScript type definitions
└── utils/                 # Utility functions
```

## Key Features

### 1. Home Page
- Hero section
- Featured casks
- Marketing sections (Why Invest, About Us, etc.)
- Video showcase

### 2. Marketplace
- NFT listing and browsing
- Filtering and search
- Regular cask and whole cask sections

### 3. NFT Detail
- Cask information display
- Trading functionality (buy, sell, auction)
- Price history and charts
- Asset activities

### 4. User Profile
- Owned NFTs
- Transaction history
- Portfolio management

### 5. Authentication
- Wallet connection via Reown AppKit
- Signature verification

### 6. Blockchain Integration
- Smart contract interactions
- Transaction handling
- Event listening

## Data Flow

1. **Authentication Flow**:
   - User connects wallet via Reown AppKit
   - Wallet address is stored in Jotai state
   - Authentication state is used throughout the app

2. **Data Fetching Flow**:
   - Apollo Client queries GraphQL API
   - Data is stored in component state or Jotai atoms
   - UI renders based on fetched data

3. **Transaction Flow**:
   - User initiates action (buy, sell, etc.)
   - Smart contract interaction via Ethers.js
   - Transaction status tracked in UI
   - Success/failure notifications

## UI Component Architecture

The UI follows a component-based architecture:

- **Layouts**: Base layout with Navbar and Footer
- **Sections**: Page-specific sections
- **Components**: Reusable UI components
- **Modals**: For interactive actions

## Challenges and Pain Points

1. **Maintainability**: The Pages Router pattern becomes complex with nested routes
2. **Performance**: Large bundle sizes and client-side rendering impact performance
3. **State Management**: Jotai state spread across many files
4. **API Integration**: Tight coupling with GraphQL schema
5. **Component Reuse**: Inconsistent component patterns

## Dependencies

Key dependencies include:

- `@apollo/client`: GraphQL client
- `@reown/appkit`: Blockchain wallet integration
- `ethers`: Ethereum library
- `jotai`: State management
- `react-hook-form`: Form handling
- `tailwindcss`: Styling
- `yup`: Form validation

This architecture will be migrated to the new Next.js 15 App Router pattern with PayloadJS integration.

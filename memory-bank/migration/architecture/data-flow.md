# Data Flow Architecture

This document outlines the data flow architecture for the DCasks frontend migration, focusing on how data will move through the system in both the initial implementation and future phases.

## Current Data Flow

```mermaid
flowchart TD
    A[React Components] <--> B[Jotai State]
    B <--> C[Apollo Client]
    C <--> D[GraphQL API]
```

## Initial Migration Data Flow

In the initial migration phase, we'll continue using the existing GraphQL API but with a refactored data fetching approach compatible with the App Router:

```mermaid
flowchart TD
    A[Server Components] --> B[Data Fetching Layer]
    B --> C[urql Client]
    A --> D[Client Components]
    D --> E[React Query/Jotai]
    C --> F[GraphQL API]
    G[GraphQL Codegen] --> C
```

## Future Data Flow with PayloadJS

In the future phase with PayloadJS integration:

```mermaid
flowchart TD
    A[Server Components] --> B[PayloadJS API]
    B --> C[MongoDB Database]
    A --> D[Client Components]
    D --> E[React Query]
```

## Data Fetching Strategies

### 1. Server Components

Server components will fetch data directly from the API:

```typescript
// app/(frontend)/marketplace/page.tsx
export default async function MarketplacePage() {
  // In initial phase - using abstracted urql client
  const casks = await fetchCasks({ limit: 10 });

  // Future phase - using PayloadJS
  // const casks = await payload.find({ collection: 'casks', limit: 10 });

  return (
    <div>
      <h1>Marketplace</h1>
      <CaskList casks={casks} />
    </div>
  );
}
```

### 2. Client Components

Client components will use React Query for data fetching and caching:

```typescript
// components/CaskFilters.tsx
'use client';

import { useQuery } from '@tanstack/react-query';
import { fetchCaskFilters } from '@/lib/api';

export function CaskFilters() {
  const { data, isLoading } = useQuery({
    queryKey: ['caskFilters'],
    queryFn: fetchCaskFilters,
  });

  if (isLoading) return <div>Loading filters...</div>;

  return (
    <div>
      {/* Filter UI */}
    </div>
  );
}
```

## API Abstraction Layer

To facilitate the future transition from GraphQL to PayloadJS, we'll implement an API abstraction layer:

```typescript
// lib/api/casks.ts
import { client } from '@/lib/urql';
import { CasksDocument } from '@/lib/graphql/generated';

export async function fetchCasks({ limit = 10, skip = 0, filters = {} }) {
  // In initial phase - using urql client
  const { data } = await client.query(CasksDocument, {
    limit,
    skip,
    where: filters
  }).toPromise();

  return data.casks;

  // Future phase - using PayloadJS
  // const payload = await getPayload();
  // return payload.find({
  //   collection: 'casks',
  //   limit,
  //   page: Math.floor(skip / limit) + 1,
  //   where: convertFilters(filters),
  // });
}
```

## Blockchain Data Flow

The blockchain data flow will remain largely unchanged:

```mermaid
flowchart TD
    A[React Components] <--> B[Reown AppKit]
    B <--> C[Ethereum Blockchain]
    D[Custom Hooks] --> A
    D --> E[Contract ABIs]
    E --> F[Ethers.js]
    F --> B
```

## Authentication Flow

The authentication flow will continue to use wallet-based authentication:

```mermaid
flowchart TD
    A[User Interface] --> B[Reown AppKit]
    B --> C[Ethereum Wallet]
    B --> D[Auth Context Provider]
    D --> E[Jotai Store]
    F[Server Components] --> G[Auth Middleware]
    G --> H[Protected Routes]
    D --> G
```

## Data Caching Strategy

### Initial Phase

- Use React Query for client-side data caching
- Implement stale-while-revalidate pattern
- Set appropriate cache times based on data volatility

### Future Phase

- Leverage Next.js cache mechanisms
- Implement revalidation strategies
- Use PayloadJS caching capabilities

## Real-time Data Considerations

For real-time data needs (e.g., auction updates):

- Initially: Implement polling with React Query
- Future: Consider WebSockets or Server-Sent Events

## Error Handling

Consistent error handling across the application:

- Server components: try/catch with error.tsx files
- Client components: React Query error handling
- Global error boundary for unexpected errors

## Data Transformation

To maintain consistency between API responses and component props:

- Implement adapter functions to transform API data
- Create type-safe interfaces for all data structures
- Use Zod for runtime validation of API responses

## Component Data Flow

```mermaid
flowchart TD
    A[Page Component] --> B[Layout Component]
    A --> C[Feature Component]
    C --> D[UI Component]
    C --> E[Data Fetching]
    E --> F[API Layer]
    F --> G[GraphQL/PayloadJS]
```

## Component Structure

Following the principle of smaller, more focused components:

```mermaid
flowchart TD
    A[Large Page Component] --> B[Feature Section 1]
    A --> C[Feature Section 2]
    A --> D[Feature Section 3]

    B --> E[UI Component 1]
    B --> F[UI Component 2]

    E --> G[Atomic Component 1]
    E --> H[Atomic Component 2]

    subgraph "File Structure"
        I[pages/marketplace.tsx] --> J[sections/marketplace/]
        J --> K[components/marketplace/]
        K --> L[ui/]
    end
```

## File Organization

```mermaid
flowchart TD
    A[src/] --> B[app/]
    A --> C[components/]
    A --> D[lib/]

    B --> E[(frontend)/]
    E --> F[page.tsx]
    E --> G[layout.tsx]
    E --> H[marketplace/]

    C --> I[ui/]
    C --> J[features/]
    C --> K[layouts/]

    D --> L[api/]
    D --> M[hooks/]
    D --> N[utils/]

    I --> O[button.tsx]
    I --> P[card.tsx]

    J --> Q[marketplace/]
    Q --> R[cask-card.tsx]
    Q --> S[cask-filters.tsx]
```

This data flow architecture provides a clear path for the migration while ensuring a smooth transition to PayloadJS in the future.

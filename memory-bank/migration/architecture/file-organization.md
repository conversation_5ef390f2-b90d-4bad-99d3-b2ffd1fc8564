# File Organization Principles

This document outlines the principles for organizing files in the DCasks frontend migration project, focusing on creating smaller, more focused files and components.

## Directory Structure

```mermaid
flowchart TD
    A[src/] --> B[app/]
    A --> C[components/]
    A --> D[lib/]
    A --> E[hooks/]
    A --> F[types/]
    A --> G[styles/]
    
    B --> H["(frontend)/"]
    H --> I[page.tsx]
    H --> J[layout.tsx]
    H --> K[marketplace/]
    H --> L[nft/]
    H --> M[profile/]
    
    C --> N[ui/]
    C --> O[features/]
    C --> P[layouts/]
    C --> Q[blockchain/]
    
    D --> R[api/]
    D --> S[utils/]
    D --> T[graphql/]
    D --> U[blockchain/]
    
    E --> V[use-casks.ts]
    E --> W[use-nfts.ts]
    E --> X[use-marketplace.ts]
    
    F --> Y[api.ts]
    F --> Z[blockchain.ts]
    F --> AA[components.ts]
```

## Core Principles

### 1. Small, Focused Files

Each file should have a single responsibility and be focused on a specific task. This makes the codebase more maintainable and easier to understand.

**Before:**
```typescript
// marketplace.tsx (large file with multiple responsibilities)
export default function Marketplace() {
  // Data fetching
  // Filtering logic
  // Sorting logic
  // Pagination logic
  // UI rendering
  // Event handling
  // ...
}
```

**After:**
```typescript
// marketplace/page.tsx (focused on composition)
export default function MarketplacePage() {
  return (
    <div>
      <MarketplaceHeader />
      <MarketplaceFilters />
      <MarketplaceGrid />
      <MarketplacePagination />
    </div>
  );
}

// components/features/marketplace/MarketplaceGrid.tsx (focused on grid rendering)
export function MarketplaceGrid() {
  const { casks, isLoading } = useCasks();
  
  if (isLoading) return <MarketplaceLoading />;
  
  return (
    <div className="grid grid-cols-3 gap-4">
      {casks.map(cask => (
        <CaskCard key={cask.id} cask={cask} />
      ))}
    </div>
  );
}
```

### 2. Descriptive Naming

Use clear, descriptive names for files and components that indicate their purpose and responsibility.

**Before:**
```
- Card.tsx
- List.tsx
- Modal.tsx
```

**After:**
```
- CaskCard.tsx
- CaskList.tsx
- CreateSellModal.tsx
```

### 3. Logical Grouping

Group related files in directories that reflect their purpose and relationship.

```
components/
  ui/              # Generic UI components
    button.tsx
    card.tsx
    input.tsx
  features/        # Feature-specific components
    marketplace/
      CaskCard.tsx
      CaskFilters.tsx
      CaskGrid.tsx
    nft/
      NftDetail.tsx
      NftImage.tsx
      NftInfo.tsx
  layouts/         # Layout components
    Header.tsx
    Footer.tsx
    Sidebar.tsx
```

### 4. Component Hierarchy

Establish a clear component hierarchy with different levels of abstraction.

```mermaid
flowchart TD
    A[Page Components] --> B[Layout Components]
    A --> C[Feature Components]
    C --> D[UI Components]
    D --> E[Atomic Components]
```

1. **Page Components**: Top-level components that represent a page
2. **Layout Components**: Components that define the structure of a page
3. **Feature Components**: Components that implement specific features
4. **UI Components**: Reusable UI components
5. **Atomic Components**: Primitive UI elements

### 5. Separation of Concerns

Separate different concerns into different files and components.

```mermaid
flowchart TD
    A[UI] --> B[Components]
    C[Logic] --> D[Hooks]
    E[Data] --> F[API]
    G[Types] --> H[Interfaces]
```

1. **UI**: Components that render the user interface
2. **Logic**: Hooks and utilities that implement business logic
3. **Data**: API clients and data fetching utilities
4. **Types**: TypeScript interfaces and types

## File Naming Conventions

### 1. Component Files

- Use PascalCase for component files
- Use descriptive names that indicate the component's purpose
- Group related components in directories

```
components/
  ui/
    Button.tsx
    Card.tsx
    Input.tsx
  features/
    marketplace/
      CaskCard.tsx
      CaskFilters.tsx
      CaskGrid.tsx
```

### 2. Hook Files

- Prefix hook files with `use-`
- Use kebab-case for hook files
- Group related hooks in directories

```
hooks/
  use-casks.ts
  use-nfts.ts
  marketplace/
    use-filters.ts
    use-pagination.ts
    use-sorting.ts
```

### 3. Utility Files

- Use descriptive names that indicate the utility's purpose
- Use kebab-case for utility files
- Group related utilities in directories

```
lib/
  utils/
    format-price.ts
    format-date.ts
    validate-input.ts
  api/
    casks.ts
    nfts.ts
    marketplace.ts
```

### 4. Type Files

- Use descriptive names that indicate the type's purpose
- Use kebab-case for type files
- Group related types in directories

```
types/
  api.ts
  blockchain.ts
  components.ts
  marketplace/
    cask.ts
    filter.ts
    pagination.ts
```

## Component Structure

### 1. Small Components

Break down large components into smaller, more focused components.

**Before:**
```tsx
// CaskDetail.tsx (large component)
export function CaskDetail({ cask }) {
  // Lots of state and logic
  
  return (
    <div>
      {/* Header */}
      <div>
        <h1>{cask.name}</h1>
        <p>{cask.distillery}</p>
        <p>{cask.region}</p>
      </div>
      
      {/* Images */}
      <div>
        <img src={cask.cover} alt={cask.name} />
        <div>
          {cask.images.map(image => (
            <img key={image} src={image} alt={cask.name} />
          ))}
        </div>
      </div>
      
      {/* Info */}
      <div>
        <p>{cask.description}</p>
        <p>Age: {cask.ays} years</p>
        <p>Target Bottling Date: {formatDate(cask.targetBottlingDate)}</p>
      </div>
      
      {/* Trading */}
      <div>
        <button>Buy</button>
        <button>Sell</button>
        <button>Auction</button>
      </div>
    </div>
  );
}
```

**After:**
```tsx
// CaskDetail.tsx (composition of smaller components)
export function CaskDetail({ cask }) {
  return (
    <div>
      <CaskHeader cask={cask} />
      <CaskImages cask={cask} />
      <CaskInfo cask={cask} />
      <CaskTrading cask={cask} />
    </div>
  );
}

// CaskHeader.tsx
export function CaskHeader({ cask }) {
  return (
    <div>
      <h1>{cask.name}</h1>
      <p>{cask.distillery}</p>
      <p>{cask.region}</p>
    </div>
  );
}

// CaskImages.tsx
export function CaskImages({ cask }) {
  return (
    <div>
      <img src={cask.cover} alt={cask.name} />
      <div>
        {cask.images.map(image => (
          <img key={image} src={image} alt={cask.name} />
        ))}
      </div>
    </div>
  );
}

// CaskInfo.tsx
export function CaskInfo({ cask }) {
  return (
    <div>
      <p>{cask.description}</p>
      <p>Age: {cask.ays} years</p>
      <p>Target Bottling Date: {formatDate(cask.targetBottlingDate)}</p>
    </div>
  );
}

// CaskTrading.tsx
export function CaskTrading({ cask }) {
  return (
    <div>
      <button>Buy</button>
      <button>Sell</button>
      <button>Auction</button>
    </div>
  );
}
```

### 2. Separation of Logic and UI

Separate business logic from UI rendering using custom hooks.

**Before:**
```tsx
// CaskFilters.tsx (logic and UI mixed)
export function CaskFilters() {
  const [filters, setFilters] = useState({
    minPrice: 0,
    maxPrice: 10000,
    region: [],
    distillery: [],
  });
  
  const handlePriceChange = (value) => {
    setFilters({ ...filters, minPrice: value[0], maxPrice: value[1] });
  };
  
  const handleRegionChange = (region, checked) => {
    if (checked) {
      setFilters({ ...filters, region: [...filters.region, region] });
    } else {
      setFilters({ ...filters, region: filters.region.filter(r => r !== region) });
    }
  };
  
  // More logic...
  
  return (
    <div>
      {/* UI rendering */}
    </div>
  );
}
```

**After:**
```tsx
// hooks/use-filters.ts (logic)
export function useFilters() {
  const [filters, setFilters] = useState({
    minPrice: 0,
    maxPrice: 10000,
    region: [],
    distillery: [],
  });
  
  const handlePriceChange = (value) => {
    setFilters({ ...filters, minPrice: value[0], maxPrice: value[1] });
  };
  
  const handleRegionChange = (region, checked) => {
    if (checked) {
      setFilters({ ...filters, region: [...filters.region, region] });
    } else {
      setFilters({ ...filters, region: filters.region.filter(r => r !== region) });
    }
  };
  
  // More logic...
  
  return {
    filters,
    handlePriceChange,
    handleRegionChange,
    // More handlers...
  };
}

// components/features/marketplace/CaskFilters.tsx (UI)
export function CaskFilters() {
  const {
    filters,
    handlePriceChange,
    handleRegionChange,
    // More handlers...
  } = useFilters();
  
  return (
    <div>
      {/* UI rendering */}
    </div>
  );
}
```

### 3. Server vs. Client Components

Clearly separate server and client components.

```tsx
// app/marketplace/page.tsx (server component)
export default async function MarketplacePage() {
  // Server-side data fetching
  const casks = await fetchCasks();
  
  return (
    <div>
      <MarketplaceHeader />
      <ClientFilters />
      <MarketplaceGrid casks={casks} />
    </div>
  );
}

// components/features/marketplace/ClientFilters.tsx (client component)
'use client';

export function ClientFilters() {
  // Client-side state and logic
  
  return (
    <div>
      {/* UI rendering */}
    </div>
  );
}
```

## Examples

### Example 1: Marketplace Page

```
app/
  (frontend)/
    marketplace/
      page.tsx              # Server component for the marketplace page
      loading.tsx           # Loading state for the marketplace page
      error.tsx             # Error state for the marketplace page

components/
  features/
    marketplace/
      MarketplaceHeader.tsx # Header for the marketplace page
      MarketplaceFilters.tsx # Filters for the marketplace (client component)
      MarketplaceGrid.tsx   # Grid of casks (server component)
      CaskCard.tsx          # Card for a single cask
      CaskCardSkeleton.tsx  # Skeleton loading state for a cask card
      
hooks/
  marketplace/
    use-filters.ts          # Hook for filter state and logic
    use-pagination.ts       # Hook for pagination state and logic
    use-sorting.ts          # Hook for sorting state and logic
    
lib/
  api/
    casks.ts                # API functions for casks
```

### Example 2: NFT Detail Page

```
app/
  (frontend)/
    nft/
      [id]/
        page.tsx            # Server component for the NFT detail page
        loading.tsx         # Loading state for the NFT detail page
        error.tsx           # Error state for the NFT detail page

components/
  features/
    nft/
      NftHeader.tsx         # Header for the NFT detail page
      NftImages.tsx         # Images for the NFT
      NftInfo.tsx           # Information about the NFT
      NftTrading.tsx        # Trading actions for the NFT (client component)
      NftHistory.tsx        # Transaction history for the NFT
      
hooks/
  nft/
    use-nft.ts              # Hook for NFT data
    use-trading.ts          # Hook for trading actions
    
lib/
  api/
    nfts.ts                 # API functions for NFTs
```

## Benefits

1. **Maintainability**: Smaller files are easier to understand and maintain
2. **Reusability**: Focused components can be reused in different contexts
3. **Testability**: Smaller components and functions are easier to test
4. **Performance**: Server components can be optimized for performance
5. **Collaboration**: Multiple developers can work on different files without conflicts
6. **Readability**: Clear file organization makes the codebase easier to navigate

## Implementation Strategy

1. **Analyze Existing Code**: Identify large files and components that can be broken down
2. **Create Directory Structure**: Set up the new directory structure
3. **Migrate Components**: Gradually migrate components to the new structure
4. **Extract Logic**: Extract business logic into custom hooks
5. **Separate Server and Client**: Clearly separate server and client components
6. **Update Imports**: Update import statements to reflect the new structure

This file organization approach will result in a more maintainable, scalable, and performant codebase for the DCasks frontend migration project.

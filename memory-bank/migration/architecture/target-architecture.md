# Target Architecture: DCasks on Jasmine

## Overview

The target architecture for the DCasks frontend migration is a modern Next.js 15 application using the App Router pattern with PayloadJS integration. This architecture will improve maintainability, performance, and developer experience while preserving all existing functionality.

## Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **CMS/API**: PayloadJS (for future backend integration)
- **Database**: MongoDB (via PayloadJS, for future use)
- **State Management**: React Context + React Query
- **Data Fetching**: 
  - Initially: Apollo Client (connecting to existing GraphQL API)
  - Future: PayloadJS API
- **Styling**: Tailwind CSS with shadcn/ui components
- **Form Handling**: React Hook Form with Zod validation
- **Blockchain Integration**: Reown AppKit (updated version)
- **Authentication**: Wallet-based (via Reown AppKit)

## Project Structure

```
apps/jasmine/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (frontend)/         # Frontend routes
│   │   │   ├── layout.tsx      # Main layout
│   │   │   ├── page.tsx        # Home page
│   │   │   ├── marketplace/    # Marketplace pages
│   │   │   ├── nft/            # NFT detail pages
│   │   │   └── profile/        # User profile pages
│   │   └── api/                # API routes
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # shadcn/ui components
│   │   ├── forms/              # Form components
│   │   └── blockchain/         # Blockchain-specific components
│   ├── lib/                    # Utility functions and libraries
│   │   ├── apollo/             # Apollo client setup (temporary)
│   │   └── blockchain/         # Blockchain utilities
│   ├── hooks/                  # Custom React hooks
│   ├── providers/              # React Context providers
│   ├── types/                  # TypeScript type definitions
│   └── blockchain/             # Smart contract integration
│       ├── abis/               # Contract ABIs
│       └── hooks/              # Contract interaction hooks
├── public/                     # Static assets
└── payload.config.ts           # PayloadJS configuration (for future use)
```

## Key Architectural Improvements

### 1. App Router Pattern

The Next.js App Router pattern offers several advantages:

- **Nested Layouts**: More intuitive layout nesting
- **Route Groups**: Logical organization of routes
- **Loading States**: Built-in loading UI
- **Error Boundaries**: Improved error handling
- **Server Components**: Better performance and SEO

### 2. Component Architecture

- **Client vs Server Components**: Clear separation of concerns
- **shadcn/ui Integration**: Consistent, accessible UI components
- **Component Co-location**: Components located near their usage

### 3. Data Fetching Strategy

- **Initial Phase**: 
  - Continue using Apollo Client to connect to existing GraphQL API
  - Refactor data fetching to be compatible with App Router
  - Create abstraction layer for future API changes

- **Future Phase**:
  - Transition to PayloadJS API
  - Implement server components for data fetching
  - Use React Query for client-side data management

### 4. State Management

- **React Context**: For global state (authentication, theme, etc.)
- **React Query**: For server state management
- **Local State**: For component-specific state
- **URL State**: For shareable UI state (filters, pagination, etc.)

### 5. Authentication Flow

- Continue using wallet-based authentication via Reown AppKit
- Implement middleware for protected routes
- Future: Integration with PayloadJS user model

## UI Component Architecture

The UI will follow a more structured component architecture:

- **Layout Components**: App-wide layouts and navigation
- **Page Components**: Page-specific components
- **Feature Components**: Feature-specific components
- **UI Components**: Reusable UI primitives (shadcn/ui)
- **Form Components**: Form-specific components

## Performance Optimizations

- **Server Components**: Reduce client-side JavaScript
- **Image Optimization**: Next.js Image component
- **Font Optimization**: Next.js Font optimization
- **Code Splitting**: Automatic code splitting
- **Bundle Analysis**: Regular bundle size monitoring

## SEO Improvements

- **Metadata API**: Next.js metadata for better SEO
- **Structured Data**: JSON-LD for rich search results
- **Sitemaps**: Automated sitemap generation
- **OpenGraph**: Enhanced social sharing

## Accessibility Improvements

- **ARIA Attributes**: Proper accessibility attributes
- **Keyboard Navigation**: Improved keyboard support
- **Focus Management**: Better focus handling
- **Color Contrast**: Ensuring sufficient contrast

## Future Integration with PayloadJS

While the initial focus is on the frontend migration, the architecture is designed to seamlessly integrate with PayloadJS in the future:

- **Collections**: Define data models as PayloadJS collections
- **API Routes**: Replace GraphQL with PayloadJS API
- **Authentication**: Integrate wallet authentication with PayloadJS
- **Admin Panel**: Utilize PayloadJS admin panel for content management

This architecture provides a solid foundation for the DCasks frontend while enabling future backend integration with PayloadJS.

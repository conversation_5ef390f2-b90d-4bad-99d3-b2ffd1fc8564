# DCasks Frontend Migration

## Overview

This documentation provides a comprehensive guide for migrating the DCasks frontend from Next.js 12 Pages Router to Next.js 15 App Router with PayloadJS integration. The migration focuses on improving maintainability, performance, and developer experience while preserving all existing functionality.

## Documentation Structure

```mermaid
flowchart TD
    A[Migration Documentation] --> B[Architecture]
    A --> C[Components]
    A --> D[Features]
    A --> E[API]
    A --> F[Progress]
    A --> G[Resources]

    B --> B1[Current Architecture]
    B --> B2[Target Architecture]
    B --> B3[Data Flow]
    B --> B4[File Organization]

    C --> C1[Component Inventory]
    C --> C2[shadcn/ui Mapping]

    D --> D1[Home Page]
    D --> D2[Marketplace]
    D --> D3[Blockchain Integration]

    E --> E1[GraphQL Endpoints]
    E --> E2[Connector Strategy]

    F --> F1[Sprint Planning]
    F --> F2[Migration Status]
    F --> F3[Milestones]
    F --> F4[Blockers]

    G --> G1[Tech Stack]
    G --> G2[Dependencies]
    G --> G3[Learning Resources]
```

## Quick Links

### Architecture
- [Current Architecture](architecture/current-architecture.md)
- [Target Architecture](architecture/target-architecture.md)
- [Data Flow](architecture/data-flow.md)
- [File Organization](architecture/file-organization.md)

### Components
- [Component Inventory](components/inventory.md)
- [shadcn/ui Mapping](components/shadcn-mapping.md)

### Features
- [Home Page](features/home.md)
- [Marketplace](features/marketplace.md)
- [Blockchain Integration](features/blockchain.md)

### API
- [GraphQL Endpoints](api/graphql-endpoints.md)
- [Connector Strategy](api/connector-strategy.md)

### Progress
- [Sprint Planning](progress/sprint-planning.md)
- [Migration Status](progress/migration-status.md)
- [Milestones](progress/milestones.md)
- [Blockers](progress/blockers.md)

### Resources
- [Tech Stack](resources/tech-stack.md)
- [Dependencies](resources/dependencies.md)
- [Learning Resources](resources/learning-resources.md)

## Key Principles

1. **Small, Focused Components**: Break down large components into smaller, more focused ones
2. **Clear Data Flow**: Separate server and client state management
3. **Type Safety**: Ensure strong typing throughout the codebase
4. **Performance**: Leverage server components for improved performance
5. **Accessibility**: Ensure all components are accessible
6. **Testing**: Comprehensive testing with Playwright

## Getting Started

1. Review the [migration overview](overview.md)
2. Explore the [target architecture](architecture/target-architecture.md)
3. Check the [component inventory](components/inventory.md)
4. Review the [sprint planning](progress/sprint-planning.md)
5. Track progress in the [migration status](progress/migration-status.md)

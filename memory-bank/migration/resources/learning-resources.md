# Learning Resources

This document provides a curated list of learning resources for the technologies used in the DCasks frontend migration project.

## Next.js App Router

### Official Documentation
- [Next.js Documentation](https://nextjs.org/docs) - Official Next.js documentation
- [App Router Documentation](https://nextjs.org/docs/app) - Official App Router documentation
- [Next.js GitHub Repository](https://github.com/vercel/next.js) - Source code and examples

### Tutorials and Courses
- [Next.js App Router Course](https://nextjs.org/learn) - Official Next.js course
- [Mastering Next.js](https://masteringnextjs.com/) - Comprehensive Next.js course
- [Next.js App Router Playground](https://app-router.vercel.app/) - Interactive examples

### Articles and Guides
- [Migrating from Pages to App Router](https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration) - Official migration guide
- [Understanding Server Components](https://vercel.com/blog/understanding-react-server-components) - Vercel's guide to Server Components
- [Next.js App Router: The Complete Guide](https://www.joshwcomeau.com/react/server-components/) - In-depth guide by Josh Comeau

### Videos
- [Next.js App Router Crash Course](https://www.youtube.com/watch?v=ZVnjOPwW4ZA) - Crash course by Vercel
- [Next.js 13 App Router Full Course](https://www.youtube.com/watch?v=wm5gMKuwSYk) - Comprehensive course by Traversy Media
- [Server Components and the App Router](https://www.youtube.com/watch?v=RHw1REhR1Qc) - Deep dive by Lee Robinson

## React Server Components

### Official Documentation
- [React Server Components RFC](https://github.com/reactjs/rfcs/blob/main/text/0188-server-components.md) - Official RFC
- [React Server Components Docs](https://react.dev/blog/2023/03/22/react-labs-what-we-have-been-working-on-march-2023#react-server-components) - React team's update

### Articles and Guides
- [Understanding React Server Components](https://vercel.com/blog/understanding-react-server-components) - Vercel's guide
- [React Server Components: A New Era for React](https://www.patterns.dev/posts/react-server-components) - In-depth explanation
- [The Complete Guide to React Server Components](https://www.joshwcomeau.com/react/server-components/) - Comprehensive guide

### Videos
- [React Server Components Explained](https://www.youtube.com/watch?v=TQQPAU21ZUw) - Explanation by Jack Herrington
- [React Server Components Deep Dive](https://www.youtube.com/watch?v=BZlwtR9pDp4) - Deep dive by Theo

## shadcn/ui

### Official Documentation
- [shadcn/ui Documentation](https://ui.shadcn.com/docs) - Official documentation
- [shadcn/ui Components](https://ui.shadcn.com/docs/components/accordion) - Component documentation
- [shadcn/ui GitHub Repository](https://github.com/shadcn-ui/ui) - Source code and examples

### Tutorials and Guides
- [Getting Started with shadcn/ui](https://ui.shadcn.com/docs/installation) - Official installation guide
- [Customizing shadcn/ui](https://ui.shadcn.com/docs/theming) - Theming and customization
- [Building a Dashboard with shadcn/ui](https://ui.shadcn.com/docs/figma) - Design resources

### Videos
- [shadcn/ui Crash Course](https://www.youtube.com/watch?v=dD1fpoGHuC8) - Introduction by Traversy Media
- [Build a Modern UI with shadcn/ui](https://www.youtube.com/watch?v=7MKEOfSP2s4) - Tutorial by Web Dev Simplified
- [shadcn/ui: From Zero to Hero](https://www.youtube.com/watch?v=DTGRIaAJYIo) - Comprehensive guide

## React Query

### Official Documentation
- [TanStack Query Documentation](https://tanstack.com/query/latest/docs/react/overview) - Official documentation
- [TanStack Query GitHub Repository](https://github.com/TanStack/query) - Source code and examples

### Tutorials and Guides
- [Practical React Query](https://tkdodo.eu/blog/practical-react-query) - Series of articles by TkDodo
- [React Query Data Transformations](https://tkdodo.eu/blog/react-query-data-transformations) - Guide to data transformations
- [React Query as a State Manager](https://tkdodo.eu/blog/react-query-as-a-state-manager) - Using React Query for state management

### Videos
- [React Query in 100 Seconds](https://www.youtube.com/watch?v=novnyCaa7To) - Quick introduction by Fireship
- [React Query: Complete Course](https://www.youtube.com/watch?v=8K1N3fE-cDs) - Comprehensive course
- [React Query: Advanced Patterns](https://www.youtube.com/watch?v=HPKK2Gv9Eso) - Advanced usage patterns

## urql and GraphQL Codegen

### Official Documentation
- [urql Documentation](https://formidable.com/open-source/urql/docs/) - Official documentation
- [urql GitHub Repository](https://github.com/urql-graphql/urql) - Source code and examples
- [GraphQL Codegen Documentation](https://the-guild.dev/graphql/codegen/docs/getting-started) - Official documentation

### Tutorials and Guides
- [Getting Started with urql](https://formidable.com/open-source/urql/docs/basics/getting-started/) - Official getting started guide
- [urql with Next.js](https://formidable.com/open-source/urql/docs/advanced/server-side-rendering/#nextjs) - Integration guide
- [urql Caching](https://formidable.com/open-source/urql/docs/basics/document-caching/) - Caching strategies
- [GraphQL Codegen with urql](https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-urql) - Type generation for urql

### Videos
- [urql Crash Course](https://www.youtube.com/watch?v=oupYQxFdY28) - Introduction to urql
- [GraphQL with urql](https://www.youtube.com/watch?v=Xyu7m3mFaLU) - Comprehensive guide
- [GraphQL Codegen Tutorial](https://www.youtube.com/watch?v=YTEu9XVCxYo) - Type-safe GraphQL operations

## PayloadJS

### Official Documentation
- [PayloadCMS Documentation](https://payloadcms.com/docs/getting-started/what-is-payload) - Official documentation
- [PayloadCMS GitHub Repository](https://github.com/payloadcms/payload) - Source code and examples

### Tutorials and Guides
- [Getting Started with PayloadCMS](https://payloadcms.com/docs/getting-started/installation) - Official installation guide
- [PayloadCMS with Next.js](https://payloadcms.com/docs/integrations/next-js) - Integration guide
- [Building a Blog with PayloadCMS](https://payloadcms.com/blog/building-a-blog-with-payload) - Tutorial

### Videos
- [PayloadCMS Crash Course](https://www.youtube.com/watch?v=1RQXGg9cDGk) - Introduction by Traversy Media
- [PayloadCMS: Complete Guide](https://www.youtube.com/watch?v=zrHCZDXXLJ8) - Comprehensive guide
- [PayloadCMS with Next.js App Router](https://www.youtube.com/watch?v=Qg3Uf2WkJ-g) - Integration tutorial

## Blockchain and Web3

### Official Documentation
- [Ethers.js Documentation](https://docs.ethers.org/v6/) - Official Ethers.js documentation
- [Reown AppKit Documentation](https://docs.reown.com/) - Official Reown AppKit documentation

### Tutorials and Guides
- [Getting Started with Ethers.js](https://docs.ethers.org/v6/getting-started/) - Official getting started guide
- [Ethers.js v6 Migration Guide](https://docs.ethers.org/v6/migrating/) - Migration guide from v5 to v6
- [Web3 Authentication Patterns](https://docs.reown.com/authentication) - Authentication patterns

### Videos
- [Ethers.js Crash Course](https://www.youtube.com/watch?v=yk7nVp5HTCk) - Introduction to Ethers.js
- [Web3 Authentication with Reown AppKit](https://www.youtube.com/watch?v=QjGZz_xf3cA) - Authentication tutorial
- [Building DApps with Next.js and Ethers.js](https://www.youtube.com/watch?v=a0osIaAOFSE) - Comprehensive guide

## Testing

### Official Documentation
- [Playwright Documentation](https://playwright.dev/docs/intro) - Official Playwright documentation
- [Playwright Test Runner](https://playwright.dev/docs/test-runners) - Playwright test runner documentation
- [Playwright Component Testing](https://playwright.dev/docs/test-components) - Component testing with Playwright

### Tutorials and Guides
- [Testing Next.js Applications with Playwright](https://nextjs.org/docs/testing#playwright) - Official Next.js Playwright guide
- [Testing React Server Components](https://nextjs.org/docs/app/building-your-application/testing/server-components) - Server component testing
- [End-to-End Testing with Playwright](https://playwright.dev/docs/writing-tests) - E2E testing guide

### Videos
- [Playwright Crash Course](https://www.youtube.com/watch?v=Xz6lhEzgI5I) - Introduction to Playwright
- [Playwright End-to-End Testing](https://www.youtube.com/watch?v=wawbt1cATsk) - Comprehensive Playwright guide
- [Visual Testing with Playwright](https://www.youtube.com/watch?v=MXfZeE9RQDw) - Visual testing tutorial

## TypeScript

### Official Documentation
- [TypeScript Documentation](https://www.typescriptlang.org/docs/) - Official documentation
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html) - Comprehensive guide

### Tutorials and Guides
- [TypeScript for JavaScript Programmers](https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html) - Quick start guide
- [TypeScript with React](https://react-typescript-cheatsheet.netlify.app/) - React TypeScript cheatsheet
- [Advanced TypeScript Patterns](https://www.typescriptlang.org/docs/handbook/advanced-types.html) - Advanced type patterns

### Videos
- [TypeScript Crash Course](https://www.youtube.com/watch?v=BCg4U1FzODs) - Introduction by Traversy Media
- [TypeScript for React Developers](https://www.youtube.com/watch?v=Z5iWr6Srsj8) - TypeScript with React
- [Advanced TypeScript](https://www.youtube.com/watch?v=F7O4gA0RRKc) - Advanced concepts

## Tailwind CSS

### Official Documentation
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - Official documentation
- [Tailwind CSS GitHub Repository](https://github.com/tailwindlabs/tailwindcss) - Source code and examples

### Tutorials and Guides
- [Tailwind CSS: From Zero to Production](https://tailwindcss.com/docs/installation) - Official guide
- [Customizing Tailwind CSS](https://tailwindcss.com/docs/configuration) - Configuration guide
- [Responsive Design with Tailwind CSS](https://tailwindcss.com/docs/responsive-design) - Responsive design patterns

### Videos
- [Tailwind CSS Crash Course](https://www.youtube.com/watch?v=UBOj6rqRUME) - Introduction by Traversy Media
- [Tailwind CSS: Complete Course](https://www.youtube.com/watch?v=lCxcTsOHrjo) - Comprehensive guide
- [Advanced Tailwind CSS](https://www.youtube.com/watch?v=pfaSUYaSgRo) - Advanced concepts

## Form Handling

### Official Documentation
- [React Hook Form Documentation](https://react-hook-form.com/get-started) - Official documentation
- [Zod Documentation](https://zod.dev/) - Official Zod documentation

### Tutorials and Guides
- [Getting Started with React Hook Form](https://react-hook-form.com/get-started) - Official guide
- [Form Validation with Zod](https://zod.dev/?id=introduction) - Zod validation guide
- [React Hook Form with Zod](https://react-hook-form.com/get-started#SchemaValidation) - Integration guide

### Videos
- [React Hook Form Crash Course](https://www.youtube.com/watch?v=bU_eq8qyjic) - Introduction
- [Form Validation with Zod](https://www.youtube.com/watch?v=AeQ3f4V_FzQ) - Zod tutorial
- [Building Forms with React Hook Form and Zod](https://www.youtube.com/watch?v=QALpkRi5itA) - Comprehensive guide

## Community Resources

### Forums and Communities
- [Next.js Discord](https://discord.gg/nextjs) - Official Next.js Discord
- [React Discord](https://discord.gg/reactiflux) - React community Discord
- [PayloadCMS Discord](https://discord.gg/payload) - PayloadCMS community Discord

### Blogs and Newsletters
- [Vercel Blog](https://vercel.com/blog) - Vercel's official blog
- [React Newsletter](https://reactnewsletter.com/) - Weekly React newsletter
- [JavaScript Weekly](https://javascriptweekly.com/) - Weekly JavaScript newsletter

### GitHub Repositories
- [Next.js Examples](https://github.com/vercel/next.js/tree/canary/examples) - Official Next.js examples
- [shadcn/ui Examples](https://github.com/shadcn-ui/ui/tree/main/apps/www/app/examples) - shadcn/ui examples
- [PayloadCMS Templates](https://github.com/payloadcms/payload/tree/main/templates) - PayloadCMS templates

## Books

- [Next.js in Action](https://www.manning.com/books/next-js-in-action) - Comprehensive Next.js guide
- [React Cookbook](https://www.oreilly.com/library/view/react-cookbook/9781492085836/) - Recipes for React development
- [TypeScript in 50 Lessons](https://typescript-book.com/) - TypeScript guide
- [Testing JavaScript Applications](https://www.manning.com/books/testing-javascript-applications) - Comprehensive testing guide

## Online Courses

- [Epic React](https://epicreact.dev/) - Kent C. Dodds' React course
- [Total TypeScript](https://www.totaltypescript.com/) - Matt Pocock's TypeScript course
- [Web3 University](https://www.web3.university/) - Web3 development courses

## Recommended Learning Path

For team members new to these technologies, we recommend the following learning path:

1. **Next.js App Router Fundamentals**
   - Complete the official Next.js App Router course
   - Build a simple application with App Router

2. **React Server Components**
   - Understand the difference between client and server components
   - Practice data fetching with server components

3. **shadcn/ui and Tailwind CSS**
   - Set up shadcn/ui in a Next.js project
   - Build UI components with shadcn/ui and Tailwind CSS

4. **Data Fetching and State Management**
   - Learn React Query for server state management
   - Understand Apollo Client for GraphQL data fetching

5. **Form Handling**
   - Practice form handling with React Hook Form and Zod
   - Build complex forms with validation

6. **Blockchain Integration**
   - Understand Ethers.js for blockchain interaction
   - Implement wallet connection with Reown AppKit

7. **Testing**
   - Write unit tests with Jest and React Testing Library
   - Implement end-to-end tests with Cypress

This learning path will provide a solid foundation for contributing to the DCasks frontend migration project.

## Internal Documentation

In addition to these external resources, we maintain internal documentation specific to our project:

- [Project Architecture](../architecture/target-architecture.md) - Overview of the target architecture
- [Component Library](../components/inventory.md) - Inventory of UI components
- [API Documentation](../api/graphql-endpoints.md) - Documentation of API endpoints
- [Blockchain Integration](../features/blockchain.md) - Documentation of blockchain integration

This document will be updated regularly with new resources as they become available.

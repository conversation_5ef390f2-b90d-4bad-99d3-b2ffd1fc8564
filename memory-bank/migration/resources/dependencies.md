# Dependencies and Versions

This document outlines the dependencies and their versions for the DCasks frontend migration project.

## Core Dependencies

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| next | 15.1.7 | 15.x | React framework | App Router, server components |
| react | 19.0.0 | 19.x | UI library | Core React library |
| react-dom | 19.0.0 | 19.x | DOM rendering | React DOM rendering |
| typescript | 5.7.3 | 5.x | Type checking | Static type checking |

## UI and Styling

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| tailwindcss | 3.1.8 | 3.x | CSS framework | Utility-first CSS |
| @tailwindcss/forms | 0.5.3 | 0.5.x | Form styling | Form input styling |
| class-variance-authority | 0.7.1 | 0.7.x | Component styling | Dynamic class composition |
| clsx | 2.1.1 | 2.x | Class utilities | Class name utilities |
| tailwind-merge | 1.6.0 | 1.x | Class merging | Merge Tailwind classes |
| lucide-react | 0.479.0 | 0.x | Icons | Icon library |
| @radix-ui/react-slot | 1.1.2 | 1.x | UI primitives | Slot component |
| @radix-ui/react-checkbox | 1.0.4 | 1.x | UI primitives | Checkbox component |
| @radix-ui/react-label | 2.0.2 | 2.x | UI primitives | Label component |
| @radix-ui/react-select | 2.0.0 | 2.x | UI primitives | Select component |
| @radix-ui/react-tabs | 1.1.2 | 1.x | UI primitives | Tabs component |

## Data Fetching and State Management

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| @urql/core | 4.2.0 | 4.x | GraphQL client | GraphQL data fetching |
| @urql/next | 1.1.0 | 1.x | Next.js integration | Next.js integration for urql |
| graphql | 16.6.0 | 16.x | GraphQL | GraphQL language |
| graphql-codegen | 5.0.0 | 5.x | GraphQL code generation | Type-safe GraphQL operations |
| @tanstack/react-query | 5.59.16 | 5.x | Data fetching | Server state management |
| jotai | 2.12.0 | 2.x | State management | Atomic state management |

## Form Handling

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| react-hook-form | 7.35.0 | 7.x | Form management | Form state management |
| @hookform/resolvers | 2.9.8 | 3.x | Form validation | Form validation resolvers |
| zod | 3.22.4 | 3.x | Schema validation | Type-safe schema validation |

## Blockchain Integration

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| @reown/appkit | 1.6.8 | 1.6.x | Wallet connection | Blockchain wallet integration |
| @reown/appkit-adapter-ethers | 1.6.8 | 1.6.x | Ethers adapter | Ethers.js adapter for AppKit |
| ethers | 6.13.5 | 6.x | Ethereum library | Ethereum interaction |

## UI Components and Utilities

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| date-fns | 2.29.3 | 2.x | Date utilities | Date formatting and manipulation |
| react-toastify | 11.0.3 | 11.x | Notifications | Toast notifications |
| react-loading-skeleton | 3.1.0 | 3.x | Loading UI | Skeleton loading UI |
| react-spinners | 0.13.4 | 0.13.x | Loading UI | Loading spinners |
| react-dropzone | 14.2.2 | 14.x | File upload | File upload component |
| react-select | 5.4.0 | 5.x | Select component | Advanced select component |
| react-modal | 3.15.1 | 3.x | Modal component | Modal dialog component |

## Charts and Visualization

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| chart.js | 4.4.8 | 4.x | Charts | Chart library |
| react-chartjs-2 | 5.3.0 | 5.x | React charts | React wrapper for Chart.js |
| recharts | 2.15.1 | 2.x | React charts | React charts library |
| chartjs-adapter-date-fns | 3.0.0 | 3.x | Chart.js adapter | Date adapter for Chart.js |

## Utilities

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| axios | 0.27.2 | 1.x | HTTP client | HTTP requests |
| js-cookie | 3.0.1 | 3.x | Cookie handling | Browser cookie management |
| lodash | 4.17.21 | 4.x | Utilities | JavaScript utilities |
| sharp | 0.31.1 | 0.32.x | Image processing | Image optimization |
| qrcode | 1.5.4 | 1.5.x | QR codes | QR code generation |
| react-qr-code | 2.0.15 | 2.x | QR codes | QR code component |

## Development Dependencies

| Package | Current Version | Target Version | Purpose | Notes |
|---------|----------------|----------------|---------|-------|
| eslint | 8.24.0 | 8.x | Linting | Code linting |
| eslint-config-next | 12.3.1 | 15.x | Next.js linting | Next.js linting rules |
| prettier | 2.7.1 | 3.x | Code formatting | Code formatting |
| @playwright/test | 1.40.0 | 1.x | Testing | End-to-end testing |
| @types/react | 18.0.21 | 18.x | TypeScript types | React TypeScript types |
| @types/node | 18.7.19 | 20.x | TypeScript types | Node.js TypeScript types |
| postcss | 8.4.16 | 8.x | CSS processing | CSS post-processing |
| autoprefixer | 10.4.12 | 10.x | CSS prefixing | CSS vendor prefixing |

## Package.json Configuration

```json
{
  "name": "@dcasks/jasmine",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "playwright test",
    "test:ui": "playwright test --ui",
    "codegen": "graphql-codegen",
    "typecheck": "tsc --noEmit"
  },
  "dependencies": {
    "@hookform/resolvers": "^3.1.0",
    "@radix-ui/react-checkbox": "^1.0.4",
    "@radix-ui/react-label": "^2.0.2",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-slot": "^1.1.2",
    "@radix-ui/react-tabs": "^1.1.2",
    "@reown/appkit": "^1.6.8",
    "@reown/appkit-adapter-wagmi": "^1.6.8",
    "@tanstack/react-query": "^5.59.16",
    "@urql/core": "^4.2.0",
    "@urql/next": "^1.1.0",
    "axios": "^1.4.0",
    "chart.js": "^4.4.8",
    "chartjs-adapter-date-fns": "^3.0.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "date-fns": "^2.29.3",
    "ethers": "^6.13.5",
    "graphql": "^16.6.0",
    "jotai": "^2.12.0",
    "js-cookie": "^3.0.1",
    "lodash": "^4.17.21",
    "lucide-react": "^0.479.0",
    "next": "^15.1.7",
    "qrcode": "^1.5.4",
    "react": "19.0.0",
    "react-chartjs-2": "^5.3.0",
    "react-dom": "19.0.0",
    "react-dropzone": "^14.2.2",
    "react-hook-form": "^7.35.0",
    "react-loading-skeleton": "^3.1.0",
    "react-modal": "^3.15.1",
    "react-qr-code": "^2.0.15",
    "react-select": "^5.4.0",
    "react-spinners": "^0.13.4",
    "react-toastify": "11.0.3",
    "recharts": "^2.15.1",
    "sharp": "^0.32.1",
    "tailwind-merge": "^1.6.0",
    "zod": "^3.22.4"
  },
  "devDependencies": {
    "@graphql-codegen/cli": "^5.0.0",
    "@graphql-codegen/typescript": "^4.0.1",
    "@graphql-codegen/typescript-operations": "^4.0.1",
    "@graphql-codegen/typescript-urql": "^4.0.0",
    "@playwright/test": "^1.40.0",
    "@types/js-cookie": "^3.0.2",
    "@types/lodash": "^4.14.195",
    "@types/node": "^20.5.7",
    "@types/react": "^18.2.21",
    "@types/react-dom": "^18.2.7",
    "@types/react-modal": "^3.13.1",
    "autoprefixer": "^10.4.15",
    "eslint": "^8.48.0",
    "eslint-config-next": "^15.0.0",
    "eslint-config-prettier": "^9.0.0",
    "postcss": "^8.4.29",
    "prettier": "^3.0.3",
    "prettier-plugin-tailwindcss": "^0.5.4",
    "tailwindcss": "^3.3.3",
    "tailwindcss-animate": "^1.0.7",
    "typescript": "^5.2.2"
  }
}
```

## Dependencies Migration Strategy

### 1. Core Dependencies

1. **Next.js**: Upgrade to Next.js 15.x with App Router
2. **React**: Upgrade to React 19.x
3. **TypeScript**: Upgrade to TypeScript 5.x

### 2. UI and Styling

1. **Tailwind CSS**: Continue using Tailwind CSS 3.x
2. **shadcn/ui**: Add shadcn/ui components with Radix UI primitives
3. **Icons**: Migrate to Lucide React for icons

### 3. Data Fetching and State Management

1. **urql**: Replace Apollo Client with urql for GraphQL
2. **GraphQL Codegen**: Add GraphQL Codegen for type-safe queries
3. **React Query**: Add React Query for server state management
4. **Jotai**: Continue using Jotai for client-side state

### 4. Form Handling

1. **React Hook Form**: Continue using React Hook Form
2. **Zod**: Migrate from Yup to Zod for schema validation

### 5. Blockchain Integration

1. **Reown AppKit**: Continue using Reown AppKit
2. **Ethers.js**: Upgrade to Ethers.js v6

### 6. Testing

1. **Playwright**: Add Playwright for end-to-end and component testing

## Installation Instructions

```bash
# Create new Next.js project with App Router
npx create-next-app@latest dcasks-jasmine --typescript --tailwind --eslint

# Install UI dependencies
npm install class-variance-authority clsx tailwind-merge lucide-react
npm install @radix-ui/react-slot @radix-ui/react-checkbox @radix-ui/react-label @radix-ui/react-select @radix-ui/react-tabs

# Install data fetching and state management
npm install @urql/core @urql/next graphql @tanstack/react-query jotai

# Install GraphQL Codegen
npm install -D @graphql-codegen/cli @graphql-codegen/typescript @graphql-codegen/typescript-operations @graphql-codegen/typescript-urql

# Install form handling
npm install react-hook-form @hookform/resolvers zod

# Install blockchain integration
npm install @reown/appkit @reown/appkit-adapter-wagmi ethers

# Install utilities
npm install date-fns js-cookie lodash sharp qrcode react-qr-code

# Install UI components
npm install react-toastify react-loading-skeleton react-spinners react-dropzone react-select react-modal

# Install chart libraries
npm install chart.js react-chartjs-2 recharts chartjs-adapter-date-fns

# Install development dependencies
npm install -D @playwright/test
```

## Configuration Files

### 1. tsconfig.json

```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 2. tailwind.config.js

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

### 3. playwright.config.ts

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});
```

### 4. codegen.ts

```typescript
import { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: process.env.NEXT_PUBLIC_GRAPHQL_URL,
  documents: ['src/**/*.tsx', 'src/**/*.ts'],
  generates: {
    './src/lib/graphql/generated.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-urql',
      ],
      config: {
        withHooks: true,
        withComponent: false,
        withHOC: false,
      },
    },
  },
};

export default config;
```

## Dependency Management Best Practices

1. **Version Pinning**: Pin dependencies to specific versions to ensure consistency
2. **Regular Updates**: Regularly update dependencies to get security fixes and new features
3. **Dependency Auditing**: Use `npm audit` to check for security vulnerabilities
4. **Peer Dependencies**: Be aware of peer dependency requirements
5. **Bundle Size Monitoring**: Monitor bundle size with tools like `next/bundle-analyzer`
6. **Tree Shaking**: Ensure dependencies support tree shaking to reduce bundle size
7. **Type Definitions**: Use TypeScript type definitions for all dependencies
8. **Testing**: Test the application after dependency updates

## Component Organization

```mermaid
flowchart TD
    A[src/] --> B[components/]
    A --> C[app/]
    A --> D[lib/]
    B --> E[ui/]
    B --> F[features/]
    B --> G[layouts/]
    C --> H[(frontend)/]
    C --> I[api/]
    D --> J[graphql/]
    D --> K[hooks/]
    D --> L[utils/]
```

This document provides a comprehensive overview of the dependencies and their versions for the DCasks frontend migration project, along with installation instructions and configuration files.

# DCasks Project Brief

## Project Overview
DCasks is a blockchain-based platform for tokenizing, trading, and investing in whisky casks and other spirits. The platform enables fractional ownership of high-value spirits through NFTs, creating a marketplace for investors, collectors, and enthusiasts primarily using ERC1155 and ERC20 tokens on layer 2 blockchain.

## Core Components & Services

### DCasks Smart Contracts
Smart contracts providing the blockchain foundation for the platform:
- **NFT Implementation (DCaskBottle)** - ERC1155 tokens representing ownership of physical bottles/casks
- **Marketplace (DCaskMarket)** - Contract for buying/selling DCasks NFTs
- **Membership System (DCaskMembership)** - Tiered system providing benefits to platform users
- **Profit Model (DCaskProfitModel)** - Commission and fee distribution system
- **Tulip Features (DCaskTulip)** - Special features or promotions related to the platform

### DCasks Backend
NestJS-based application providing server-side functionality:
- Blockchain interactions (both Ethereum and Solana)
- User management and authentication
- NFT operations and market functionality 
- API services via GraphQL and data persistence with MongoDB
- Mining operations (particularly for Solana)
- System activity logs and notifications

### DCasks Frontend
User-facing Next.js application:
- Browse, buy, and sell whisky cask NFTs
- Blockchain wallet integrations (e.g., MetaMask)
- Display cask details, investment information, and marketplace listings
- User authentication, profiles, and transaction history
- Educational content about whisky investment

## Primary Goals
- Create a secure, transparent blockchain platform for whisky/spirits investment
- Enable fractional ownership of high-value whisky casks
- Provide a marketplace for trading these digital assets
- Implement multiple sale types: direct sales, auctions, and offers
- Build trust through transparency and detailed asset information
- Maintain security for blockchain transactions and user data
- Provide accessible information about whisky as an investment class

## Technical Foundation

### Smart Contracts
- Built on Ethereum/EVM compatible blockchains
- Smart contracts developed in Solidity
- Implementation of ERC1155 token standard for NFTs
- Access control for admin functions
- Support for multiple payment tokens
- Upgradeable contract architecture
- Deterministic token ID generation for consistent tracking

### Backend
- **Framework**: NestJS (Node.js)
- **API**: GraphQL with Apollo Server
- **Database**: MongoDB
- **Blockchain Integration**: Ethereum and Solana
- **Authentication**: JWT-based with passport
- **File Storage**: AWS S3
- **Email Service**: AWS SES
- **Monitoring**: Sentry
- **Transaction Tracking**: Storage of blockchain transaction hashes

### Frontend
- Next.js application (currently on Next.js 15, using Pages Router)
- React components with TailwindCSS styling
- GraphQL with Apollo Client for data fetching
- Ethers.js for blockchain interaction
- TypeScript for type safety

## Current Status
- Beta phase
- Recently upgraded to Next.js 15 in the frontend but maintaining legacy architecture (Pages Router)
- Implementing whole cask functionality in the backend
- No comprehensive automated testing in place

## Development Challenges
- Maintaining consistent code patterns across the codebase
- Technical debt from developer turnover
- Legacy architecture despite framework upgrades
- Ensuring seamless integration between all three components
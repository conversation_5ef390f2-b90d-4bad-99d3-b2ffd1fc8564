# DCasks System Patterns

## Overall System Architecture

The DCasks platform consists of three main components that work together to provide a complete whisky investment platform:

```mermaid
flowchart TD
    Frontend[Frontend Next.js App] --> Backend[Backend NestJS API]
    Frontend --> Blockchain[Web3/Blockchain]
    Backend --> Blockchain
    Backend --> Database[(MongoDB)]
    Blockchain --> SmartContracts[Smart Contracts]
    AdminCMS[Admin CMS] --> Backend
```

## Documentation Architecture

DCasks employs a comprehensive documentation architecture that complements the Memory Bank with detailed feature and technical specifications:

```mermaid
flowchart TD
    Overview[docs/overview.md] --> Features[Feature Documentation]
    Overview --> Architecture[Architecture Documentation]

    Features --> F1[docs/features/feature1.md]
    Features --> F2[docs/features/feature2.md]
    Features --> FN[docs/features/featureN.md]

    Architecture --> A1[docs/architecture/technical-overview.md]
    Architecture --> A2[docs/architecture/data-model.md]
    Architecture --> A3[docs/architecture/integration-points.md]

    Overview <-.-> MB[memory-bank/*]
    F1 <-.-> MB
    F2 <-.-> MB
    FN <-.-> MB
    A1 <-.-> MB
    A2 <-.-> MB
    A3 <-.-> MB
```

### Documentation Patterns
- **Feature-Based Documentation**: Each significant feature has dedicated documentation with diagrams
- **Architecture Documentation**: Technical architecture, data models, and integration points documented separately
- **Mermaid Diagrams**: Visual representation of processes, data flows, and component relationships
- **Cross-Referencing**: Documentation linked to relevant Memory Bank files for context
- **Auto-Update**: Documentation updated through AI assistance using the **update documentation** command

## Smart Contracts Architecture

### Core Contract Structure
The DCasks system consists of multiple smart contracts that work together:
- **DCaskNft/DCaskBottle**: ERC1155 tokens representing ownership of physical bottles/casks
- **DCaskMarketplace**: Handles sales, auctions, and offers
- **DCaskProfitModel**: Handles fee calculations and distribution
- **DCaskMembership**: Membership functionality and privileges
- **Supporting Contracts**: Swap, Wokens, MultiTransfer

### Contract Relationships
- DCaskNft is the base for tokenization
- DCaskMarketplace depends on DCaskNft for token transfers and ownership verification
- DCaskProfitModel is used by DCaskMarketplace for fee calculations
- DCaskMembership provides special privileges in the marketplace

## Backend Architecture

### Module Structure
DCasks Backend follows a modular architecture based on NestJS principles:

```mermaid
flowchart TD
    Client[Client Applications] --> API[GraphQL API]
    API --> Auth[Auth Module]
    API --> User[User Module]
    API --> Cask[Cask Module]
    API --> Market[Market Module]
    API --> NFT[NFT Module]
    API --> Mining[Mining Module]
    API --> WholeCask[Whole Cask Module]

    Auth --> Provider[Provider Module]
    User --> Provider
    Cask --> Provider
    Market --> Provider
    NFT --> Provider
    Mining --> Provider
    WholeCask --> Provider

    Provider --> Blockchain[Blockchain Services]
    Provider --> Database[(MongoDB)]
    Provider --> Storage[File Storage]
    Provider --> Email[Email Service]
```

### Design Patterns
- **Module Pattern**: Functional modules (auth, user, cask, etc.) with resolvers, services, models, DTOs
- **Repository Pattern**: Models define data structure, services contain business logic
- **Dependency Injection**: NestJS's IoC container for loose coupling and testability
- **GraphQL API Design**: Resolvers for queries and mutations, DTOs for input/output structures
- **Blockchain Integration**: Interfaces with both Ethereum (ethers.js) and Solana (@solana/web3.js)
- **Factory Pattern**: Provider and contract factory services for better dependency management

### Provider Architecture
The provider module follows a factory-based architecture to manage different blockchain providers:

```mermaid
flowchart TD
    subgraph ProviderModule
        PS[ProviderService]
        PF[ProviderFactoryService]
        CF[ContractFactoryService]
    end

    PS --> PF
    PS --> CF
    PF <--> CF

    PF --> HP[HttpProvider]
    PF --> WP[WebSocketProvider]
    PF --> EP[EthProvider]

    CF --> HC[HTTP Contracts]
    CF --> WC[WebSocket Contracts]
    CF --> EC[Ethereum Contracts]

    subgraph Services
        OtherService[Other Services]
    end

    subgraph EventListener
        ELS[EventListenerService]
    end

    OtherService --> PS
    ELS --> CF

    HP --> BQ[(Blockchain Queries)]
    WP --> BE[(Blockchain Events)]
    EP --> EM[(Ethereum Mainnet)]
```

The key components of this pattern are:
- **ProviderFactoryService**: Creates and manages different provider instances (HTTP, WebSocket, ETH)
- **ContractFactoryService**: Creates and manages contract instances connected to appropriate providers
- **Provider Separation**: HTTP provider for queries and transactions, WebSocket for event listening
- **Reconnection Logic**: Automatic WebSocket reconnection with proper error handling
- **Contract Reconnection**: Contracts are reconnected when the provider reconnects

## Admin CMS Architecture

### Resource Structure
The Admin CMS is built using Refine and follows a resource-based architecture:

```mermaid
flowchart TD
    Login[Login] --> Dashboard[Dashboard]
    Dashboard --> Users[Users Resource]
    Dashboard --> CaskOwners[Cask Owners Resource]
    CaskOwners --> CaskOwnerRequests[Cask Owner Requests]
    Dashboard --> NFTs[NFTs Resource]
    NFTs --> CaskIssueRequests[Cask Issue Requests]
    NFTs --> WholeCaskIssueRequests[Whole Cask Issue Requests]
```

### Design Patterns
- **Resource Pattern**: Organized around CRUD operations on resources
- **List/Show Pattern**: Standard list and show views for each resource
- **Apollo Integration**: GraphQL operations using Apollo Client
- **Authentication**: JWT-based admin authentication
- **Component Structure**: Reusable components for tables, forms, and actions

## Frontend Architecture

### Component Structure

```mermaid
flowchart TD
    subgraph Frontend
        Pages[Pages Router]
        Components[Components]
        Hooks[Custom Hooks]
        State[State Management]
        API[API Layer]
    end

    subgraph External
        BC[Blockchain/Web3]
        GQL[GraphQL Backend]
    end

    Pages --> Components
    Pages --> Hooks
    Components --> Hooks
    Hooks --> State
    Hooks --> API
    API --> GQL
    Hooks --> BC
    State --> BC
```

### Design Patterns
- **State Management**: Jotai for global state, organized in `_state` directory
- **Component Structure**: Base components, feature-specific components, page-level components
- **Data Fetching**: Apollo Client for GraphQL operations organized in `apollo/` directory
- **Authentication**: JWT-based authentication with AuthProvider HOC and useAuth hook
- **Blockchain Integration**: WalletProvider HOC for Metamask connection, Ethers.js v6 for interactions

### UI Component Patterns

The DCasks frontend implements several UI component patterns to ensure consistency, reusability, and maintainability:

```mermaid
flowchart TD
    BaseComponents[Base Components] --> CompoundComponents[Compound Components]
    BaseComponents --> GenericComponents[Generic Components]

    GenericComponents --> TypedImplementations[Typed Implementations]

    CompoundComponents --> Pages[Pages]
    TypedImplementations --> Pages
```

#### Generic Component Pattern
For UI elements that share structure but display different data types, we use TypeScript generics to create reusable components:

```typescript
// Generic base component with TypeScript generics
export function GenericComponent<T extends BaseDataType>({
  data,
  renderContent,
}: GenericComponentProps<T>) {
  // Implementation
}

// Typed implementation
const SpecificComponent = ({ data }: Props) => {
  const renderContent = (specificData: SpecificDataType) => {
    // Specific rendering logic
  }

  return (
    <GenericComponent
      data={data}
      renderContent={renderContent}
    />
  )
}
```

#### Render Props Pattern
For customizable content areas within consistent UI structures:

```typescript
interface ComponentProps<T> {
  data: T
  renderHeader?: (data: T) => ReactNode
  renderFooter?: (data: T) => ReactNode
}
```

#### Example: CaskCard Component System
The CaskCard component system exemplifies these patterns:

```mermaid
flowchart TD
    CaskCard[CaskCard<T>] --> CaskHoverDetail[CaskHoverDetail<T>]
    CaskCard --> CaskCardFooter[CaskCardFooter<T>]

    WholeCaskCard[WholeCaskCard] --> CaskCard
    MarketplaceCard[MarketplaceCard] --> CaskCard
```

- **Base Component**: `CaskCard<T extends BaseCaskData>` with generic type parameter
- **Subcomponents**: `CaskHoverDetail` and `CaskCardFooter` handling specific aspects of the card UI
- **Implementations**: `WholeCaskCard` and `MarketplaceCard` providing specific data types and rendering logic
- **Utility Functions**: Shared date formatting (`formatDate`) and age calculation (`calculateAge`) functions

## Data Flow Patterns

### Authentication Flow

```mermaid
sequenceDiagram
    Frontend->>Backend: Login request
    Backend->>Provider: Validate credentials
    Provider->>Backend: Authentication result
    Backend->>Frontend: JWT token
    Frontend->>Frontend: Store in Cookie
    Frontend->>Frontend: Update Auth State
```

### NFT Purchase Flow

```mermaid
flowchart LR
    Browse[Browse NFTs] --> Connect[Connect Wallet]
    Connect --> Purchase[Initiate Purchase]
    Purchase --> Smart[Smart Contract Call]
    Smart --> TxConfirm[Transaction Confirmation]
    TxConfirm --> Event[Blockchain Event]
    Event --> Backend[Backend Record]
    Backend --> UIUpdate[UI Update]
```

### NFT Issuance Approval Flow

```mermaid
sequenceDiagram
    User->>Backend: Submit Cask/Whole Cask Issue Request
    Backend->>Database: Store Issue Request
    Admin->>CMS: Review Issue Request
    CMS->>Backend: Approve/Reject Request
    Backend->>Database: Update Request Status
    Backend->>User: Notification (if implemented)
    User->>Frontend: Check Status
```

### Blockchain Interaction Pattern

```mermaid
sequenceDiagram
    Frontend->>Backend: Request operation
    Backend->>Service: Process business logic
    Service->>Provider: Blockchain interaction
    Provider->>SmartContract: Submit transaction
    SmartContract->>Provider: Transaction result
    Provider->>Service: Process result
    Service->>Backend: Return result
    Backend->>Frontend: Response
```

### Blockchain Event Handling Pattern

```mermaid
sequenceDiagram
    SmartContract->>WSProvider: Emit event
    WSProvider->>EventListener: On event
    EventListener->>EventStorage: Store event
    EventStorage->>Database: Save event data
    EventProcessor->>Database: Query pending events
    EventProcessor->>Service: Process event
    Service->>Database: Update entity state
```

## Blockchain Transaction Patterns

### NFT Minting
- NFTs are minted through the blockchain integration layer
- Transaction hashes are stored with the associated entity (e.g., `WholeCaskIssue.txHash`)
- Frontend can display transaction status by querying the stored hash
- Blockchain events are processed asynchronously to update entity status

### Token ID Generation
- Deterministic token IDs are used where possible (e.g., using `nonce` values)
- Random token ID generation serves as fallback when no predetermined ID exists
- Token IDs are checked against existing IDs to prevent collisions

### User Wallet Integration
- Users connect via MetaMask or similar web3 wallets
- Address is used for authentication and transaction signing
- Wallet connection status is maintained in frontend state

## Development and Testing Patterns

### Backend
- Modular testing approach with NestJS testing utilities
- End-to-end tests for API validation
- Unit tests for business logic verification
- Mock provider and contract factories for isolated testing
- Test providers in isolation from blockchain networks

### Frontend
- Component testing with React Testing Library
- Jest for unit tests
- Cypress for end-to-end testing (when implemented)

### Smart Contracts
- Unit tests for individual contract functions
- Integration tests for contract interactions
- Hardhat tasks for manual verification
- Task-driven development for common operations

### Admin CMS
- Feature-driven development
- Reusable hooks for common admin operations
- Apollo GraphQL for data operations
- Resource-based pattern for consistent UI structure
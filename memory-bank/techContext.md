# DCasks Technical Context

## Technology Stack by Service

### Smart Contracts
- **Language**: Solidity ^0.8.0
- **Framework**: Hardhat - Ethereum development environment
- **Standards**: 
  - ERC1155 for NFT tokens (DCaskBottle)
  - ERC20 for payment tokens
- **Security**: OpenZeppelin contracts and libraries
- **Upgradeability**: Transparent proxy pattern

### Backend
- **Runtime**: Node.js
- **Framework**: NestJS (v10)
- **API**: GraphQL with Apollo Server (v4)
- **Database**: MongoDB with Mongoose ODM (v8)
- **Language**: TypeScript (v5.3+)
- **Package Manager**: Yarn

### Frontend
- **Framework**: Next.js 15 (using Pages Router)
- **UI Library**: React 19
- **Language**: TypeScript
- **Styling**: TailwindCSS with some SCSS
- **State Management**: Jotai
- **Data Fetching**: Apollo Client (GraphQL)
- **Form Handling**: React Hook Form with Zod/Yup validation

## Blockchain Integration

### Ethereum Integration
- **Smart Contracts**: Solidity with OpenZeppelin libraries
- **Client Libraries**: ethers.js (v5.7.0 for backend, v6 for frontend)
- **Type Safety**: TypeChain for type-safe contract interactions
- **Utilities**: eth-sig-util and ethereumjs-util for cryptographic operations
- **Token ID Generation**: Deterministic IDs using nonce values for consistent identification
- **Transaction Tracking**: Storage of transaction hashes (`txHash`) for improved auditability

### Provider Architecture
- **Provider Factory**: Specialized service managing different provider instances
- **HTTP Provider**: JsonRpcProvider for blockchain queries and transactions
- **WebSocket Provider**: WebSocketProvider for real-time event subscriptions
- **Reconnection Logic**: Automatic WebSocket reconnection with exponential backoff
- **Contract Factory**: Centralized service for managing contract instances
- **Separation of Concerns**: Query operations vs. event listening operations

### Solana Integration
- **Client Libraries**: @solana/web3.js for blockchain interactions
- **Token Operations**: @solana/spl-token for token operations
- **NFT Operations**: @metaplex-foundation libraries

### Networks
- **Ethereum**: Layer 2 networks for reduced gas fees
- **Solana**: For mining operations

## Development Environment

### Smart Contracts
```bash
# Install dependencies
yarn install
# Compile contracts
npx hardhat compile
# Run tests
npx hardhat test
# Run local blockchain
npx hardhat node
# Deploy contracts
npx hardhat run scripts/[script-name].ts
```

### Backend
```bash
# Install dependencies
yarn install
# Configure environment variables
cp .env.example .env
# Start development server
yarn start:dev
# Start job processing service
yarn start:dev:job
# Run database seeding
yarn seed:whole-cask
```

### Frontend
```bash
# Install dependencies
yarn install
# Run development server
yarn dev
# Generate GraphQL types
yarn codegenv2
# Generate TypeChain types
yarn typegen
# Build for production
yarn build
```

## Key Dependencies

### Smart Contracts
- **@openzeppelin/contracts-upgradeable**: For secure, standard implementations
- **@nomiclabs/hardhat-ethers**: Integration with ethers.js
- **@nomiclabs/hardhat-waffle**: Testing framework integration
- **Typechain**: TypeScript bindings for contracts

### Backend
- **Blockchain Libraries**: 
  - ethers.js: For Ethereum blockchain interactions
  - @solana/web3.js: For Solana blockchain interactions
- **Provider Architecture**:
  - Provider Factory Service: Management of different provider types
  - Contract Factory Service: Contract instantiation and management
  - Event Listener Service: WebSocket-based event subscription
- **Data Processing**: class-transformer, class-validator
- **Authentication**: passport, JWT
- **File Storage**: AWS S3, AWS SES
- **Monitoring**: Sentry

### Frontend
- **UI Components**: Headless UI, Heroicons, Lucide React, Radix UI, React Toastify
- **Blockchain/Web3**: Ethers.js, Reown AppKit
- **Data Management**: Apollo Client, TanStack Query
- **Visualization**: Chart.js, Recharts

## Infrastructure & Deployment

### Smart Contracts
- **Networks**: Configuration in hardhat.config.ts
- **Verification**: Contract verification on block explorers
- **Gas Optimization**: Batch operations and efficient storage patterns

### Backend
- **Containerization**: Docker
- **Deployment**: Docker Compose
- **Monitoring**: Sentry
- **Cloud Services**: AWS (S3 for file storage, SES for email)

### Frontend
- **Hosting**: Vercel platform for Next.js
- **Environment Variables**: API endpoints and contract addresses
- **Monitoring**: Sentry for error tracking

## Technical Constraints

### Smart Contracts
- **Gas Optimization**: Minimizing transaction costs
- **Security**: Role-based access control, secure fund handling
- **Blockchain Limitations**: Transaction finality, block gas limits

### Backend
- **Blockchain Throughput**: Transaction limits
- **Database Optimization**: MongoDB query optimization
- **Job Processing**: Background tasks for blockchain operations
- **WebSocket Stability**: Handling disconnections and reconnections gracefully
- **Provider Separation**: Maintaining clear boundaries between different provider types

### Frontend
- **Legacy Architecture**: Pages Router despite Next.js 15
- **Blockchain UX**: Handling wallet connections and transaction delays
- **Mobile Responsiveness**: Ensuring compatibility with mobile wallets

## Testing Approach

### Smart Contracts
- **Testing Framework**: Hardhat test suite with Mocha/Chai
- **Coverage**: Unit tests for contract functions
- **Verification**: Manual testing via Hardhat tasks

### Backend
- **Testing Framework**: Jest
- **Integration Tests**: API endpoint validation
- **Mocking**: 
  - Blockchain service mocks
  - Provider factory mocks
  - Contract factory mocks
- **Unit Tests**: Event listener and blockchain service tests
# DCasks Active Context

## Current Focus

The DCasks platform is currently undergoing several key initiatives across its components:

### Sailing Whisky NFT Marketplace Development
- Developing a new dedicated marketplace for Sailing Whisky NFTs with Helio payment integration
- Implementing dual payment options (cryptocurrency and credit card) through Helio
- Creating automated NFT transfer system upon payment confirmation
- Building real-time NFT state management (Available/Locked/Sold) with blockchain synchronization
- Designing single-page user experience with EVM wallet connection requirement

### Monorepo Consolidation and Clean Architecture
- Successfully consolidated all memory-bank documentation into a centralized repository at the monorepo root
- Moving toward clean architecture principles with clear separation of concerns
- Establishing unified development patterns across all services
- Emphasizing domain-driven design across the codebase
- Implemented comprehensive documentation system to complement Memory Bank

### Whole Cask Functionality Implementation
- Backend implementation of whole cask models, services, and resolvers
- Smart contract validation and inspection capabilities for whole cask NFTs
- Creation and execution of seeding scripts for whole cask test data
- Admin CMS page for whole cask request approval implemented and tested

### Frontend Stabilization
- Fixing errors in existing features after Next.js 15 upgrade
- Re-enabling and testing features that haven't been used recently
- Addressing inconsistencies in the codebase while working within Pages Router constraints
- Implementing Telegram channel integration with a button in the navigation bar and popup modal
- Implementing Facebook integration with button in navbar and popup modal
- Redesigning homepage with new carousel components for featured casks and marketplace items
- Refactoring UI components for better reusability, including the new generic CaskCard component system

### Blockchain Integration
- Ensuring proper interaction with ERC1155 tokens for whole cask NFTs
- Validating Ethers.js v6 integration in the frontend
- Monitoring whole cask NFT minting activities across networks
- Implemented specialized provider factory services for better blockchain integration
- Separated HTTP provider (queries) from WebSocket provider (event listening) for improved stability

## Recent Changes

### Monorepo Structure
- Consolidated memory-bank documentation from all services into a single source of truth
- Removed service-specific memory-bank folders to eliminate duplication
- Integrated all project context into comprehensive files organized by domain instead of service

### Backend
- Implemented whole cask functionality (models, DTOs, services, resolvers)
- Modified whole cask issue token ID generation to use nonce value instead of random generation
- Added txHash field to WholeCaskIssue model to track blockchain transaction hashes
- Created seeding scripts for whole cask data with Glasgow whisky details
- Added yarn-compatible script commands to package.json
- Successfully executed seeding scripts to populate test data
- Enhanced NFT minting process to store transaction hashes for better tracking
- Fixed GraphQL error for WholeCask queries by implementing separate query methods in WholeCaskIssueService
- Added proper projectId handling to ensure consistent string conversion throughout the service
- Implemented minPrice and maxPrice fields to the WholeCask model for market events
- Updated the WholeCask model to simplify NFT trading by using only the DCaskTulip market
- Removed unnecessary DCaskMarketplace references as whole cask NFTs should only be traded in the Tulip market
- Added improved error handling and logging for WholeCask operations
- Fixed WholeCask creator field updates to properly track ownership changes during NFT transfers
- Enhanced NFTService to handle both TransferSingle and TransferBatch events for WholeCask NFTs
- Added detailed logging for WholeCask creator updates to assist with monitoring and debugging
- Refactored blockchain provider architecture to use provider factory pattern for better code organization
- Split blockchain provider into HTTP provider (for queries) and WebSocket provider (for event listening)
- Implemented clean architecture principles in blockchain event handling with appropriate service boundaries
- Enhanced WebSocket provider with robust reconnection logic for event listener stability
- Added contract factory service to centralize contract initialization and management

### Admin CMS
- Added new whole-cask-issue-requests page under the NFTs menu
- Implemented List and Show views for whole cask requests
- Created GraphQL queries and mutations for whole cask operations
- Added approval/rejection functionality for admin users
- Tested and verified the complete workflow with real data

### Frontend
- Upgraded to Next.js 15 while maintaining Pages Router architecture
- Updated dependencies in package.json
- Migrated to Ethers.js v6 from previous version
- Refactored marketplace page to implement clean architecture with separate components for regular and whole casks
- Improved component organization by extracting domain-specific display logic
- Implemented specialized hooks for whole cask data with useWholeCaskNft and useWholeCaskDetail
- Refactored trading panels and bid sections to accept props rather than directly using hooks
- Enhanced component reusability by extracting prop interfaces and consistent data patterns
- Improved error handling with clear loading, error, and empty states in whole cask components
- Ensured consistent use of deterministic whole cask token IDs throughout the frontend
- Enhanced price chart tooltip functionality to display individual tooltips for each data point, improving user experience when comparing bottle prices
- Added Facebook integration with button in navbar and popup modal following the same pattern as Telegram integration
- Implemented new homepage design with FeatureCaskCarousel and MarketplaceCarousel components
- Added GraphQL integration for fetching cask data directly in components
- Replaced older grid-based marketplace display with modern carousel UI featuring interactive hover effects
- Created a reusable CaskCard component system with TypeScript generics to eliminate code duplication
- Implemented composable architecture for card components with CaskHoverDetail and CaskCardFooter sub-components
- Refactored WholeCaskCard and MarketplaceCard to use the new generic CaskCard component
- Added utility functions for consistent date formatting and age calculation

### Smart Contracts
- Added new Hardhat tasks in whole-cask.ts for token validation
- Verified that no whole cask NFTs have been minted on the testnet
- Confirmed that DCaskNft follows the ERC1155 standard, not ERC721
- Implemented proper token balance checking using balanceOf for ERC1155 tokens
- Enhanced blockchain interaction by using deterministic token IDs based on nonce values

## Active Decisions & Considerations

### Clean Architecture Principles
- Adopting stronger separation of concerns across all services
- Ensuring domain logic is independent of frameworks and external dependencies
- Focusing on maintainability and testability when extending functionality
- Consolidating common patterns into shared components where appropriate
- Implemented component separation in marketplace to create clear boundaries between regular and whole cask features
- Using composition over inheritance to improve modularity and maintainability
- Applying factory pattern for better dependency management in blockchain providers

### Architecture & Technical Approach
- Using Yarn as the package manager for the project
- Continuing with Pages Router for frontend despite Next.js 15 upgrade for stability
- Using both direct blockchain queries and GraphQL API for NFT status checks
- Following existing architectural patterns when extending functionality
- Using deterministic token ID generation based on nonce values for whole cask NFTs
- Tracking blockchain transaction hashes for better monitoring and debugging
- Ensuring proper type conversion for IDs between GraphQL and MongoDB
- Implementing checks to prevent duplicate WholeCask entities for the same token ID
- Separating blockchain provider concerns: HTTP for queries, WebSocket for events

### State Management
- Using Jotai for global state with hooks pattern in frontend
- Need to standardize state access patterns
- Potential inconsistencies in state management approach across codebase

### Integration Points
- Need clearer documentation on integration patterns between blockchain networks and application logic
- For ERC1155 tokens, querying balances for specific addresses rather than looking for single owners
- GraphQL endpoint provides convenient access to indexed NFT data
- WebSocket connections used for real-time blockchain event monitoring
- HTTP connections used for blockchain queries and transactions

### UI Component Architecture
- Implementing composable, reusable UI components with TypeScript generics
- Using render props pattern for flexible content areas while maintaining consistent structure
- Extracting common logic into utility functions
- Maintaining strict type safety with TypeScript interfaces and generics
- Creating documentation for new component patterns to ensure consistent implementation

## Current Risks
1. **Technical Debt**: Multiple developer contributions have led to inconsistent patterns
2. **Testing Gap**: Lack of comprehensive automated testing increases risk of regressions
3. **Dependency Management**: Recent upgrades may have introduced compatibility issues
4. **Architecture Limitations**: Pages Router constraints vs. modern Next.js features
5. **Integration Complexity**: Ensuring seamless operation across all three components
6. **Type Conversion**: ID fields between GraphQL and MongoDB require careful handling to prevent null/undefined errors
7. **WebSocket Stability**: Monitoring needed for WebSocket connection reliability

## Next Steps

### Comprehensive Documentation System
1. Begin populating the new documentation structure in the `/docs` directory
2. Create detailed feature documentation with Mermaid diagrams for key functionality
3. Document technical architecture comprehensively with visual representations
4. Maintain synchronization between Memory Bank and detailed documentation
5. Use the **update documentation** command to keep documentation current

### Clean Architecture Implementation
1. Review and refactor service boundaries for better separation of concerns
2. Extract domain logic from framework-specific code where possible
3. Implement more consistent error handling patterns across all services
4. Define clear interfaces between system components
5. Expand provider factory pattern to other appropriate services

### Monorepo Strategy
1. Establish consistent development workflows across services
2. Implement shared components and utilities in the packages directory
3. Standardize documentation approaches
4. Create unified build and testing pipelines

### Backend
1. Finalize and test the whole cask functionality
2. Explore the specific functionality of core modules
3. Analyze blockchain integration patterns more deeply
4. Document key business workflows
5. Ensure consistent ID handling across GraphQL resolvers and services
6. Continue monitoring for GraphQL non-nullable field errors
7. Implement automated tests for WholeCask creator field updates during transfers
8. Monitor logs for WholeCask creator updates to verify correct operation in production
9. Test WebSocket reconnection logic in production environments
10. Monitor performance difference between HTTP and WebSocket providers

### Admin CMS
1. Consider enhancing the whole-cask-issue-requests Show view with more detailed information
2. Add filtering and sorting options to the List view for improved usability
3. Implement any additional whole cask management features needed by admins
4. Ensure proper error handling for GraphQL operations

### Frontend
1. Identify and fix runtime errors in the application
2. Test and fix unused/untested features
3. Standardize component patterns and state management
4. Catalog areas needing refactoring
5. Continue implementing clean architecture patterns across key pages
6. Apply the marketplace modular component pattern to other complex pages
7. Complete whole cask component refactoring to ensure consistent prop passing pattern
8. Implement unit tests for the whole cask components and hooks
9. Consider further extracting common trading panel logic into shared components
10. Document the whole cask component architecture for future reference

### Smart Contracts
1. Continue monitoring for whole cask NFT minting activities
2. Implement additional inspection or monitoring tasks as needed
3. Explore deeper integration with GraphQL API for comprehensive NFT data retrieval

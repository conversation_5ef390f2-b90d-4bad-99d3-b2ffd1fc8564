{"name": "w", "version": "1.0.0", "description": "E2E tests for Jasmine project using Playwright", "type": "module", "scripts": {"test": "playwright test", "e2e": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "report": "playwright show-report", "install:browsers": "playwright install"}, "devDependencies": {"@playwright/test": "^1.42.1", "@types/node": "^20.11.30", "typescript": "^5.3.3"}, "engines": {"node": ">=18"}}
import { expect, test } from "@playwright/test";
import { HomePage } from "../src/pages/home-page";
import { waitForNetworkIdle } from "../src/utils/test-utils";

test.describe("Home Page Tests", () => {
  test("should load the home page", async ({ page }) => {
    const homePage = new HomePage(page);
    await homePage.goto();
    await homePage.waitForPageLoad();
  });

  // test("should have buttons on the page", async ({ page }) => {
  //   const homePage = new HomePage(page);
  //   await homePage.goto();
  //   await homePage.waitForPageLoad();
  //   await waitForNetworkIdle(page);

  //   // Check for common buttons that might exist on the home page
  //   // Note: You'll need to update these with actual button text from your application
  //   try {
  //     await homePage.expectButtonExists("Connect Wallet");
  //   } catch (e) {
  //     console.log(
  //       "Connect Wallet button not found, trying other common buttons"
  //     );
  //   }

  //   // Try to find any button on the page
  //   const anyButton = page.getByRole("button");
  //   const buttonCount = await anyButton.count();

  //   console.log(`Found ${buttonCount} buttons on the page`);
  //   expect(buttonCount).toBeGreaterThan(0);
  // });
});

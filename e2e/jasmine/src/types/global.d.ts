// Global type declarations for our tests

interface Window {
  // Mock functions for @telegram-apps/sdk
  isTMA: () => boolean;
  retrieveLaunchParams: () => {
    tgWebAppData: {
      user: {
        id: number;
        first_name: string;
        last_name: string;
        username: string;
        language_code: string;
        is_premium: boolean;
      };
      auth_date: number;
      hash: string;
      query_id: string;
    };
    startParam: string;
    initData: string;
  };

  // Mock state for wallet connection
  mockWalletConnected: boolean;
  mockWalletAddress: string;

  // Mock for @reown/appkit/react
  useAppKitAccount: () => {
    address: string | undefined;
    isConnected: boolean;
  };

  // Telegram WebApp global object
  Telegram: {
    WebApp: {
      initData: string;
      initDataUnsafe: {
        user: {
          id: number;
          first_name: string;
          last_name: string;
          username: string;
          language_code: string;
          is_premium: boolean;
        };
      };
      ready: () => void;
      expand: () => void;
      close: () => void;
    };
  };
}

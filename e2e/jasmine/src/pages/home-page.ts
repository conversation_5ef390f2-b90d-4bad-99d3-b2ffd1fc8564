import { Page, Locator, expect } from "@playwright/test";

export class HomePage {
  readonly page: Page;
  readonly url: string;

  constructor(page: Page) {
    this.page = page;
    this.url = "/";
  }

  async goto() {
    await this.page.goto(this.url);
  }

  async waitForPageLoad() {
    // Wait for the main content to be visible
    await this.page.waitForSelector("header", { state: "visible" });
    await expect(
      this.page.getByText("The Preferred Whisky Trading Partner")
    ).toBeVisible();
    await expect(this.page.getByText("Coming Soon")).toBeVisible();
  }

  // getButtonByText(text: string): Locator {
  //   return this.page.getByRole("button", { name: text });
  // }

  // async expectButtonExists(text: string) {
  //   const button = this.getButtonByText(text);
  //   await expect(button).toBeVisible();
  //   return button;
  // }
}

import { Page } from '@playwright/test';

/**
 * Waits for network requests to complete
 * @param page Playwright page object
 * @param timeout Timeout in milliseconds
 */
export async function waitForNetworkIdle(page: Page, timeout = 5000): Promise<void> {
  await page.waitForLoadState('networkidle', { timeout });
}

/**
 * Takes a screenshot and saves it with a timestamp
 * @param page Playwright page object
 * @param name Screenshot name
 */
export async function takeScreenshot(page: Page, name: string): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  await page.screenshot({ path: `./screenshots/${name}-${timestamp}.png`, fullPage: true });
}

/**
 * Scrolls to an element
 * @param page Playwright page object
 * @param selector Element selector
 */
export async function scrollToElement(page: Page, selector: string): Promise<void> {
  await page.evaluate((sel) => {
    const element = document.querySelector(sel);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, selector);
}

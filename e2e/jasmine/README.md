# Jasmine E2E Tests

This directory contains end-to-end tests for the Jasmine project using Playwright.

## Setup

1. Install dependencies:
   ```bash
   cd e2e/jasmine
   pnpm install
   ```

2. Install Playwright browsers:
   ```bash
   pnpm install:browsers
   ```

## Running Tests

Make sure the Jasmine application is running locally at http://localhost:3000 or update the `baseURL` in `playwright.config.ts`.

### Run all tests:
```bash
pnpm test
```

### Run tests with UI mode:
```bash
pnpm test:ui
```

### Run tests in headed mode (visible browser):
```bash
pnpm test:headed
```

### Debug tests:
```bash
pnpm test:debug
```

### View test report:
```bash
pnpm report
```

## Test Structure

- `tests/`: Contains all test files
- `src/pages/`: Page Object Models
- `src/utils/`: Utility functions

## Adding New Tests

1. Create page objects in `src/pages/` for new pages
2. Add test files in `tests/` directory
3. Run tests to verify

## Future Enhancements

- Authentication flows with Telegram and wallet
- More comprehensive test coverage
- CI/CD integration

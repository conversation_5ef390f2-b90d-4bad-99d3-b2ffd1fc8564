{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["ES2022", "DOM"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "./dist", "allowImportingTsExtensions": true, "noEmit": true}, "include": ["**/*.ts"], "exclude": ["node_modules"]}
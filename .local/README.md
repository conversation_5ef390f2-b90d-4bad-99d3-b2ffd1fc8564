# AI Editor Workspace (.local)

This folder serves as a local workspace for AI editor tools like Cursor, Augment, and other AI-assisted development environments. It stores implementation plans, work notes, session context, and temporary files that enhance AI-driven development workflows.

## Purpose

The `.local/` folder provides a structured workspace for:
- AI editor implementation plans and technical specifications
- Work session notes and progress tracking
- AI conversation history and context preservation
- Temporary files, drafts, and experimental code
- Reference materials and documentation links
- AI tool configurations and prompts

## Folder Structure

```
.local/
├── README.md                           # This file
├── plans/                             # Implementation plans and specs
│   ├── features/                      # Feature implementation plans
│   │   ├── marketplace-migration/     # Feature-specific planning
│   │   └── ecs-deployment/           # Infrastructure planning
│   ├── architecture/                 # System architecture documents
│   └── templates/                    # Plan templates and formats
├── sessions/                         # Work session tracking
│   ├── 2024-01/                     # Monthly session folders
│   │   ├── session-2024-01-15.md   # Daily session notes
│   │   └── progress-tracker.md      # Monthly progress summary
│   └── current/                     # Current active sessions
├── context/                         # AI conversation context
│   ├── conversations/               # Saved AI conversations
│   ├── prompts/                    # Reusable prompts and templates
│   └── knowledge-base/             # AI context and learning materials
├── drafts/                         # Temporary files and experiments
│   ├── code-snippets/              # Code experiments and drafts
│   ├── configs/                    # Configuration drafts
│   └── scratch/                    # Temporary workspace
├── references/                     # Documentation and links
│   ├── external-docs/              # External documentation links
│   ├── api-specs/                  # API specifications and schemas
│   └── best-practices/             # Development best practices
└── tools/                          # AI tool configurations
    ├── cursor/                     # Cursor-specific configs
    ├── augment/                    # Augment-specific configs
    └── shared/                     # Shared tool configurations
```

## Naming Conventions

### Files
- **Session notes**: `session-YYYY-MM-DD.md` (e.g., `session-2024-01-15.md`)
- **Implementation plans**: `plan-[feature-name].md` (e.g., `plan-marketplace-migration.md`)
- **Progress tracking**: `progress-[timeframe].md` (e.g., `progress-2024-q1.md`)
- **Conversation logs**: `conv-[topic]-YYYY-MM-DD.md` (e.g., `conv-ecs-migration-2024-01-15.md`)
- **Code drafts**: `draft-[component-name].ext` (e.g., `draft-payment-service.ts`)

### Folders
- Use kebab-case for folder names (e.g., `marketplace-migration`, `ecs-deployment`)
- Include dates for time-based organization (e.g., `2024-01`, `2024-q1`)
- Use descriptive names that indicate content type and scope

## Guidelines

### What TO Store
✅ **Implementation plans and technical specifications**
✅ **AI conversation history and context**
✅ **Work session notes and progress tracking**
✅ **Temporary code experiments and drafts**
✅ **AI tool configurations and custom prompts**
✅ **Reference links and documentation notes**
✅ **Architecture diagrams and planning documents**

### What NOT to Store
❌ **Sensitive information (API keys, passwords, secrets)**
❌ **Production code or final implementations**
❌ **Large binary files or media assets**
❌ **Generated files that can be recreated**
❌ **Personal information unrelated to the project**
❌ **Duplicate content already in version control**

## Usage Instructions

### Starting a New Feature
1. Create a feature folder in `plans/features/[feature-name]/`
2. Use the plan template from `plans/templates/`
3. Document initial AI conversations in `context/conversations/`
4. Track daily progress in `sessions/current/`

### Daily Workflow
1. Start session notes in `sessions/current/session-YYYY-MM-DD.md`
2. Save important AI conversations in `context/conversations/`
3. Store code experiments in `drafts/code-snippets/`
4. Update progress tracking regularly

### AI Context Management
1. Save valuable prompts in `context/prompts/`
2. Document AI learning and insights in `context/knowledge-base/`
3. Maintain conversation history for complex features
4. Use context files to maintain AI session continuity

## Best Practices

### Organization
- **Regular cleanup**: Archive old sessions monthly
- **Consistent naming**: Follow established naming conventions
- **Logical grouping**: Group related files in appropriate folders
- **Clear documentation**: Use descriptive filenames and folder structures

### AI Workflow Enhancement
- **Context preservation**: Save important AI conversations for future reference
- **Prompt reuse**: Build a library of effective prompts and templates
- **Progress tracking**: Maintain clear records of implementation progress
- **Knowledge building**: Document AI insights and learning for team benefit

### File Management
- **Size limits**: Keep individual files under 1MB
- **Regular archiving**: Move completed work to archive folders
- **Backup important work**: Copy critical plans to project documentation
- **Clean workspace**: Remove outdated drafts and temporary files

## Integration with AI Editors

### Cursor
- Store Cursor-specific configurations in `tools/cursor/`
- Use `.cursorrules` files for project-specific AI behavior
- Maintain conversation context in `context/conversations/`

### Augment
- Configure Augment settings in `tools/augment/`
- Use knowledge base files for context enhancement
- Store implementation plans for Augment's planning features

### General AI Tools
- Maintain tool-agnostic formats (Markdown, JSON)
- Use shared configurations in `tools/shared/`
- Document tool-specific workflows and best practices

## Maintenance

### Weekly
- Review and organize session notes
- Clean up temporary files in `drafts/scratch/`
- Update progress tracking documents

### Monthly
- Archive completed sessions to dated folders
- Review and update AI prompts and templates
- Clean up outdated reference materials

### Quarterly
- Archive old implementation plans
- Review and update folder structure if needed
- Document lessons learned and best practices

---

**Note**: This folder is excluded from version control via `.gitignore` to maintain it as a local workspace. Important plans and documentation should be copied to the main project documentation when ready for team sharing.

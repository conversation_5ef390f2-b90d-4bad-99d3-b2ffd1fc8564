# Pool Processor Background Job E2E Test Cases

## Overview

This document defines comprehensive End-to-End test cases for the Pool Processor background job flow using Vitest with real MongoDB database connections. The tests focus on queue management, FIFO processing, user selection, payment timeout handling, and pool state transitions.

## Test Framework Configuration

### Vitest Setup
- **Framework**: Vitest E2E testing with real MongoDB connections
- **Environment**: SIT (System Integration Testing) environment
- **Database**: Real MongoDB database with seeding and cleanup utilities
- **Test Timeout**: 30 seconds per test
- **Server**: Real dcasks-backend server running on port 9000

### Test File Structure
```
test/e2e/pool-processor.e2e.spec.ts
```

### Database Setup Requirements
```bash
# Before tests: Seed database with test data
yarn db:seed

# After tests: Clean up database
yarn db:cleanup

# Complete workflow
yarn test:e2e:full
```

## Test Organization

Tests are organized into functional groups following the Given/When/Then pattern:

1. **Queue Management Tests** - Basic queue operations and FIFO processing
2. **Pool State Transition Tests** - User status changes and pool management
3. **Payment Timeout Handling Tests** - Payment expiration and cleanup
4. **Error Scenario Tests** - Edge cases and error conditions
5. **Performance Tests** - Load testing and concurrent operations

## Test Cases

### 1. Queue Management Tests (Organized by User Stories)

#### Tests for US-Q001: Join Sale Queue
*User Story: As a user with a crypto wallet, I want to join the NFT sale queue by connecting my wallet*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given user with valid wallet connection, when user joins queue, then queue session is created with proper timeout` | Verify queue session creation with expiration timeout using timeout constants | Queue session created with proper heartbeat timeout configuration | US-Q001 |
| `Given user joins queue successfully, when queue position is assigned, then user receives sequential position number` | Verify proper queue position assignment in FIFO order | User assigned next available position (e.g., position 5 if 4 users ahead) | US-Q001 |
| `Given user attempts to join queue with same wallet address twice, when second join attempt is made, then duplicate is prevented` | Verify duplicate prevention mechanism for same wallet address | Second join attempt rejected, user remains at original position | US-Q001 |
| `Given queue has 1000 users already, when new user joins queue, then user is added successfully` | Verify unlimited queue capacity (no maximum queue size limit) | User successfully added as position 1001, no capacity restrictions | US-Q001 |
| `Given user joins queue, when session is created, then heartbeat timeout is set to WAITING_USER_HEARTBEAT_TIMEOUT_MS` | Verify queue session uses correct timeout constant for waiting users | Session configured with 60-second heartbeat timeout for waiting status | US-Q001 |
| `Given user with invalid wallet signature, when user attempts to join queue, then join is rejected` | Verify wallet signature validation during queue join process | Join attempt rejected, no queue session created, user receives error message | US-Q001 |
| `Given sale is paused, when user attempts to join queue, then join is prevented` | Verify queue join respects sale configuration status | Join attempt blocked, user notified that sale is currently paused | US-Q001 |
| `Given user joins queue successfully, when initial heartbeat is set, then timestamp is recorded for timeout monitoring` | Verify initial heartbeat timestamp is properly set for Pool Processor monitoring | Initial heartbeat timestamp recorded, eligible for WAITING_USER_HEARTBEAT_TIMEOUT_MS monitoring | US-Q001 |

#### Tests for US-Q002: Monitor Queue Position
*User Story: As a queued user, I want to see my current position and estimated wait time in real-time*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given queue has 5 waiting users with valid heartbeats, when pool processor runs, then first user is selected for purchase` | Verify FIFO queue processing moves first waiting user to active pool after heartbeat validation | First user status changes from WAITING to ACTIVE | US-Q002 |
| `Given active pool is at capacity (10 users), when pool processor runs, then no new users are moved from queue` | Verify pool capacity limits are respected after heartbeat monitoring | Queue users remain in WAITING status | US-Q002 |
| `Given queue has 10 users with valid heartbeats and pool has space for 5, when pool processor runs, then exactly 5 users are moved to active pool` | Verify batch processing respects available pool capacity after heartbeat cleanup | Exactly 5 users moved to ACTIVE status | US-Q002 |

#### Tests for US-Q003: Enter Active Pool
*User Story: As a queued user with valid heartbeat, I want to be automatically moved to the active pool when it's my turn*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given queue with valid heartbeats after cleanup, when pool processor continues to queue filling, then FIFO order is maintained` | Verify queue filling respects FIFO order after heartbeat-based removals | Remaining users promoted in correct chronological order | US-Q003 |
| `Given waiting users with expired heartbeats and active pool with space, when pool processor runs, then expired users are not promoted` | Verify waiting user heartbeat monitoring prevents expired users from entering active pool | Only users with valid heartbeats eligible for promotion | US-Q003 |
| `Given queue advancement after heartbeat removals creates available pool space, when pool processor runs, then valid users are promoted` | Verify queue advancement after heartbeat-based removals | Valid waiting users promoted to fill available pool space | US-Q003 |

#### Tests for US-Q004: Handle Waiting User Heartbeat Timeout
*User Story: As a user waiting in queue who stops sending heartbeats, I want to be automatically removed from the queue after 1 minute of inactivity*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given waiting user with heartbeat at 59 seconds ago, when pool processor runs with WAITING_USER_HEARTBEAT_TIMEOUT_MS, then user remains in queue` | Verify WAITING_USER_HEARTBEAT_TIMEOUT_MS (60,000ms) boundary condition - user just within threshold | User stays WAITING, eligible for pool promotion | US-Q004 |
| `Given waiting user with heartbeat at 61 seconds ago, when pool processor runs with WAITING_USER_HEARTBEAT_TIMEOUT_MS, then user is expired` | Verify WAITING_USER_HEARTBEAT_TIMEOUT_MS (60,000ms) boundary condition - user just over threshold | User marked as EXPIRED, removed from queue | US-Q004 |
| `Given 10 waiting users with heartbeats from 30-120 seconds ago, when pool processor runs, then only users within WAITING_USER_HEARTBEAT_TIMEOUT_MS remain` | Verify selective removal based on 60-second threshold | Users with heartbeats ≤60s remain, users with heartbeats >60s expired | US-Q004 |
| `Given waiting user sends heartbeat during monitoring phase, when pool processor runs, then updated heartbeat is respected` | Verify real-time heartbeat updates during waiting user monitoring | User with updated heartbeat preserved if within threshold | US-Q004 |
| `Given all waiting users have expired heartbeats, when pool processor runs, then queue is completely cleared` | Verify batch removal when all waiting users exceed WAITING_USER_HEARTBEAT_TIMEOUT_MS | All waiting users marked as EXPIRED, queue becomes empty | US-Q004 |
| `Given waiting users with staggered heartbeat expiration times, when pool processor runs, then users are expired based on individual timestamps` | Verify individual heartbeat evaluation rather than batch timeout | Each user evaluated against their specific heartbeat timestamp | US-Q004 |
| `Given waiting user heartbeat exactly at WAITING_USER_HEARTBEAT_TIMEOUT_MS threshold, when pool processor runs multiple times, then consistent behavior occurs` | Verify deterministic behavior at exact timeout boundary | Consistent expiration behavior across multiple processor runs | US-Q004 |

#### Tests for US-Q005: Experience Queue Position Recalculation
*User Story: As a user waiting in queue when other users are removed due to timeouts, I want to see my position automatically update to reflect the new queue order*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given queue positions 1-10 with user 3 having expired heartbeat, when pool processor runs, then positions 4-10 are decremented by 1` | Verify updateQueuePositions() after single user removal | Users 4-10 become positions 3-9, maintaining sequential order | US-Q005 |
| `Given queue positions 1-10 with users 2, 5, 8 having expired heartbeats, when pool processor runs, then remaining users get sequential positions 1-7` | Verify position recalculation after multiple non-consecutive removals | Remaining 7 users assigned positions 1, 2, 3, 4, 5, 6, 7 sequentially | US-Q005 |
| `Given queue with expired users at beginning (positions 1-3), when pool processor runs, then remaining users move to front of queue` | Verify position recalculation when queue head users are removed | Users previously at positions 4+ become new positions 1+ | US-Q005 |
| `Given queue with expired users at end (positions 8-10), when pool processor runs, then front users maintain positions` | Verify position recalculation when queue tail users are removed | Users at positions 1-7 maintain their positions, queue size reduced | US-Q005 |
| `Given empty queue after all users expire, when updateQueuePositions runs, then operation completes without errors` | Verify graceful handling of empty queue during position updates | No errors, method completes successfully with empty result set | US-Q005 |
| `Given queue with users having identical heartbeat timestamps, when pool processor runs, then FIFO order is preserved for remaining users` | Verify FIFO order preservation when multiple users have same heartbeat status | Users with same heartbeat status processed in join order | US-Q005 |

#### Tests for US-Q006: Handle Active Pool Heartbeat Timeout
*User Story: As a user in the active pool who stops sending heartbeats, I want to be automatically removed from the active pool after 5 minutes of inactivity*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given active user with heartbeat at 299 seconds ago, when pool processor runs with ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, then user remains active` | Verify ACTIVE_USER_HEARTBEAT_TIMEOUT_MS (300,000ms) boundary condition - user just within threshold | User stays ACTIVE in pool | US-Q006 |
| `Given active user with heartbeat at 301 seconds ago, when pool processor runs with ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, then user is expired` | Verify ACTIVE_USER_HEARTBEAT_TIMEOUT_MS (300,000ms) boundary condition - user just over threshold | User marked as EXPIRED, removed from pool | US-Q006 |
| `Given user in active pool with heartbeat timeout using ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, when pool processor runs, then user is marked as expired` | Verify heartbeat timeout detection using 300-second threshold | User status changes from ACTIVE to EXPIRED | US-Q006 |

#### Tests for US-Q007: Experience Queue Advancement After Timeout Cleanup
*User Story: As a user waiting in queue when timeout cleanup occurs, I want to be automatically advanced when space becomes available in the active pool*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given queue processing when all waiting users have expired heartbeats and active pool is empty, when pool processor runs, then pool remains empty` | Verify graceful handling when no valid users exist for promotion | Pool remains empty, no errors, statistics updated correctly | US-Q007 |

#### Edge Cases and Boundary Conditions Tests (Cross-User Story Validation)
*These tests validate complex scenarios that span multiple user stories*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given queue with mixed valid and expired heartbeats and active pool at capacity, when pool processor runs, then no promotions occur` | Verify capacity limits respected even after heartbeat cleanup | Expired users removed, valid users remain in queue due to capacity | US-Q002, US-Q004 |

### 2. Pool State Transition Tests (Related to NFT Selection User Stories)

#### Tests for NFT Selection and Pool Management
*These tests relate to user stories US-N001, US-N002, US-N003 from the NFT Selection Stories section*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given user completes NFT selection, when pool processor runs, then user is removed from active pool` | Verify completed users are removed from pool tracking | User removed from active pool, pool capacity updated | US-N002 |
| `Given multiple users with different statuses, when pool processor runs, then each user is processed according to their status` | Verify correct status-based processing logic | Each user processed correctly based on current status | US-N001, US-N002 |
| `Given user session expires during active pool processing, when pool processor runs, then session is cleaned up and pool rebalanced` | Verify session cleanup and pool rebalancing | Session removed, pool statistics updated | US-N003 |

### 3. Payment Timeout Handling Tests (Related to Payment Processing User Stories)

#### Tests for Payment Processing and Timeout Management
*These tests relate to user stories US-P001, US-P002, US-P003, US-P004, US-P005 from the Payment Processing Stories section*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given user with payment timeout, when pool processor runs, then user is removed and next user is selected` | Verify payment timeout triggers user removal and queue advancement | Timed-out user removed, next user moved to ACTIVE | US-P005 |
| `Given multiple users with payment timeouts, when pool processor runs, then all timed-out users are processed` | Verify batch processing of multiple payment timeouts | All timed-out users removed, pool refilled from queue | US-P005 |
| `Given user payment expires during pool processing, when pool processor runs, then NFT lock is released` | Verify NFT lock release for payment timeouts | NFT status changes from LOCKED to AVAILABLE | US-P005 |
| `Given payment timeout with related order, when pool processor runs, then order status is updated to expired` | Verify order status updates for payment timeouts | Order status changes to EXPIRED | US-P005 |

### 4. Error Scenario Tests (Cross-Functional Error Handling)

#### System Resilience and Error Recovery Tests
*These tests validate error handling across all user story scenarios*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given database connection error during heartbeat monitoring, when pool processor runs, then error is logged and processing continues` | Verify graceful error handling for database issues during timeout checks | Error logged, no system crash, other operations continue | US-Q004, US-Q006, US-Q007 |
| `Given corrupted session data with invalid heartbeat timestamps, when pool processor runs, then corrupted sessions are cleaned up` | Verify handling of invalid session data during heartbeat validation | Corrupted sessions removed, valid sessions processed | US-Q004, US-Q009 |
| `Given sale configuration is paused, when pool processor runs, then no heartbeat monitoring or queue processing occurs` | Verify respect for sale configuration settings | No users moved, no heartbeat checks, queue remains unchanged | US-A001 (Admin Stories) |
| `Given invalid pool tracking data during position updates, when pool processor runs, then pool tracking is reset and rebuilt` | Verify recovery from corrupted pool tracking during queue management | Pool tracking reset, rebuilt from current sessions | US-Q005 |
| `Given concurrent pool processor executions with heartbeat timeouts, when multiple instances run, then no duplicate processing occurs` | Verify thread safety and concurrent execution handling with timeout constants | No duplicate user processing, consistent state, proper timeout handling | US-Q010 |

### 5. Performance Tests (User Experience Validation)

#### Performance and Scalability Tests
*These tests ensure user stories perform well under load and meet user experience expectations*

| Test Name | Description | Expected Outcome | User Story Reference |
|-----------|-------------|------------------|---------------------|
| `Given 1000 waiting users with mixed heartbeat statuses, when pool processor runs, then heartbeat monitoring completes within 2 seconds` | Verify performance of WAITING_USER_HEARTBEAT_TIMEOUT_MS checks with large queue | Heartbeat processing completes within time limit | US-Q004, US-Q002 |
| `Given 100 active users with mixed heartbeat statuses, when pool processor runs, then ACTIVE_USER_HEARTBEAT_TIMEOUT_MS validation completes efficiently` | Verify efficient processing of active pool heartbeat checks | All heartbeat validations processed within performance threshold | US-Q006, US-Q007 |
| `Given rapid heartbeat updates during timeout processing, when pool processor runs, then no race conditions occur with named constants` | Verify handling of concurrent heartbeat updates with timeout constants | Consistent state, no data corruption, proper timeout application | US-Q011 |
| `Given queue position updates for 500 remaining users after mass expiration, when pool processor runs, then updateQueuePositions() completes within 1 second` | Verify performance of position recalculation with large datasets | Position updates complete within performance threshold | US-Q005 |

## User Story to Test Case Traceability Matrix

### Queue Management User Stories Coverage

| User Story ID | User Story Title | Test Cases Count | Coverage Status |
|---------------|------------------|------------------|-----------------|
| US-Q001 | Join Sale Queue | 8 | ✅ Complete |
| US-Q002 | Monitor Queue Position | 3 | ✅ Complete |
| US-Q003 | Enter Active Pool | 3 | ✅ Complete |
| US-Q004 | Handle Waiting User Heartbeat Timeout | 7 | ✅ Complete |
| US-Q005 | Experience Queue Position Recalculation | 6 | ✅ Complete |
| US-Q006 | Handle Active Pool Heartbeat Timeout | 3 | ✅ Complete |
| US-Q007 | Experience Queue Advancement After Timeout Cleanup | 1 | ✅ Complete |

### Cross-Reference: Test Cases by User Story

#### US-Q001: Join Sale Queue
- `Given user with valid wallet connection, when user joins queue, then queue session is created with proper timeout`
- `Given user joins queue successfully, when queue position is assigned, then user receives sequential position number`
- `Given user attempts to join queue with same wallet address twice, when second join attempt is made, then duplicate is prevented`
- `Given queue has 1000 users already, when new user joins queue, then user is added successfully`
- `Given user joins queue, when session is created, then heartbeat timeout is set to WAITING_USER_HEARTBEAT_TIMEOUT_MS`
- `Given user with invalid wallet signature, when user attempts to join queue, then join is rejected`
- `Given sale is paused, when user attempts to join queue, then join is prevented`
- `Given user joins queue successfully, when initial heartbeat is set, then timestamp is recorded for timeout monitoring`

#### US-Q002: Monitor Queue Position
- `Given queue has 5 waiting users with valid heartbeats, when pool processor runs, then first user is selected for purchase`
- `Given active pool is at capacity (10 users), when pool processor runs, then no new users are moved from queue`
- `Given queue has 10 users with valid heartbeats and pool has space for 5, when pool processor runs, then exactly 5 users are moved to active pool`

#### US-Q003: Enter Active Pool
- `Given queue with valid heartbeats after cleanup, when pool processor continues to queue filling, then FIFO order is maintained`
- `Given waiting users with expired heartbeats and active pool with space, when pool processor runs, then expired users are not promoted`
- `Given queue advancement after heartbeat removals creates available pool space, when pool processor runs, then valid users are promoted`

#### US-Q004: Handle Waiting User Heartbeat Timeout
- `Given waiting user with heartbeat at 59 seconds ago, when pool processor runs with WAITING_USER_HEARTBEAT_TIMEOUT_MS, then user remains in queue`
- `Given waiting user with heartbeat at 61 seconds ago, when pool processor runs with WAITING_USER_HEARTBEAT_TIMEOUT_MS, then user is expired`
- `Given 10 waiting users with heartbeats from 30-120 seconds ago, when pool processor runs, then only users within WAITING_USER_HEARTBEAT_TIMEOUT_MS remain`
- `Given waiting user sends heartbeat during monitoring phase, when pool processor runs, then updated heartbeat is respected`
- `Given all waiting users have expired heartbeats, when pool processor runs, then queue is completely cleared`
- `Given waiting users with staggered heartbeat expiration times, when pool processor runs, then users are expired based on individual timestamps`
- `Given waiting user heartbeat exactly at WAITING_USER_HEARTBEAT_TIMEOUT_MS threshold, when pool processor runs multiple times, then consistent behavior occurs`

#### US-Q005: Experience Queue Position Recalculation
- `Given queue positions 1-10 with user 3 having expired heartbeat, when pool processor runs, then positions 4-10 are decremented by 1`
- `Given queue positions 1-10 with users 2, 5, 8 having expired heartbeats, when pool processor runs, then remaining users get sequential positions 1-7`
- `Given queue with expired users at beginning (positions 1-3), when pool processor runs, then remaining users move to front of queue`
- `Given queue with expired users at end (positions 8-10), when pool processor runs, then front users maintain positions`
- `Given empty queue after all users expire, when updateQueuePositions runs, then operation completes without errors`
- `Given queue with users having identical heartbeat timestamps, when pool processor runs, then FIFO order is preserved for remaining users`

#### US-Q006: Handle Active Pool Heartbeat Timeout
- `Given active user with heartbeat at 299 seconds ago, when pool processor runs with ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, then user remains active`
- `Given active user with heartbeat at 301 seconds ago, when pool processor runs with ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, then user is expired`
- `Given user in active pool with heartbeat timeout using ACTIVE_USER_HEARTBEAT_TIMEOUT_MS, when pool processor runs, then user is marked as expired`

#### US-Q007: Experience Queue Advancement After Timeout Cleanup
- `Given queue processing when all waiting users have expired heartbeats and active pool is empty, when pool processor runs, then pool remains empty`

### Coverage Summary
- **Total User Stories Covered**: 7 out of 7 Queue Management Stories (100%)
- **Total Test Cases**: 31 test cases covering queue management functionality
- **Queue Join Tests**: 8 test cases for queue entry and session creation
- **Boundary Condition Tests**: 8 test cases for timeout constant boundaries
- **Edge Case Tests**: 6 test cases for complex scenarios
- **Performance Tests**: 4 test cases for scalability validation
- **Error Handling Tests**: 5 test cases for resilience validation

## Test Implementation Structure

### Test File Structure
```
test/e2e/pool-processor.e2e.spec.ts
```

### Test Organization Framework
- **Test Groups**: Organized by functional areas (Queue Management, Pool State Transitions, etc.)
- **Naming Convention**: Given/When/Then pattern for descriptive test names
- **Test Isolation**: Each test runs with fresh database state
- **Environment**: SIT environment with real MongoDB connections

### Database Seeding Requirements

For each test, the following data should be seeded:

1. **Sale Configuration**: Active sale with proper queue and pool limits
2. **Queue Sessions**: Test users in various states (WAITING, ACTIVE, PAYMENT, EXPIRED)
   - WAITING users with different heartbeat timestamps (recent, 30s ago, 1min+ ago)
   - ACTIVE users with different heartbeat timestamps (recent, 3min ago, 5min+ ago)
3. **Active Pool Tracking**: Current pool statistics and capacity tracking
4. **NFT Inventory**: Available NFTs for selection and locking
5. **Orders**: Test orders in various payment states
6. **Payment Records**: Test payments with different timeout scenarios

### Test Data Cleanup

After each test:
1. Remove all test queue sessions
2. Reset active pool tracking
3. Unlock any test NFT locks
4. Remove test orders and payment records
5. Reset sale configuration to default test state

## Integration with Existing E2E Framework

These tests will integrate with the existing E2E testing framework:

- **Database Scripts**: Use existing `yarn db:seed` and `yarn db:cleanup` commands
- **Test Environment**: Run in SIT environment with real MongoDB connections
- **Test Execution**: Use `yarn test:e2e:full` workflow for complete testing
- **Error Handling**: Follow existing error logging and reporting patterns

## Test Implementation Requirements

### Required Helper Functions
- **Database Operations**: Functions to seed queue users, set heartbeat timestamps for different user statuses, create payment timeouts
- **Heartbeat Management**:
  * `setWaitingUserHeartbeat(sessionId, secondsAgo)` - Set heartbeat relative to WAITING_USER_HEARTBEAT_TIMEOUT_MS (60 seconds)
  * `setActiveUserHeartbeat(sessionId, secondsAgo)` - Set heartbeat relative to ACTIVE_USER_HEARTBEAT_TIMEOUT_MS (300 seconds)
  * `setCleanupHeartbeat(sessionId, secondsAgo)` - Set heartbeat relative to CLEANUP_HEARTBEAT_TIMEOUT_MS (600 seconds)
  * `createExpiredWaitingUser(sessionId)` - Create user with heartbeat > 60 seconds ago
  * `createValidWaitingUser(sessionId)` - Create user with heartbeat < 60 seconds ago
  * `createExpiredActiveUser(sessionId)` - Create user with heartbeat > 300 seconds ago
  * `createValidActiveUser(sessionId)` - Create user with heartbeat < 300 seconds ago
- **Queue Management**:
  * `verifyQueuePositions(expectedPositions)` - Verify queue positions match expected sequential order
  * `checkQueueIntegrity()` - Verify no duplicate positions, all positions sequential
  * `validateQueueAfterRemovals(removedUsers, remainingUsers)` - Verify position recalculation
  * `getQueuePositionMapping()` - Get current user-to-position mapping
- **Pool Management**: Functions to trigger pool processor, get pool statistics, manage pool capacity
- **Timeout Validation**:
  * `verifyTimeoutConstantApplication(timeoutType, expectedThreshold)` - Verify correct timeout constant usage
  * `validateBoundaryConditions(timeoutType, boundaryValues)` - Test exact threshold boundaries
- **Assertions**: Functions to verify user status, queue positions, pool capacity, NFT lock status, timeout compliance
- **Test Data**: Functions to generate wallet addresses, create JWT tokens, manage session IDs with precise timestamp control

### Test Data Patterns
- **Standard Test Users**: Consistent structure for wallet address, status, timestamps, session IDs
- **Pool Configuration**: Test-specific settings for pool size, timeout constants, sale status
- **Queue Scenarios**: Various queue states with different user counts and statuses
- **Timeout Scenarios**:
  * **WAITING Users** (WAITING_USER_HEARTBEAT_TIMEOUT_MS = 60,000ms):
    - Valid: 30s, 45s, 59s ago
    - Boundary: exactly 60s ago
    - Expired: 61s, 90s, 120s ago
  * **ACTIVE Users** (ACTIVE_USER_HEARTBEAT_TIMEOUT_MS = 300,000ms):
    - Valid: 2min, 4min, 299s ago
    - Boundary: exactly 300s ago
    - Expired: 301s, 6min, 8min ago
  * **PAYMENT Users** (ACTIVE_USER_HEARTBEAT_TIMEOUT_MS = 300,000ms):
    - Valid: 3min, 4.5min, 299s ago
    - Boundary: exactly 300s ago
    - Expired: 301s, 5.5min, 7min ago
  * **Cleanup Scenarios** (CLEANUP_HEARTBEAT_TIMEOUT_MS = 600,000ms):
    - Valid: 8min, 9.5min, 599s ago
    - Boundary: exactly 600s ago
    - Expired: 601s, 12min, 15min ago
- **Queue Position Scenarios**:
  * Sequential positions 1-10 with various heartbeat statuses
  * Non-consecutive removals (positions 2, 5, 8)
  * Head removals (positions 1-3)
  * Tail removals (positions 8-10)
  * Complete queue expiration scenarios

## Success Criteria

- All tests pass consistently in SIT environment
- Tests complete within reasonable time limits (< 8 minutes total including enhanced queue management tests)
- Database cleanup is thorough and reliable
- Tests provide comprehensive coverage of Pool Processor functionality including:
  * **Named Timeout Constants**: WAITING_USER_HEARTBEAT_TIMEOUT_MS (60,000ms), ACTIVE_USER_HEARTBEAT_TIMEOUT_MS (300,000ms), CLEANUP_HEARTBEAT_TIMEOUT_MS (600,000ms)
  * **Enhanced Processing Flow**: Waiting user monitoring → active pool processing → queue filling → cleanup
  * **Heartbeat Monitoring**: Both WAITING and ACTIVE user heartbeat validation
  * **Queue Position Management**: updateQueuePositions() functionality and integrity
  * **Boundary Conditions**: Exact timeout threshold testing and edge cases
- Error scenarios are properly handled and logged with timeout constant usage
- Performance benchmarks are met consistently including:
  * Heartbeat monitoring for large user sets (1000+ waiting users)
  * Queue position recalculation efficiency (500+ users)
  * Concurrent operation handling with timeout constants
- Test data isolation prevents cross-test contamination
- Timeout constant boundaries are precisely validated (±1 second accuracy)
- Queue integrity is maintained after position updates (no duplicate positions, sequential order)
- Processing flow order is consistently maintained across all test scenarios

# NFT Endpoint Consolidation

## Overview

This document describes the consolidation of NFT querying functionality into a single, more flexible endpoint that handles both individual NFT lookups and filtered NFT listings based on query parameters.

## Changes Made

### 1. Removed Standalone Endpoint

**Removed:**
- `GET /api/v1/collaborations/nfts/available`

**Rationale:** This endpoint provided limited functionality and created API fragmentation. The same functionality is now available through the enhanced general NFT endpoint with better filtering capabilities.

### 2. Enhanced General NFT Endpoint

**Enhanced Endpoint:** `GET /api/v1/collaborations/nfts`

**New Query Parameters:**
- `status` - Filter by NFT status (AVAILABLE, LOCKED, SOLD, etc.)
- `minPrice` - Minimum price filter (number)
- `maxPrice` - Maximum price filter (number)
- `limit` - Number of results to return (default: 50, max: 100)
- `offset` - Number of results to skip for pagination (default: 0)
- `isActive` - Filter by active status (default: true)

**Response Format:**
```json
{
  "nfts": [
    {
      "tokenId": "1001",
      "name": "Sailing Whisky NFT #1",
      "price": 110,
      "status": "AVAILABLE",
      "isAvailable": true,
      // ... other NFT properties
    }
  ],
  "totalAvailable": 50,
  "totalInCollection": 100,
  "currentlyLocked": 5,
  "sold": 45,
  "pagination": {
    "limit": 50,
    "offset": 0,
    "total": 50,
    "hasMore": false
  }
}
```

### 3. Maintained Single NFT Endpoint

**Unchanged:** `GET /api/v1/collaborations/nfts/:tokenId`

**Response Format:**
```json
{
  "nft": {
    "tokenId": "1001",
    "name": "Sailing Whisky NFT #1",
    "price": 110,
    "status": "AVAILABLE",
    // ... other NFT properties
  },
  "isAvailable": true,
  "unavailableReason": null,
  "availableAt": null
}
```

## Usage Examples

### Get Available NFTs (Replaces /available endpoint)
```bash
GET /api/v1/collaborations/nfts?status=AVAILABLE&limit=10
```

### Get NFTs in Price Range
```bash
GET /api/v1/collaborations/nfts?minPrice=100&maxPrice=500&limit=20
```

### Get All NFTs with Pagination
```bash
GET /api/v1/collaborations/nfts?limit=25&offset=50
```

### Get Specific NFT Details
```bash
GET /api/v1/collaborations/nfts/1001
```

## Technical Implementation

### 1. New DTOs

**NFTQueryResponseDto:**
- Handles both single NFT and multiple NFT responses
- Includes pagination information for listings
- Provides collection statistics

**NFTQueryParamsDto:**
- Validates query parameters
- Handles type conversion and sanitization
- Enforces limits and constraints

### 2. Service Layer Updates

**Consolidated Query Method:**
```typescript
async queryNFTs(
  tokenId?: string,
  queryParams?: NFTQueryParamsDto,
): Promise<NFTQueryResponseDto>
```

**Features:**
- Single method handles both use cases
- Robust parameter validation
- Efficient database queries with proper indexing
- Automatic status filtering (e.g., expired locks treated as available)

### 3. Controller Updates

**Parameter Validation:**
- Sanitizes numeric parameters to prevent NaN errors
- Enforces reasonable limits (max 100 results per request)
- Provides clear error messages for invalid inputs

**Error Handling:**
- `400 Bad Request` for invalid parameters
- `404 Not Found` for non-existent NFTs
- `500 Internal Server Error` with proper logging

## Migration Guide

### For Frontend Developers

**Before (Old /available endpoint):**
```javascript
// Get available NFTs
const response = await fetch('/api/v1/collaborations/nfts/available');
const data = await response.json();
console.log(data.nfts); // Array of available NFTs
```

**After (New consolidated endpoint):**
```javascript
// Get available NFTs with same functionality
const response = await fetch('/api/v1/collaborations/nfts?status=AVAILABLE');
const data = await response.json();
console.log(data.nfts); // Array of available NFTs
console.log(data.pagination); // Additional pagination info
console.log(data.totalAvailable); // Collection statistics
```

**Enhanced Capabilities:**
```javascript
// Get NFTs with price filtering
const response = await fetch('/api/v1/collaborations/nfts?status=AVAILABLE&minPrice=100&maxPrice=500&limit=10');

// Get NFTs with pagination
const response = await fetch('/api/v1/collaborations/nfts?limit=20&offset=40');
```

### For Backend Integration

**No Breaking Changes:**
- Single NFT endpoint (`/nfts/:tokenId`) unchanged
- All existing queue and order management APIs unchanged
- Response formats maintain backward compatibility where applicable

**Internal Service Updates:**
- Consolidated query logic in NFT service
- Enhanced parameter validation
- Improved error handling and logging

## Benefits Achieved

### 1. API Simplification
- **Reduced Endpoints:** One endpoint handles multiple use cases
- **Consistent Interface:** Single pattern for NFT querying
- **Better Documentation:** Fewer endpoints to document and maintain

### 2. Enhanced Functionality
- **Flexible Filtering:** Multiple filter options (status, price, active state)
- **Pagination Support:** Efficient handling of large NFT collections
- **Collection Statistics:** Additional metadata about NFT collection state
- **Parameter Validation:** Robust input sanitization and error handling

### 3. Performance Improvements
- **Efficient Queries:** Optimized database queries with proper filtering
- **Reduced API Calls:** Single request can replace multiple calls
- **Better Caching:** Consolidated responses easier to cache

### 4. Developer Experience
- **Cleaner Integration:** Fewer endpoints to learn and integrate
- **Better Error Messages:** Clear feedback on parameter issues
- **Enhanced Response Data:** More useful information in responses

## Testing Verification

**Endpoint Testing:**
- ✅ Consolidated endpoint returns correct filtered results
- ✅ Single NFT endpoint unchanged and working
- ✅ Old `/available` endpoint properly removed (returns 404)
- ✅ Parameter validation prevents database errors
- ✅ Price filtering works correctly
- ✅ Pagination functions as expected

**Integration Testing:**
- ✅ No impact on existing queue management
- ✅ No impact on order creation process
- ✅ All E2E tests continue to pass

## Future Enhancements

**Potential Additions:**
1. **Additional Filters:** Collection address, attribute-based filtering
2. **Sorting Options:** Sort by price, creation date, token ID
3. **Search Functionality:** Text search in NFT names/descriptions
4. **Bulk Operations:** Batch availability checking

**Performance Optimizations:**
1. **Response Caching:** Cache frequently requested filter combinations
2. **Database Indexing:** Optimize indexes for common query patterns
3. **Lazy Loading:** Stream large result sets for better performance

---

*This consolidation provides a cleaner, more flexible API while maintaining full backward compatibility for critical endpoints and improving the overall developer experience.*

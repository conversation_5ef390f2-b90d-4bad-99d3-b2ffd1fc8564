# System Architecture v2: Technical Service Architecture
## Sailing Whisky NFT Sale - Service-Oriented Design

This document presents the technical service architecture for the Sailing Whisky NFT Sale system, organized by technical layers and service boundaries rather than project epics.

## Architecture Overview

The system is organized into distinct technical layers with clear service boundaries and communication patterns. This architecture focuses on service dependencies, data flow, and technical integration points.

### Technical Layers
- **Frontend Layer**: User-facing web components (Capcom website)
- **Backend Services Layer**: Core business logic and API services
- **Data Layer**: Database and persistent storage
- **External Integration Layer**: Third-party payment and blockchain services

## System Architecture Diagram v2

```mermaid
graph TB
    %% User
    User[👤 User with Crypto Wallet]
    
    %% Frontend Layer
    subgraph "Frontend Layer - Capcom Website"
        LandingPage[🏠 Landing Page<br/>- Sale countdown & info<br/>- Wallet connection<br/>- Queue entry]
        QueuePage[⏳ Queue Management<br/>- Position tracking<br/>- Real-time updates<br/>- Batch status]
        NFTSelectionPage[🎯 NFT Selection & Payment<br/>- Available NFTs<br/>- Lock mechanism<br/>- Payment method choice]
    end
    
    %% Backend Services Layer
    subgraph "Backend Services Layer"
        DCasksAPI[🔌 DCasks API Service<br/>- Queue management<br/>- NFT operations<br/>- Payment orchestration<br/>- Session management]
        PaymentGateway[💰 Payment Gateway Service<br/>- Stripe integration<br/>- NOWPayments integration<br/>- Unified payment processing<br/>- Webhook handling]
        JobService[⚙️ DCasks Job Service<br/>- Queue pool processor<br/>- Payment monitoring<br/>- NFT unlock scheduler<br/>- Background tasks]
    end
    
    %% Data Layer
    subgraph "Data Layer"
        Database[(🗄️ MongoDB Database<br/>- Queue state<br/>- Session storage<br/>- Sale configuration<br/>- NFT inventory<br/>- Payment records)]
    end
    
    %% External Integration Layer
    subgraph "External Integration Layer"
        Stripe[💳 Stripe API<br/>- Card payments<br/>- Checkout sessions<br/>- Webhooks]
        NOWPayments[🪙 NOWPayments API<br/>- Crypto payments<br/>- Auto-conversion<br/>- Webhooks]
        AdminWallet[📜 Admin Wallet Contract<br/>- NFT transfers<br/>- Blockchain operations]
        Blockchain[🔗 Blockchain Network<br/>- Smart contract calls<br/>- Transaction processing]
    end
    
    %% User Interactions
    User -->|1. Browse & Connect Wallet| LandingPage
    User -->|2. Monitor Queue Position| QueuePage
    User -->|3. Select NFT & Pay| NFTSelectionPage
    
    %% Frontend to Backend API Calls
    LandingPage <-->|Auth & Queue Status| DCasksAPI
    QueuePage <-->|"Position Updates (Polling)"| DCasksAPI
    NFTSelectionPage <-->|NFT Lock/Unlock & Payment Init| DCasksAPI
    
    %% Backend Service Communications
    DCasksAPI <-->|Read/Write Operations| Database
    JobService <-->|Queue & Config Management| Database
    JobService -->|Pool Processing Triggers| DCasksAPI
    JobService -->|Payment Status Monitoring| PaymentGateway
    DCasksAPI -->|Payment Session Creation| PaymentGateway
    DCasksAPI -->|NFT Transfer Requests| AdminWallet
    
    %% External Service Integrations
    PaymentGateway <-->|Payment Processing| Stripe
    PaymentGateway <-->|Crypto Processing| NOWPayments
    PaymentGateway -->|Webhook Notifications| DCasksAPI
    AdminWallet <-->|Smart Contract Calls| Blockchain
    
    %% Configuration and Monitoring
    Database -->|Sale Config & Batch Settings| JobService
    JobService -->|Failed Payment Cleanup| DCasksAPI
```

## Service Responsibilities

### Frontend Layer Services

#### **Landing Page Component**
- **Purpose**: Entry point and sale information display
- **Responsibilities**:
  - Display sale countdown and information
  - Handle crypto wallet connection
  - Initiate queue entry process
  - Show pre-sale vs active sale states
- **Dependencies**: DCasks API Service
- **Communication**: HTTP REST API calls

#### **Queue Management Component**
- **Purpose**: Real-time queue position tracking
- **Responsibilities**:
  - Display current queue position
  - Show batch processing status
  - Handle real-time updates via polling
  - Manage user session state
- **Dependencies**: DCasks API Service
- **Communication**: HTTP REST API calls (polling)

#### **NFT Selection & Payment Component**
- **Purpose**: NFT selection and payment processing
- **Responsibilities**:
  - Display available NFTs
  - Handle NFT locking mechanism
  - Provide payment method selection
  - Manage payment flow initiation
- **Dependencies**: DCasks API Service
- **Communication**: HTTP REST API calls

### Backend Services Layer

#### **DCasks API Service**
- **Purpose**: Core business logic and API gateway
- **Responsibilities**:
  - Queue management and position tracking
  - NFT inventory and locking operations
  - User session management
  - Payment orchestration
  - Authentication and authorization
  - Frontend API endpoints
- **Dependencies**: Database, Payment Gateway Service, Admin Wallet Contract
- **Communication**: HTTP REST APIs, Database queries, Smart contract calls

#### **Payment Gateway Service**
- **Purpose**: Unified payment processing
- **Responsibilities**:
  - Stripe payment session management
  - NOWPayments crypto processing
  - Webhook handling for both providers
  - Payment status tracking
  - Timeout management (20min NOWPayments, configurable Stripe)
- **Dependencies**: Stripe API, NOWPayments API
- **Communication**: HTTP REST APIs, Webhook endpoints

#### **DCasks Job Service**
- **Purpose**: Background processing and automation
- **Responsibilities**:
  - Queue pool processing (continuous management up to 100 users)
  - Payment status monitoring
  - NFT unlock scheduling for failed payments
  - Sale configuration management
  - Cleanup operations
- **Dependencies**: Database, DCasks API Service, Payment Gateway Service
- **Communication**: Database queries, Internal service calls

### Data Layer

#### **MongoDB Database**
- **Purpose**: Persistent data storage
- **Responsibilities**:
  - Queue state and user sessions
  - Sale configuration and timing
  - NFT inventory and lock status
  - Payment records and history
  - User authentication data
- **Dependencies**: None (data layer)
- **Communication**: Database connections from services

### External Integration Layer

#### **Stripe API**
- **Purpose**: Card payment processing
- **Responsibilities**:
  - Checkout session creation
  - Payment processing
  - Webhook notifications
  - Payment status updates
- **Dependencies**: External service
- **Communication**: HTTP REST API, Webhooks

#### **NOWPayments API**
- **Purpose**: Cryptocurrency payment processing
- **Responsibilities**:
  - Crypto payment sessions
  - Multi-currency support
  - Auto-conversion to target currency
  - Webhook notifications
- **Dependencies**: External service
- **Communication**: HTTP REST API, Webhooks

#### **Admin Wallet Smart Contract**
- **Purpose**: NFT transfer operations
- **Responsibilities**:
  - Post-payment NFT transfers
  - Blockchain transaction execution
  - NFT ownership management
- **Dependencies**: Blockchain Network
- **Communication**: Smart contract calls

#### **Blockchain Network**
- **Purpose**: Decentralized transaction processing
- **Responsibilities**:
  - Smart contract execution
  - Transaction validation
  - NFT transfer confirmation
- **Dependencies**: External network
- **Communication**: Blockchain protocol

## Data Flow Patterns

### 1. Queue Entry Flow
```
User → Landing Page → DCasks API → Database (queue state)
Database → Job Service → DCasks API → Queue Page (position updates)
```

### 2. Payment Processing Flow
```
User → NFT Selection → DCasks API → Payment Gateway → Stripe/NOWPayments
Stripe/NOWPayments → Webhook → Payment Gateway → DCasks API → Admin Wallet → Blockchain
```

### 3. Pool Processing Flow
```
Job Service → Database (pool config) → DCasks API (pool trigger) → Queue Page (status update)
```

### 4. NFT Transfer Flow
```
Payment Confirmation → DCasks API → Admin Wallet → Blockchain → NFT Transfer Complete
```

## Service Communication Patterns

### **Synchronous Communications**
- Frontend ↔ DCasks API (REST API calls)
- DCasks API ↔ Database (query operations)
- Payment Gateway ↔ External APIs (payment processing)
- DCasks API ↔ Admin Wallet (smart contract calls)

### **Asynchronous Communications**
- External APIs → Payment Gateway (webhooks)
- Job Service → DCasks API (pool processing triggers)
- Job Service → Payment Gateway (status monitoring)

### **Polling-Based Communications**
- Queue Page → DCasks API (position updates)
- Job Service → Payment Gateway (payment status checks)

## Technical Integration Points

### **API Endpoints**
- `POST /api/v1/collaborations/queue` - Queue entry
- `/api/v1/collaborations/queue/status` - Position tracking
- `POST /api/v1/collaborations/orders` - Order creation (includes automatic NFT locking)
- `POST /api/v1/collaborations/payments` - Payment initiation
- `/api/v1/collaborations/payments/webhook` - Payment confirmations

### **Database Collections**
- `queue_sessions` - User queue state
- `nft_inventory` - Available NFTs and locks
- `payment_records` - Payment tracking
- `sale_config` - System configuration

### **External API Integrations**
- Stripe Checkout Sessions API
- NOWPayments Payment API
- Blockchain RPC endpoints
- Smart contract ABI interfaces

## Deployment Architecture

### **Service Distribution**
- **Frontend**: Deployed to Vercel/CDN
- **Backend Services**: AWS ECS containers
- **Database**: MongoDB Atlas/AWS DocumentDB
- **External Services**: Third-party hosted

### **Network Communication**
- **Frontend ↔ Backend**: HTTPS REST APIs
- **Backend ↔ Database**: Encrypted database connections
- **Backend ↔ External**: HTTPS API calls
- **Webhook Endpoints**: HTTPS with signature verification

This technical architecture provides clear service boundaries, well-defined responsibilities, and explicit communication patterns for implementing the Sailing Whisky NFT Sale system.

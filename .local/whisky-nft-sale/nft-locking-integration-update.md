# NFT Locking Integration Update

## Overview

This document describes the recent changes to integrate NFT locking directly into the order creation process, eliminating standalone NFT locking endpoints and supporting multiple NFTs per order.

## Key Changes

### 1. Removed Standalone NFT Locking Endpoints

**Removed Endpoints:**
- `POST /api/v1/collaborations/nfts/lock` - No longer available
- `POST /api/v1/collaborations/nfts/:tokenId/unlock` - No longer available

**Rationale:** NFT locking is now integrated into order creation for better atomicity and simpler workflow.

### 2. Enhanced Order Creation API

**Updated Endpoint:** `POST /api/v1/collaborations/orders`

**New Request Format:**
```json
{
  "walletAddress": "0x1234...",
  "nftTokenIds": ["token1", "token2", "token3"], // 1-5 NFTs max
  "paymentMethod": "stripe",
  "userAgent": "Mozilla/5.0...",
  "ipAddress": "***********"
}
```

**New Response Format:**
```json
{
  "orderId": "ord_1234567890",
  "status": "NFT_LOCKED",
  "expiresAt": "2024-01-01T12:00:00Z",
  "nftTokenIds": ["token1", "token2", "token3"],
  "totalPrice": 1500.00,
  "currency": "USD",
  "createdAt": "2024-01-01T11:00:00Z",
  "paymentMethod": "stripe"
}
```

### 3. Updated Data Models

**Order Model Changes:**
- `nftTokenId: string` → `nftTokenIds: string[]` (array of 1-5 NFTs)
- `nftPrice: number` → `totalPrice: number` (sum of all NFT prices)

**DTO Changes:**
- `CreateOrderDto.nftTokenId` → `CreateOrderDto.nftTokenIds`
- Added validation: `@ArrayMinSize(1)` and `@ArrayMaxSize(5)`
- `OrderResponseDto.nftTokenId` → `OrderResponseDto.nftTokenIds`
- `OrderResponseDto.nftPrice` → `OrderResponseDto.totalPrice`

### 4. Atomic NFT Locking Process

**New Workflow:**
1. User selects 1-5 NFTs and creates order
2. System verifies user is in ACTIVE status
3. System checks all NFTs are available
4. **Atomic transaction:**
   - Lock all selected NFTs
   - Create order record
   - Update queue session to PAYMENT status
5. Return order details with all NFT information

**Benefits:**
- **Atomicity:** All operations succeed or fail together
- **Race Condition Prevention:** No gaps between locking and order creation
- **Simplified Workflow:** Single API call for complete operation
- **Multiple NFT Support:** Users can purchase up to 5 NFTs in one order

### 5. Updated Queue Session Workflow

**Maintained State Transitions:**
- `WAITING` → `ACTIVE` → `PAYMENT` → `COMPLETED`
- No changes to existing queue management logic
- NFT locking happens automatically during order creation

### 6. Error Handling Improvements

**Enhanced Validation:**
- Verify all NFTs exist and are available
- Prevent duplicate NFT selection within same order
- Atomic rollback if any NFT cannot be locked
- Clear error messages for each failure scenario

**Error Scenarios:**
- `400 Bad Request`: Invalid NFT IDs, user not in ACTIVE status
- `404 Not Found`: One or more NFTs not found
- `409 Conflict`: NFTs already locked, user has existing order

## Migration Impact

### For Frontend Developers

**Required Changes:**
1. Update order creation requests to use `nftTokenIds` array
2. Handle new response format with multiple NFTs
3. Remove any calls to standalone lock/unlock endpoints
4. Update UI to support multiple NFT selection (max 5)

**Example Frontend Update:**
```javascript
// OLD - Single NFT
const orderData = {
  walletAddress: user.address,
  nftTokenId: selectedNft.tokenId
};

// NEW - Multiple NFTs
const orderData = {
  walletAddress: user.address,
  nftTokenIds: selectedNfts.map(nft => nft.tokenId) // max 5
};
```

### For Backend Integration

**No Breaking Changes:**
- Existing queue management APIs unchanged
- Payment processing APIs unchanged
- NFT availability checking APIs unchanged

**Internal Service Updates:**
- Order service now handles NFT locking internally
- Payment monitor service updated for multiple NFTs
- All database operations use arrays for NFT references

## Testing Verification

**E2E Test Results:**
- ✅ All existing queue management tests pass (8/9 test cases)
- ✅ Order creation with multiple NFTs working
- ✅ Atomic locking prevents race conditions
- ✅ Proper cleanup when orders expire/cancel
- ✅ Payment processing works with multiple NFTs

## API Documentation Updates

**Updated Endpoints:**
- `POST /api/v1/collaborations/orders` - Now supports multiple NFTs
- `GET /api/v1/collaborations/orders/:orderId` - Returns multiple NFT details
- Removed documentation for standalone lock/unlock endpoints

**Maintained Endpoints:**
- `GET /api/v1/collaborations/nfts/available` - Unchanged
- `GET /api/v1/collaborations/nfts/:tokenId` - Unchanged
- All queue management endpoints - Unchanged

## Benefits Achieved

### Technical Benefits
- **Improved Atomicity:** Single transaction for locking and order creation
- **Better Performance:** Fewer API calls required
- **Enhanced Reliability:** No race conditions between lock and order
- **Simplified Architecture:** Fewer endpoints to maintain

### User Experience Benefits
- **Multiple NFT Purchase:** Users can buy up to 5 NFTs in one transaction
- **Faster Checkout:** Single step instead of lock → order → payment
- **Better Error Handling:** Clear feedback on availability issues
- **Consistent Pricing:** Total price calculated automatically

### Developer Benefits
- **Cleaner API:** Fewer endpoints to integrate
- **Better Documentation:** Single workflow to understand
- **Easier Testing:** Atomic operations easier to test
- **Reduced Complexity:** No separate lock management needed

## Next Steps

1. **Frontend Integration:** Update Capcom frontend to use new API format
2. **Documentation Review:** Ensure all API docs reflect new structure
3. **Monitoring Setup:** Add metrics for multiple NFT order patterns
4. **Performance Testing:** Validate performance with multiple NFT orders

---

*This update maintains backward compatibility for all queue management and payment processing while significantly improving the NFT selection and order creation experience.*

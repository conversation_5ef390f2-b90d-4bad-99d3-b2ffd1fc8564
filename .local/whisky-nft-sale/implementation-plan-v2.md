# Implementation Plan v2: Technical Service Implementation
## Sailing Whisky NFT Sale - Service-Oriented Implementation

This implementation plan aligns with the System Architecture v2 and focuses on technical service boundaries, implementation phases, and development workflows.

## Implementation Strategy

### **Phase-Based Approach**
The implementation follows a service-by-service approach, building from the data layer up through backend services to frontend components, ensuring each service is fully functional before moving to dependent services.

### **Service Dependencies**
```
Phase 1: Data Layer → Phase 2: Core Backend Services → Phase 3: Payment Integration → Phase 4: Frontend Layer → Phase 5: Integration & Testing
```

## Phase 1: Data Layer Foundation

### **MongoDB Database Setup**
**Duration**: 1-2 days  
**Dependencies**: None  
**Deliverables**: Database schema and collections

#### **Tasks:**
1. **Database Schema Design**
   - Design `queue_sessions` collection schema
   - Design `nft_inventory` collection schema  
   - Design `payment_records` collection schema
   - Design `sale_config` collection schema
   - Create database indexes for performance

2. **Database Setup**
   - Set up MongoDB instance (local/cloud)
   - Create database and collections
   - Implement data validation rules
   - Set up database connection configuration

#### **Database Relationships & Constraints:**

```javascript
// Primary Relationships
orders.queueSessionId → queue_sessions._id
orders.paymentRecordId → payment_records._id
orders.nftTokenId → nft_inventory.tokenId
payment_records.orderId → orders.orderId
nft_inventory.orderId → orders.orderId
queue_sessions.currentOrderId → orders.orderId
active_pool_tracking.activeUsers → queue_sessions.walletAddress

// Foreign Key Constraints
- orders.walletAddress must exist in queue_sessions
- payment_records.orderId must exist in orders
- nft_inventory.lockedBy must match orders.walletAddress when locked
- active_pool_tracking.activeUsers must contain valid queue_sessions.walletAddress
```

#### **Database Indexes for Performance:**

```javascript
// orders collection
db.orders.createIndex({ "orderId": 1 }, { unique: true })
db.orders.createIndex({ "walletAddress": 1 })
db.orders.createIndex({ "status": 1 })
db.orders.createIndex({ "createdAt": -1 })
db.orders.createIndex({ "expiresAt": 1 }) // For cleanup jobs

// queue_sessions collection
db.queue_sessions.createIndex({ "walletAddress": 1 }, { unique: true })
db.queue_sessions.createIndex({ "position": 1 })
db.queue_sessions.createIndex({ "status": 1 })
db.queue_sessions.createIndex({ "poolId": 1 })
db.queue_sessions.createIndex({ "expiresAt": 1 }) // For cleanup jobs

// nft_inventory collection
db.nft_inventory.createIndex({ "tokenId": 1 }, { unique: true })
db.nft_inventory.createIndex({ "status": 1 })
db.nft_inventory.createIndex({ "lockedBy": 1 })
db.nft_inventory.createIndex({ "lockExpiresAt": 1 }) // For unlock jobs

// payment_records collection
db.payment_records.createIndex({ "paymentId": 1 }, { unique: true })
db.payment_records.createIndex({ "orderId": 1 })
db.payment_records.createIndex({ "walletAddress": 1 })
db.payment_records.createIndex({ "status": 1 })
db.payment_records.createIndex({ "externalTransactionId": 1 })
db.payment_records.createIndex({ "expiresAt": 1 }) // For cleanup jobs

// active_pool_tracking collection
db.active_pool_tracking.createIndex({ "poolId": 1 }, { unique: true })
db.active_pool_tracking.createIndex({ "status": 1 })
db.active_pool_tracking.createIndex({ "createdAt": -1 })

// sale_config collection
db.sale_config.createIndex({ "configVersion": 1 }, { unique: true })
db.sale_config.createIndex({ "isActive": 1 })

// admin_actions collection
db.admin_actions.createIndex({ "executedAt": -1 })
db.admin_actions.createIndex({ "actionType": 1 })
db.admin_actions.createIndex({ "adminUser": 1 })
```

#### **Database Collections Schema:**

```javascript
// orders - Central order management and lifecycle tracking
{
  _id: ObjectId,
  orderId: String, // Unique order identifier (e.g., "ORDER_2024_001")
  walletAddress: String, // Customer wallet address
  queueSessionId: ObjectId, // Reference to queue_sessions
  nftTokenId: String, // Selected NFT token ID

  // Order Status Tracking
  status: String, // 'created', 'nft_locked', 'payment_pending', 'payment_completed', 'nft_transfer_pending', 'nft_transferred', 'failed', 'expired', 'cancelled'
  substatus: String, // Additional status details for debugging

  // Pricing Information
  nftPrice: Number,
  currency: String, // 'USD', 'USDT', etc.

  // Timeline Tracking
  createdAt: Date,
  nftLockedAt: Date,
  paymentInitiatedAt: Date,
  paymentCompletedAt: Date,
  nftTransferInitiatedAt: Date,
  nftTransferCompletedAt: Date,
  expiresAt: Date, // Overall order expiration

  // Payment Information
  paymentMethod: String, // 'stripe', 'nowpayments'
  paymentRecordId: ObjectId, // Reference to payment_records

  // NFT Transfer Information
  transferTransactionHash: String,
  transferRetryCount: Number,
  transferErrorMessage: String,

  // Metadata
  userAgent: String,
  ipAddress: String,
  metadata: Object // Additional order data
}

// queue_sessions - Queue management and user session tracking
{
  _id: ObjectId,
  walletAddress: String,
  position: Number,
  status: String, // 'waiting', 'active', 'payment', 'completed', 'expired', 'left_queue'
  poolId: String, // Reference to active pool (when applicable)

  // Timeline
  joinedAt: Date,
  activatedAt: Date, // When user enters active pool
  selectionStartedAt: Date, // When NFT selection window opens
  selectionExpiresAt: Date, // When selection window closes
  expiresAt: Date, // Overall session expiration

  // Session Management
  sessionToken: String, // For session validation
  lastHeartbeat: Date, // For connection monitoring
  connectionCount: Number, // Track reconnections

  // Order Reference
  currentOrderId: String, // Reference to active order

  // Metadata
  userAgent: String,
  ipAddress: String,
  sessionData: Object
}

// nft_inventory - NFT collection and availability management
{
  _id: ObjectId,
  tokenId: String, // Unique NFT token identifier
  collectionAddress: String, // Smart contract address

  // NFT Metadata
  name: String,
  description: String,
  imageUrl: String,
  metadataUrl: String,
  attributes: Array, // NFT traits and properties

  // Pricing
  price: Number,
  currency: String,

  // Availability Status
  status: String, // 'available', 'locked', 'sold', 'reserved', 'unavailable'
  lockedBy: String, // walletAddress of current holder
  lockedAt: Date,
  lockExpiresAt: Date,
  orderId: String, // Reference to current order

  // Transfer Information
  currentOwner: String, // Current blockchain owner
  transferHistory: Array, // Previous transfers

  // Metadata
  createdAt: Date,
  updatedAt: Date,
  isActive: Boolean
}

// payment_records - Payment processing and webhook tracking
{
  _id: ObjectId,
  paymentId: String, // Unique payment identifier
  orderId: String, // Reference to orders collection
  walletAddress: String,

  // Payment Details
  paymentMethod: String, // 'stripe', 'nowpayments'
  paymentProvider: String, // 'stripe', 'nowpayments'
  paymentSessionId: String, // External payment session ID

  // Amount Information
  amount: Number,
  currency: String,
  originalAmount: Number, // For crypto conversions
  originalCurrency: String,
  exchangeRate: Number, // Conversion rate if applicable

  // Status Tracking
  status: String, // 'created', 'pending', 'processing', 'completed', 'failed', 'expired', 'cancelled', 'refunded'
  failureReason: String,

  // External References
  externalTransactionId: String, // Stripe/NOWPayments transaction ID
  externalSessionUrl: String, // Payment URL for user

  // Timeline
  createdAt: Date,
  expiresAt: Date,
  completedAt: Date,
  failedAt: Date,

  // Webhook Tracking
  webhookReceived: Boolean,
  webhookData: Object, // Raw webhook payload
  webhookProcessedAt: Date,
  webhookRetryCount: Number,

  // Retry and Error Handling
  retryCount: Number,
  lastRetryAt: Date,
  errorMessages: Array,

  // Metadata
  userAgent: String,
  ipAddress: String
}

// sale_config - System configuration and sale parameters
{
  _id: ObjectId,
  configVersion: String, // For configuration versioning

  // Sale Timing
  saleStartTime: Date,
  saleEndTime: Date,
  isActive: Boolean,
  isPaused: Boolean,
  pauseReason: String,

  // Queue Configuration
  maxQueueSize: Number, // 100
  maxActivePoolSize: Number, // 100 (same as queue size for continuous processing)

  // Timeout Configuration
  selectionTimeoutMinutes: Number, // 5 minutes for NFT selection
  paymentTimeoutMinutes: Number, // 5 minutes for payment completion
  queueSessionTimeoutMinutes: Number, // Overall session timeout

  // Payment Configuration
  enabledPaymentMethods: Array, // ['stripe', 'nowpayments']
  stripeTimeoutMinutes: Number, // Configurable Stripe timeout
  nowpaymentsTimeoutMinutes: Number, // 20 minutes fixed

  // NFT Configuration
  nftCollectionAddress: String,
  nftBasePrice: Number,
  nftCurrency: String,

  // System Limits
  maxConcurrentPayments: Number,
  maxRetryAttempts: Number,

  // Metadata
  createdAt: Date,
  updatedAt: Date,
  createdBy: String // Admin user
}

// active_pool_tracking - Active pool management and statistics
{
  _id: ObjectId,
  poolId: String, // Unique pool identifier (typically date-based)

  // Pool Configuration
  maxPoolSize: Number, // Maximum concurrent users (100)
  currentPoolSize: Number, // Current active users
  status: String, // 'active', 'paused', 'closed'

  // User Management
  activeUsers: Array, // Currently active user wallet addresses
  totalProcessed: Number, // Total users processed through pool

  // Timeline
  createdAt: Date,
  lastUpdatedAt: Date,

  // Processing Statistics
  successfulPurchases: Number,
  failedPurchases: Number,
  timeoutCount: Number,
  averageSelectionTime: Number,
  averagePaymentTime: Number,

  // Metadata
  saleConfigVersion: String // Reference to sale config used
}

// admin_actions - Administrative actions and audit trail
{
  _id: ObjectId,
  actionType: String, // 'sale_start', 'sale_pause', 'sale_stop', 'manual_nft_transfer', 'queue_reset', 'order_cancel'
  adminUser: String, // Admin identifier

  // Action Details
  targetEntity: String, // 'sale', 'order', 'queue', 'nft'
  targetId: String, // ID of affected entity
  actionData: Object, // Action-specific data
  reason: String, // Reason for action

  // Timeline
  executedAt: Date,

  // Results
  success: Boolean,
  errorMessage: String,
  affectedRecords: Number,

  // Metadata
  ipAddress: String,
  userAgent: String
}
```

## Phase 2: Core Backend Services

### **DCasks API Service Enhancement**
**Duration**: 3-4 days  
**Dependencies**: Phase 1 (Database)  
**Deliverables**: Enhanced API service with queue and NFT management

#### **Tasks:**
1. **Queue Management API**
   - Implement `POST /api/v1/collaborations/queue` endpoint
   - Implement `/api/v1/collaborations/queue/status` endpoint
   - Add wallet authentication middleware
   - Implement queue position calculation logic
   - Add session management functionality

2. **NFT Management API**
   - Implement `/api/v1/collaborations/nfts/lock` endpoint
   - Implement NFT inventory management
   - Add NFT locking/unlocking logic
   - Implement timeout-based unlock mechanism

3. **Core Service Logic**
   - Queue position tracking
   - Active pool management coordination
   - Session state management
   - NFT availability checking
   - User authentication and authorization

#### **API Endpoints Implementation:**

```typescript
// Queue Management
POST /api/v1/collaborations/queue
- Body: { walletAddress: string }
- Response: { position: number, estimatedWaitTime: number, sessionId: string, queueSessionId: string }

GET /api/v1/collaborations/queue/status
- Query: { walletAddress: string }
- Response: { position: number, status: string, poolId?: string, canSelectNFT: boolean }

// Order Management
POST /api/v1/collaborations/orders
- Body: { walletAddress: string, nftTokenId: string }
- Response: { orderId: string, status: string, expiresAt: Date }

GET /api/v1/collaborations/orders/:orderId
- Response: { order: Order, paymentRecord?: PaymentRecord, nft: NFT }

GET /api/v1/collaborations/orders/wallet/:walletAddress
- Response: { orders: Array<Order> }

// NFT Management
POST /api/v1/collaborations/nfts/lock
- Body: { walletAddress: string, nftTokenId: string }
- Response: { success: boolean, lockExpiresAt: Date, orderId: string }

GET /api/v1/collaborations/nfts/available
- Response: { nfts: Array<NFT>, totalAvailable: number }

GET /api/v1/collaborations/nfts/:tokenId
- Response: { nft: NFT, isAvailable: boolean }
```

### **DCasks Job Service Enhancement**
**Duration**: 2-3 days  
**Dependencies**: Phase 2.1 (DCasks API)  
**Deliverables**: Background job processing service

#### **Tasks:**
1. **Queue Pool Processor**
   - Implement pool processing logic (up to 100 concurrent users)
   - Add pool activation triggers
   - Implement pool timeout management
   - Add queue position recalculation

2. **Payment Monitoring Jobs**
   - Implement payment status monitoring
   - Add payment timeout handling
   - Implement NFT unlock for failed payments
   - Add cleanup jobs for expired sessions

3. **Configuration Management**
   - Implement sale configuration loading
   - Add dynamic configuration updates
   - Implement sale start/stop controls

## Phase 3: Payment Integration Layer

### **Payment Gateway Service Enhancement**
**Duration**: 4-5 days  
**Dependencies**: Phase 2 (Backend Services)  
**Deliverables**: Unified payment processing service

#### **Tasks:**
1. **Stripe Integration**
   - Implement Stripe Checkout Session creation
   - Add Stripe webhook handling
   - Implement payment status tracking
   - Add configurable timeout management

2. **NOWPayments Integration**
   - Implement NOWPayments API integration
   - Add crypto payment session creation
   - Implement NOWPayments webhook handling
   - Add auto-conversion configuration (to USDT)
   - Implement 20-minute timeout handling

3. **Unified Payment Processing**
   - Implement `POST /api/v1/collaborations/payments` endpoint
   - Implement `/api/v1/collaborations/payments/webhook` endpoint
   - Add payment method selection logic
   - Implement payment confirmation handling
   - Add payment failure recovery

#### **Payment Service Implementation:**

```typescript
// Payment Creation
POST /api/v1/collaborations/payments
- Body: {
    orderId: string,
    paymentMethod: 'stripe' | 'nowpayments'
  }
- Response: {
    paymentId: string,
    paymentSessionId: string,
    redirectUrl: string,
    expiresAt: Date,
    amount: number,
    currency: string
  }

// Payment Status
GET /api/v1/collaborations/payments/:paymentId
- Response: {
    paymentRecord: PaymentRecord,
    order: Order,
    status: string
  }

// Webhook Handling
POST /api/v1/collaborations/payments/webhook
- Body: Stripe/NOWPayments webhook payload
- Headers: { 'x-webhook-signature': string }
- Response: { success: boolean, processed: boolean }

// Payment Retry (for failed payments)
POST /api/v1/collaborations/payments/:paymentId/retry
- Response: {
    newPaymentId: string,
    redirectUrl: string,
    expiresAt: Date
  }
```

### **Admin Wallet Smart Contract Integration**
**Duration**: 2-3 days  
**Dependencies**: Phase 3.1 (Payment Gateway)  
**Deliverables**: NFT transfer functionality

#### **Tasks:**
1. **Smart Contract Integration**
   - Implement Admin Wallet contract calls
   - Add NFT transfer functionality
   - Implement transaction monitoring
   - Add error handling and retry logic

2. **Blockchain Operations**
   - Implement post-payment NFT transfers
   - Add transaction confirmation tracking
   - Implement gas fee management
   - Add blockchain error handling

## Phase 4: Frontend Layer Implementation

### **Capcom Website Components**
**Duration**: 5-6 days  
**Dependencies**: Phase 3 (Payment Integration)  
**Deliverables**: Three-page frontend application

#### **Tasks:**
1. **Landing Page Component**
   - Implement sale countdown timer
   - Add wallet connection functionality
   - Implement queue entry interface
   - Add pre-sale vs active sale states
   - Integrate with DCasks API for queue status

2. **Queue Management Component**
   - Implement real-time position tracking
   - Add polling mechanism for updates
   - Implement pool status display
   - Add session state management
   - Integrate with queue status API

3. **NFT Selection & Payment Component**
   - Implement NFT gallery display
   - Add NFT locking functionality
   - Implement payment method selection
   - Add payment flow integration
   - Implement success/failure handling

#### **Frontend Architecture:**
```typescript
// Component Structure
src/
├── components/
│   ├── LandingPage/
│   │   ├── SaleCountdown.tsx
│   │   ├── WalletConnection.tsx
│   │   └── QueueEntry.tsx
│   ├── QueueManagement/
│   │   ├── PositionTracker.tsx
│   │   ├── PoolStatus.tsx
│   │   └── RealTimeUpdates.tsx
│   └── NFTSelection/
│       ├── NFTGallery.tsx
│       ├── PaymentMethodSelector.tsx
│       └── PaymentFlow.tsx
├── services/
│   ├── api.ts
│   ├── wallet.ts
│   └── polling.ts
└── hooks/
    ├── useQueue.ts
    ├── useNFTs.ts
    └── usePayment.ts
```

## Phase 5: Integration & Testing

### **System Integration**
**Duration**: 3-4 days  
**Dependencies**: Phase 4 (Frontend)  
**Deliverables**: Fully integrated system

#### **Tasks:**
1. **End-to-End Integration**
   - Connect all service layers
   - Implement complete user flows
   - Add error handling across services
   - Implement monitoring and logging

2. **Performance Optimization**
   - Optimize database queries
   - Implement API response caching
   - Add connection pooling
   - Optimize frontend polling intervals

3. **Security Implementation**
   - Add API rate limiting
   - Implement webhook signature verification
   - Add input validation and sanitization
   - Implement secure session management

### **Testing & Validation**
**Duration**: 2-3 days  
**Dependencies**: Phase 5.1 (Integration)  
**Deliverables**: Tested and validated system

#### **Tasks:**
1. **Functional Testing**
   - Test complete user journey
   - Validate queue management logic
   - Test payment processing flows
   - Validate NFT transfer operations

2. **Load Testing**
   - Test 100 concurrent users
   - Validate pool processing (100 concurrent users)
   - Test database performance
   - Validate API response times

3. **Integration Testing**
   - Test Stripe payment flow
   - Test NOWPayments crypto flow
   - Test webhook handling
   - Test blockchain operations

## Implementation Timeline

### **Total Duration: 15-20 days**

```
Week 1:
├── Days 1-2: Phase 1 (Database)
├── Days 3-6: Phase 2 (Backend Services)
└── Day 7: Phase 3.1 (Payment Gateway - Stripe)

Week 2:
├── Days 8-10: Phase 3.2 (NOWPayments + Admin Wallet)
├── Days 11-15: Phase 4 (Frontend Components)
└── Weekend: Buffer time

Week 3:
├── Days 16-18: Phase 5 (Integration & Testing)
├── Days 19-20: Final validation and deployment prep
└── Buffer: Additional testing if needed
```

## Development Workflow

### **Service Development Pattern**
1. **Database Schema** → **API Endpoints** → **Service Logic** → **Testing**
2. **Unit Tests** → **Integration Tests** → **End-to-End Tests**
3. **Local Development** → **Staging Deployment** → **Production Deployment**

### **Quality Gates**
- Each phase must pass functional tests before proceeding
- API endpoints must be documented and tested
- Database operations must be optimized and indexed
- Frontend components must be responsive and accessible
- Payment flows must be thoroughly tested with test accounts

## Technical Requirements

### **Development Environment**
- **Backend**: Node.js 22, TypeScript 5, NestJS framework
- **Frontend**: Next.js 15 with App Router, React 18, TypeScript
- **Database**: MongoDB with Mongoose ODM
- **Package Manager**: pnpm (replacing Yarn)
- **Deployment**: AWS ECS containers
- **Monitoring**: CloudWatch logs with 30-day retention (dev)

### **Service Communication**
- **API Protocol**: HTTP REST with JSON payloads
- **Authentication**: Crypto wallet signatures + session tokens
- **Rate Limiting**: 100 requests/minute per wallet address
- **Webhook Security**: Signature verification for Stripe/NOWPayments
- **Database**: Connection pooling with retry logic

### **External Service Configuration**
- **Stripe**: Checkout Sessions with configurable timeouts
- **NOWPayments**: 20-minute fixed timeout, auto-conversion to USDT
- **Blockchain**: Admin Wallet contract for NFT transfers
- **Frontend Deployment**: Vercel with CDN distribution

## Risk Mitigation

### **Technical Risks**
1. **Queue Concurrency**: Implement database-level locking for queue operations
2. **Payment Timeouts**: Add comprehensive timeout handling and cleanup jobs
3. **NFT Double-Spending**: Implement atomic lock/unlock operations with expiration
4. **Webhook Reliability**: Add retry mechanisms and dead letter queues
5. **Database Performance**: Implement proper indexing and query optimization

### **Integration Risks**
1. **External API Failures**: Implement circuit breakers and fallback mechanisms
2. **Blockchain Network Issues**: Add transaction monitoring and retry logic
3. **Frontend Polling Load**: Implement exponential backoff and connection limits
4. **Session Management**: Add persistent session recovery mechanisms

## Success Metrics

### **Performance Targets**
- **Queue Updates**: < 2 seconds response time
- **Payment Processing**: < 5 seconds for session creation
- **Database Queries**: < 100ms average response time
- **Frontend Loading**: < 3 seconds initial page load
- **Concurrent Users**: Support exactly 100 users in queue

### **Reliability Targets**
- **API Uptime**: 99.9% availability
- **Payment Success Rate**: 99%+ completion rate
- **NFT Transfer Accuracy**: 100% successful transfers
- **Session Recovery**: 100% session persistence across reconnects

This implementation plan provides a clear, service-oriented approach aligned with the System Architecture v2, ensuring each technical layer is properly implemented and tested before building dependent services.

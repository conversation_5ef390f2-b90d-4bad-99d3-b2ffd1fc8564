# Stripe Payment API Contracts

## Overview
This document defines the API contracts for the Sailing Whisky NFT Sale payment processing, clearly separated into internal service communication and external Stripe API integration.

## Architecture Overview
```
Capcom Frontend ←→ DCasks Backend ←→ Stripe Gateway ←→ Stripe API
                   (GraphQL)        (Internal APIs)    (External APIs)
```

---

# INTERNAL API ROUTES
*Communication between DCasks Backend and Stripe Gateway Service*

## Base Configuration
- **Base URL**: `https://dev-stripe.dcasks.co` (dev) | `https://stripe.dcasks.co` (prod)
- **Service Discovery**: AWS ECS Service Discovery via CloudMap
- **Authentication**: Service-to-service API key
- **Header**: `X-API-Key: {service_api_key}`
- **Content-Type**: `application/json`
- **Initiator**: DCasks Backend → Stripe Gateway

---

## 1. Create Payment Session

### Purpose
DCasks Backend requests Stripe Gateway to create a new payment session for NFT purchase.

### Endpoint
```
POST /internal/payments/create-session
```

### Authentication
```
X-API-Key: dcasks_stripe_gateway_api_key_dev_xyz123
X-Service-Name: dcasks-backend
X-Request-ID: req_1234567890
```

### Request Body
```json
{
  "nftOrderId": "nft_ord_1234567890",
  "productType": "NFT",
  "productId": "whisky-nft-001",
  "amount": 299.99,
  "currency": "USD",
  "userWalletAddress": "0x742d35Cc6634C0532925a3b8D4C9db96590b5",
  "metadata": {
    "nftType": "WHISKY_NFT",
    "tokenId": "12345",
    "contractAddress": "0x123...abc",
    "userEmail": "<EMAIL>"
  },
  "successUrl": "https://capcom.dcasks.co/payment-success?session_id={CHECKOUT_SESSION_ID}",
  "cancelUrl": "https://capcom.dcasks.co/payment-cancelled"
}
```

### Response (Success - 201)
```json
{
  "success": true,
  "data": {
    "paymentOrderId": "pay_ord_1234567890",
    "stripeSessionId": "cs_test_1234567890",
    "checkoutUrl": "https://checkout.stripe.com/pay/cs_test_...",
    "expiresAt": "2024-01-15T10:30:00Z",
    "status": "SESSION_CREATED"
  },
  "requestId": "req_1234567890"
}
```

### Response (Error - 400)
```json
{
  "success": false,
  "error": {
    "code": "INVALID_AMOUNT",
    "message": "Amount must be greater than 0.50 USD",
    "details": {
      "field": "amount",
      "value": 0.25,
      "minimum": 0.50
    }
  },
  "requestId": "req_1234567890"
}
```

---

## 2. Get Payment Status

### Purpose
DCasks Backend queries payment status for order tracking and NFT transfer decisions.

### Endpoint
```
GET /internal/payments/{paymentOrderId}/status
```

### Authentication
```
X-API-Key: dcasks_stripe_gateway_api_key_dev_xyz123
X-Service-Name: dcasks-backend
```

### Response (Success - 200)
```json
{
  "success": true,
  "data": {
    "paymentOrderId": "pay_ord_1234567890",
    "nftOrderId": "nft_ord_1234567890",
    "paymentStatus": "PAYMENT_SUCCEEDED",
    "amount": 299.99,
    "currency": "USD",
    "stripeSessionId": "cs_test_1234567890",
    "stripePaymentIntentId": "pi_test_1234567890",
    "stripeChargeId": "ch_test_1234567890",
    "paymentMethod": "card",
    "createdAt": "2024-01-15T10:00:00Z",
    "paymentCompletedAt": "2024-01-15T10:05:00Z",
    "metadata": {
      "tokenId": "12345",
      "userWalletAddress": "0x742d35Cc6634C0532925a3b8D4C9db96590b5"
    }
  }
}
```

### Response (Error - 404)
```json
{
  "success": false,
  "error": {
    "code": "PAYMENT_ORDER_NOT_FOUND",
    "message": "Payment order not found",
    "details": {
      "paymentOrderId": "pay_ord_invalid"
    }
  }
}
```

---

## 3. Payment Completion Notification

### Purpose
Stripe Gateway notifies DCasks Backend when payment is completed (triggered by webhook).

### Endpoint
```
POST /internal/payments/notify-completion
```

### Authentication
```
X-API-Key: stripe_gateway_dcasks_backend_api_key_xyz789
X-Service-Name: stripe-gateway
X-Webhook-Event-ID: evt_1234567890
```

### Request Body
```json
{
  "paymentOrderId": "pay_ord_1234567890",
  "nftOrderId": "nft_ord_1234567890",
  "paymentStatus": "PAYMENT_SUCCEEDED",
  "stripeEventId": "evt_1234567890",
  "stripePaymentIntentId": "pi_test_1234567890",
  "stripeChargeId": "ch_test_1234567890",
  "amount": 299.99,
  "currency": "USD",
  "paymentMethod": "card",
  "completedAt": "2024-01-15T10:05:00Z",
  "metadata": {
    "tokenId": "12345",
    "userWalletAddress": "0x742d35Cc6634C0532925a3b8D4C9db96590b5"
  }
}
```

### Response (Success - 200)
```json
{
  "success": true,
  "data": {
    "nftOrderId": "nft_ord_1234567890",
    "transferStatus": "TRANSFER_QUEUED",
    "message": "NFT transfer queued for processing"
  }
}
```

---

## 4. Bulk Payment Status Query

### Purpose
DCasks Backend queries multiple payment statuses for batch processing and reconciliation.

### Endpoint
```
POST /internal/payments/bulk-status
```

### Request Body
```json
{
  "paymentOrderIds": [
    "pay_ord_1234567890",
    "pay_ord_1234567891",
    "pay_ord_1234567892"
  ],
  "includeMetadata": true
}
```

### Response (Success - 200)
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "paymentOrderId": "pay_ord_1234567890",
        "paymentStatus": "PAYMENT_SUCCEEDED",
        "completedAt": "2024-01-15T10:05:00Z"
      },
      {
        "paymentOrderId": "pay_ord_1234567891",
        "paymentStatus": "PAYMENT_PROCESSING",
        "completedAt": null
      }
    ],
    "totalCount": 3,
    "processedCount": 2
  }
}
```

---

## DCasks Backend GraphQL APIs

### Base URL
- **Development**: `https://dev-api-ecs.dcasks.co/graphql`
- **Production**: `https://api-ecs.dcasks.co/graphql`

### Authentication
- **Type**: Wallet Signature Authentication
- **Header**: `Authorization: Bearer {jwt_token}`

---

## 1. Create NFT Order Mutation

### GraphQL Mutation
```graphql
mutation CreateNFTOrder($input: CreateNFTOrderInput!) {
  createNFTOrder(input: $input) {
    nftOrderId
    paymentOrderId
    status
    checkoutUrl
    expiresAt
  }
}
```

### Input Type
```graphql
input CreateNFTOrderInput {
  tokenId: String!
  userWalletAddress: String!
  amount: Float!
  currency: String!
  paymentMethod: PaymentMethod!
}

enum PaymentMethod {
  STRIPE
  CRYPTO
}
```

### Response
```json
{
  "data": {
    "createNFTOrder": {
      "nftOrderId": "nft_ord_1234567890",
      "paymentOrderId": "pay_ord_1234567890",
      "status": "CREATED",
      "checkoutUrl": "https://checkout.stripe.com/pay/cs_test_...",
      "expiresAt": "2024-01-15T10:30:00Z"
    }
  }
}
```

---

## 2. Get NFT Order Query

### GraphQL Query
```graphql
query GetNFTOrder($nftOrderId: ID!) {
  nftOrder(nftOrderId: $nftOrderId) {
    nftOrderId
    paymentOrderId
    tokenId
    userWalletAddress
    paymentStatus
    transferStatus
    amount
    currency
    blockchainTxHash
    createdAt
    transferCompletedAt
  }
}
```

### Response
```json
{
  "data": {
    "nftOrder": {
      "nftOrderId": "nft_ord_1234567890",
      "paymentOrderId": "pay_ord_1234567890",
      "tokenId": "12345",
      "userWalletAddress": "0x742d35Cc6634C0532925a3b8D4C9db96590b5",
      "paymentStatus": "PAYMENT_SUCCEEDED",
      "transferStatus": "TRANSFER_COMPLETED",
      "amount": 299.99,
      "currency": "USD",
      "blockchainTxHash": "0xabc123...",
      "createdAt": "2024-01-15T10:00:00Z",
      "transferCompletedAt": "2024-01-15T10:10:00Z"
    }
  }
}
```

---

## 3. Get User Orders Query

### GraphQL Query
```graphql
query GetUserOrders($userId: ID!, $limit: Int, $offset: Int) {
  userOrders(userId: $userId, limit: $limit, offset: $offset) {
    orders {
      nftOrderId
      paymentOrderId
      tokenId
      paymentStatus
      transferStatus
      amount
      currency
      createdAt
    }
    totalCount
    hasMore
  }
}
```

---

## Error Codes Reference

### Stripe Gateway Errors
- `INVALID_AMOUNT` - Amount validation failed
- `INVALID_CURRENCY` - Unsupported currency
- `INVALID_WALLET` - Invalid wallet address format
- `STRIPE_ERROR` - Stripe API error
- `SESSION_EXPIRED` - Payment session expired
- `PAYMENT_FAILED` - Payment processing failed

### DCasks Backend Errors
- `NFT_NOT_AVAILABLE` - Selected NFT is not available
- `USER_NOT_FOUND` - User authentication failed
- `INSUFFICIENT_BALANCE` - Admin wallet insufficient balance
- `TRANSFER_FAILED` - NFT transfer failed
- `ORDER_NOT_FOUND` - Order ID not found
- `UNAUTHORIZED` - User not authorized for this order

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `409` - Conflict (e.g., NFT already sold)
- `500` - Internal Server Error
- `503` - Service Unavailable

## ECS Service Discovery Configuration

### AWS CloudMap Service Registration
- **Namespace**: `dcasks.local` (private DNS namespace)
- **Service Names**:
  - `stripe-gateway.dcasks.local` (Stripe Gateway service)
  - `dcasks-backend.dcasks.local` (DCasks Backend service)
- **Health Checks**: HTTP health check on `/health` endpoint
- **Service Discovery**: Automatic service registration and deregistration
- **Load Balancing**: Round-robin across healthy service instances

### Internal Service Communication
- **Discovery Method**: AWS CloudMap DNS-based service discovery
- **Network**: Private VPC subnets only
- **Security Groups**: Allow traffic only between registered services
- **Authentication**: X-API-Key header validation
- **Monitoring**: CloudWatch service discovery metrics

## Rate Limiting
- **Stripe Gateway**: 100 requests per minute per IP
- **DCasks Backend**: 1000 requests per minute per user
- **Headers**: `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Request/Response Examples

### Complete Payment Flow Example

1. **Create NFT Order**
```bash
curl -X POST https://dev-api-ecs.dcasks.co/graphql \
  -H "Authorization: Bearer jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "mutation CreateNFTOrder($input: CreateNFTOrderInput!) { createNFTOrder(input: $input) { nftOrderId paymentOrderId checkoutUrl } }",
    "variables": {
      "input": {
        "tokenId": "12345",
        "userWalletAddress": "0x742d35Cc6634C0532925a3b8D4C9db96590b5",
        "amount": 299.99,
        "currency": "USD",
        "paymentMethod": "STRIPE"
      }
    }
  }'
```

2. **Check Payment Status**
```bash
curl -X GET https://dev-stripe.dcasks.co/internal/payments/pay_ord_1234567890/status \
  -H "X-API-Key: dcasks_stripe_gateway_api_key_dev_xyz123" \
  -H "X-Service-Name: dcasks-backend"
```

3. **Get Final Order Status**
```bash
curl -X POST https://dev-api-ecs.dcasks.co/graphql \
  -H "Authorization: Bearer jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query GetNFTOrder($nftOrderId: ID!) { nftOrder(nftOrderId: $nftOrderId) { paymentStatus transferStatus blockchainTxHash } }",
    "variables": {
      "nftOrderId": "nft_ord_1234567890"
    }
  }'
```

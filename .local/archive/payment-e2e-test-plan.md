# Payment E2E Test Plan

## Overview
This document defines end-to-end test scenarios for the Sailing Whisky NFT Sale payment system using Playwright, focusing on critical user journeys and error handling.

## Test Environment Setup

### Prerequisites
```bash
# Install Playwright
npm install @playwright/test

# Configure test environment
cp .env.test.example .env.test
```

### Test Configuration
```javascript
// playwright.config.js
module.exports = {
  testDir: './e2e/payment',
  timeout: 60000,
  use: {
    baseURL: 'http://localhost:3000',
    headless: false, // Run with --headed flag for development
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  reporter: [['list'], ['html']],
  projects: [
    {
      name: 'payment-flow',
      testMatch: 'payment-*.spec.js'
    }
  ]
};
```

## Test Data Setup

### Stripe Test Cards
```javascript
const STRIPE_TEST_CARDS = {
  SUCCESS: '****************',
  DECLINED: '****************',
  INSUFFICIENT_FUNDS: '****************',
  EXPIRED: '****************',
  PROCESSING_ERROR: '****************',
  REQUIRES_3DS: '****************'
};

const TEST_USER_WALLET = '0x742d35Cc6634C0532925a3b8D4C9db96590b5';
const TEST_NFT_TOKEN_ID = 'whisky-nft-test-001';
```

### Database Test Data
```javascript
// Setup test NFT inventory
const testNFTs = [
  {
    tokenId: 'whisky-nft-test-001',
    contractAddress: '0x123...abc',
    price: 299.99,
    available: true,
    metadata: {
      name: 'Sailing Whisky #001',
      rarity: 'Rare',
      attributes: { age: 25, region: 'Speyside' }
    }
  }
];
```

## Core Test Scenarios

### 1. Successful Payment Flow

#### Test: Complete Stripe Payment to NFT Transfer
```javascript
// e2e/payment/successful-payment.spec.js
test('Complete payment flow - Stripe to NFT transfer', async ({ page }) => {
  // Step 1: Navigate to NFT selection
  await page.goto('/nft-sale');
  await page.waitForSelector('[data-testid="nft-grid"]');
  
  // Step 2: Select NFT and initiate payment
  await page.click('[data-testid="nft-card-whisky-001"]');
  await page.click('[data-testid="buy-with-card-button"]');
  
  // Step 3: Verify order creation
  await page.waitForSelector('[data-testid="payment-loading"]');
  const orderIdElement = await page.locator('[data-testid="order-id"]');
  const orderId = await orderIdElement.textContent();
  expect(orderId).toMatch(/^nft_ord_/);
  
  // Step 4: Complete Stripe payment
  await page.waitForSelector('[data-testid="stripe-checkout-redirect"]');
  await page.click('[data-testid="stripe-checkout-redirect"]');
  
  // Fill Stripe checkout form
  await page.fill('[data-testid="card-number"]', STRIPE_TEST_CARDS.SUCCESS);
  await page.fill('[data-testid="card-expiry"]', '12/25');
  await page.fill('[data-testid="card-cvc"]', '123');
  await page.fill('[data-testid="billing-name"]', 'Test User');
  await page.click('[data-testid="submit-payment"]');
  
  // Step 5: Verify payment success
  await page.waitForURL('**/payment-success**');
  await expect(page.locator('[data-testid="payment-success-message"]')).toBeVisible();
  
  // Step 6: Verify NFT transfer completion
  await page.waitForSelector('[data-testid="nft-transfer-status"]');
  await expect(page.locator('[data-testid="nft-transfer-status"]')).toHaveText('Completed');
  
  // Step 7: Verify blockchain transaction
  const txHashElement = await page.locator('[data-testid="blockchain-tx-hash"]');
  const txHash = await txHashElement.textContent();
  expect(txHash).toMatch(/^0x[a-fA-F0-9]{64}$/);
  
  // Step 8: Verify order in user history
  await page.goto('/my-orders');
  await expect(page.locator(`[data-testid="order-${orderId}"]`)).toBeVisible();
  await expect(page.locator(`[data-testid="order-${orderId}-status"]`)).toHaveText('Completed');
});
```

### 2. Payment Failure Scenarios

#### Test: Card Declined
```javascript
test('Payment failure - Card declined', async ({ page }) => {
  // Navigate and select NFT
  await page.goto('/nft-sale');
  await page.click('[data-testid="nft-card-whisky-001"]');
  await page.click('[data-testid="buy-with-card-button"]');
  
  // Complete checkout with declined card
  await page.waitForSelector('[data-testid="stripe-checkout-redirect"]');
  await page.click('[data-testid="stripe-checkout-redirect"]');
  await page.fill('[data-testid="card-number"]', STRIPE_TEST_CARDS.DECLINED);
  await page.fill('[data-testid="card-expiry"]', '12/25');
  await page.fill('[data-testid="card-cvc"]', '123');
  await page.click('[data-testid="submit-payment"]');
  
  // Verify failure handling
  await page.waitForSelector('[data-testid="payment-error"]');
  await expect(page.locator('[data-testid="payment-error-message"]')).toContainText('declined');
  
  // Verify NFT is available again
  await page.goto('/nft-sale');
  await expect(page.locator('[data-testid="nft-card-whisky-001"]')).toBeVisible();
  await expect(page.locator('[data-testid="nft-available-badge"]')).toBeVisible();
});
```

#### Test: Payment Session Timeout
```javascript
test('Payment failure - Session timeout', async ({ page }) => {
  // Create payment session
  await page.goto('/nft-sale');
  await page.click('[data-testid="nft-card-whisky-001"]');
  await page.click('[data-testid="buy-with-card-button"]');
  
  const orderIdElement = await page.locator('[data-testid="order-id"]');
  const orderId = await orderIdElement.textContent();
  
  // Simulate session timeout (mock API response)
  await page.route('**/api/payments/**', route => {
    route.fulfill({
      status: 400,
      body: JSON.stringify({
        success: false,
        error: { code: 'SESSION_EXPIRED', message: 'Payment session has expired' }
      })
    });
  });
  
  // Try to access expired session
  await page.reload();
  await expect(page.locator('[data-testid="session-expired-message"]')).toBeVisible();
  
  // Verify user can restart payment
  await page.click('[data-testid="restart-payment-button"]');
  await expect(page.locator('[data-testid="stripe-checkout-redirect"]')).toBeVisible();
});
```

### 3. NFT Transfer Failure Scenarios

#### Test: Blockchain Network Error
```javascript
test('NFT transfer failure - Network error', async ({ page }) => {
  // Complete successful payment first
  await completeSuccessfulPayment(page);
  
  // Mock blockchain network error
  await page.route('**/api/nft-transfer/**', route => {
    route.fulfill({
      status: 500,
      body: JSON.stringify({
        success: false,
        error: { code: 'BLOCKCHAIN_ERROR', message: 'Network timeout' }
      })
    });
  });
  
  // Verify error handling
  await page.waitForSelector('[data-testid="transfer-error"]');
  await expect(page.locator('[data-testid="transfer-error-message"]')).toContainText('network');
  
  // Verify retry mechanism
  await expect(page.locator('[data-testid="transfer-retry-button"]')).toBeVisible();
  await page.click('[data-testid="transfer-retry-button"]');
  
  // Verify retry attempt
  await expect(page.locator('[data-testid="transfer-status"]')).toHaveText('Retrying...');
});
```

### 4. Webhook Testing

#### Test: Webhook Delivery and Processing
```javascript
test('Webhook processing - Payment success', async ({ page, request }) => {
  // Create payment order
  const orderResponse = await request.post('/api/graphql', {
    data: {
      query: `
        mutation CreateNFTOrder($input: CreateNFTOrderInput!) {
          createNFTOrder(input: $input) {
            nftOrderId
            paymentOrderId
          }
        }
      `,
      variables: {
        input: {
          tokenId: TEST_NFT_TOKEN_ID,
          userWalletAddress: TEST_USER_WALLET,
          amount: 299.99,
          currency: 'USD',
          paymentMethod: 'STRIPE'
        }
      }
    }
  });
  
  const { nftOrderId, paymentOrderId } = orderResponse.data.createNFTOrder;
  
  // Simulate Stripe webhook
  const webhookPayload = {
    id: 'evt_test_webhook',
    type: 'payment_intent.succeeded',
    data: {
      object: {
        id: 'pi_test_123',
        status: 'succeeded',
        metadata: { paymentOrderId }
      }
    }
  };
  
  // Send webhook
  const webhookResponse = await request.post('/api/payments/webhook', {
    data: webhookPayload,
    headers: {
      'stripe-signature': 'test-signature'
    }
  });
  
  expect(webhookResponse.status()).toBe(200);
  
  // Verify order status updated
  await page.goto(`/order-status/${nftOrderId}`);
  await page.waitForSelector('[data-testid="payment-status"]');
  await expect(page.locator('[data-testid="payment-status"]')).toHaveText('Succeeded');
});
```

## Performance Tests

### Test: Concurrent Payment Processing
```javascript
test('Performance - Concurrent payments', async ({ browser }) => {
  const contexts = await Promise.all(
    Array.from({ length: 5 }, () => browser.newContext())
  );
  
  const pages = await Promise.all(
    contexts.map(context => context.newPage())
  );
  
  // Start concurrent payment flows
  const paymentPromises = pages.map(async (page, index) => {
    await page.goto('/nft-sale');
    await page.click(`[data-testid="nft-card-whisky-00${index + 1}"]`);
    await page.click('[data-testid="buy-with-card-button"]');
    
    const startTime = Date.now();
    await page.waitForSelector('[data-testid="stripe-checkout-redirect"]');
    const endTime = Date.now();
    
    return endTime - startTime;
  });
  
  const responseTimes = await Promise.all(paymentPromises);
  
  // Verify all payments initiated within acceptable time
  responseTimes.forEach(time => {
    expect(time).toBeLessThan(3000); // 3 seconds max
  });
  
  // Cleanup
  await Promise.all(contexts.map(context => context.close()));
});
```

## Test Utilities

### Helper Functions
```javascript
// test-utils/payment-helpers.js
export async function completeSuccessfulPayment(page) {
  await page.goto('/nft-sale');
  await page.click('[data-testid="nft-card-whisky-001"]');
  await page.click('[data-testid="buy-with-card-button"]');
  await page.waitForSelector('[data-testid="stripe-checkout-redirect"]');
  await page.click('[data-testid="stripe-checkout-redirect"]');
  await page.fill('[data-testid="card-number"]', STRIPE_TEST_CARDS.SUCCESS);
  await page.fill('[data-testid="card-expiry"]', '12/25');
  await page.fill('[data-testid="card-cvc"]', '123');
  await page.click('[data-testid="submit-payment"]');
  await page.waitForURL('**/payment-success**');
}

export async function mockStripeWebhook(request, paymentOrderId, eventType = 'payment_intent.succeeded') {
  return await request.post('/api/payments/webhook', {
    data: {
      id: `evt_test_${Date.now()}`,
      type: eventType,
      data: {
        object: {
          id: `pi_test_${Date.now()}`,
          status: eventType.includes('succeeded') ? 'succeeded' : 'failed',
          metadata: { paymentOrderId }
        }
      }
    }
  });
}
```

### Database Cleanup
```javascript
// test-utils/db-cleanup.js
export async function cleanupTestData() {
  // Clean up test orders
  await db.paymentorders.deleteMany({ 
    userId: TEST_USER_WALLET,
    productId: { $regex: /^whisky-nft-test/ }
  });
  
  await db.nftsaleorders.deleteMany({
    userWalletAddress: TEST_USER_WALLET,
    tokenId: { $regex: /^whisky-nft-test/ }
  });
  
  // Reset NFT availability
  await db.nftinventory.updateMany(
    { tokenId: { $regex: /^whisky-nft-test/ } },
    { $set: { available: true, lockedBy: null } }
  );
}
```

## Test Execution

### Run Commands
```bash
# Run all payment tests
npx playwright test e2e/payment --headed

# Run specific test file
npx playwright test e2e/payment/successful-payment.spec.js --headed

# Run tests with debugging
npx playwright test --debug

# Generate test report
npx playwright show-report
```

### CI/CD Integration
```yaml
# .github/workflows/e2e-tests.yml
name: E2E Payment Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e:payment
        env:
          STRIPE_TEST_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
          DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
```

## Success Criteria

### Test Coverage Requirements
- **Payment Flow**: 100% of critical paths tested
- **Error Scenarios**: All major error conditions covered
- **Performance**: Response times under 3 seconds
- **Reliability**: 99% test pass rate in CI/CD

### Key Metrics to Validate
- Payment success rate > 95%
- NFT transfer completion rate > 99%
- Average payment processing time < 5 minutes
- Webhook processing success rate > 99.9%

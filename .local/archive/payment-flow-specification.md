# Payment Flow Specification

## Overview
This document defines the complete payment flow for the Sailing Whisky NFT Sale, covering both Stripe and crypto payment methods with detailed state transitions and error handling.

## Main Payment Flow (Happy Path)

### 1. Payment Initiation
```mermaid
sequenceDiagram
    participant User
    participant Capcom as Capcom Frontend
    participant DCasks as DCasks Backend
    participant Stripe_GW as Stripe Gateway
    participant Stripe_API as Stripe API

    User->>Capcom: Select NFT & Click "Buy with Card"
    Capcom->>DCasks: GraphQL: createNFTOrder(tokenId, wallet, amount)
    DCasks->>DCasks: Validate NFT availability & user
    DCasks->>DCasks: Create NFT order record
    DCasks->>Stripe_GW: POST /create-session
    Stripe_GW->>Stripe_GW: Create payment order record
    Stripe_GW->>Stripe_API: Create checkout session
    Stripe_API-->>Stripe_GW: Return session URL & ID
    Stripe_GW-->>DCasks: Return checkout URL
    DCasks-->>Capcom: Return order details + checkout URL
    Capcom->>User: Redirect to Stripe Checkout
```

### 2. Payment Processing
```mermaid
sequenceDiagram
    participant User
    participant Stripe_API as Stripe API
    participant Stripe_GW as Stripe Gateway
    participant DCasks as DCasks Backend

    User->>Stripe_API: Complete payment (card/wallet)
    Stripe_API->>User: Show payment confirmation
    Stripe_API->>Stripe_GW: Webhook: payment_intent.succeeded
    Stripe_GW->>Stripe_GW: Update payment status to SUCCEEDED
    Stripe_GW->>DCasks: POST /payment-completed notification
    DCasks->>DCasks: Update NFT order status to PROCESSING
    DCasks->>DCasks: Queue NFT transfer job
```

### 3. NFT Transfer & Completion
```mermaid
sequenceDiagram
    participant DCasks as DCasks Backend
    participant JobService as DCasks Job Service
    participant SMC as Sale Smart Contract
    participant User_Wallet as User Wallet
    participant Capcom as Capcom Frontend

    JobService->>JobService: Pick up NFT transfer job
    JobService->>SMC: Transfer NFT from admin wallet
    SMC->>User_Wallet: Transfer NFT to user wallet
    SMC-->>JobService: Return transaction hash
    JobService->>DCasks: Update order status to COMPLETED
    DCasks->>DCasks: Store blockchain transaction details
    
    Note over Capcom: User checks order status
    Capcom->>DCasks: GraphQL: getNFTOrder(orderId)
    DCasks-->>Capcom: Return completed order + tx hash
    Capcom->>User: Show success + NFT in wallet
```

## State Transitions

### Payment Order States
```mermaid
stateDiagram-v2
    [*] --> CREATED : Order initiated
    CREATED --> SESSION_CREATED : Stripe session created
    SESSION_CREATED --> PAYMENT_PENDING : User redirected to Stripe
    PAYMENT_PENDING --> PAYMENT_PROCESSING : Stripe processing
    PAYMENT_PROCESSING --> PAYMENT_SUCCEEDED : Payment confirmed
    PAYMENT_PROCESSING --> PAYMENT_FAILED : Payment declined
    PAYMENT_PENDING --> PAYMENT_CANCELLED : User cancelled
    SESSION_CREATED --> PAYMENT_EXPIRED : Session timeout
    PAYMENT_SUCCEEDED --> [*] : Triggers NFT transfer
    PAYMENT_FAILED --> [*]
    PAYMENT_CANCELLED --> [*]
    PAYMENT_EXPIRED --> [*]
```

### NFT Transfer States
```mermaid
stateDiagram-v2
    [*] --> TRANSFER_PENDING : Payment succeeded
    TRANSFER_PENDING --> TRANSFER_PROCESSING : Job service picks up
    TRANSFER_PROCESSING --> TRANSFER_SUBMITTED : Transaction submitted
    TRANSFER_SUBMITTED --> TRANSFER_CONFIRMING : Awaiting confirmations
    TRANSFER_CONFIRMING --> TRANSFER_COMPLETED : NFT transferred
    TRANSFER_PROCESSING --> TRANSFER_FAILED : Transaction failed
    TRANSFER_FAILED --> TRANSFER_RETRY : Retry mechanism
    TRANSFER_RETRY --> TRANSFER_PROCESSING : Retry attempt
    TRANSFER_FAILED --> TRANSFER_MANUAL_REVIEW : Max retries exceeded
    TRANSFER_COMPLETED --> [*]
```

## Error Handling Scenarios

### 1. Payment Failures

#### Scenario: Card Declined
```
Flow: PAYMENT_PROCESSING → PAYMENT_FAILED
Trigger: Stripe webhook payment_intent.payment_failed
Actions:
1. Update payment order status to FAILED
2. Release NFT lock (make available again)
3. Redirect user to failure page with retry option
4. Log failure reason for analytics
```

#### Scenario: Payment Session Expired
```
Flow: SESSION_CREATED → PAYMENT_EXPIRED
Trigger: 30-minute session timeout
Actions:
1. Update payment order status to EXPIRED
2. Release NFT lock automatically
3. Show timeout message to user
4. Allow user to restart payment process
```

### 2. NFT Transfer Failures

#### Scenario: Insufficient Gas/Balance
```
Flow: TRANSFER_PROCESSING → TRANSFER_FAILED
Trigger: Blockchain transaction failure
Actions:
1. Update transfer status to FAILED
2. Log error details (gas estimation, balance check)
3. Queue for retry with exponential backoff
4. Alert admin if retry limit exceeded
```

#### Scenario: Smart Contract Error
```
Flow: TRANSFER_SUBMITTED → TRANSFER_FAILED
Trigger: Contract execution failure
Actions:
1. Update transfer status to FAILED
2. Capture transaction hash and error details
3. Queue for manual review
4. Notify admin for intervention
```

### 3. System Failures

#### Scenario: Webhook Delivery Failure
```
Problem: Stripe webhook not received
Detection: Payment succeeded on Stripe but order still PENDING
Recovery:
1. Implement webhook retry mechanism (Stripe automatic)
2. Manual reconciliation job runs every 5 minutes
3. Query Stripe API for payment status updates
4. Update local order status accordingly
```

#### Scenario: Database Connection Loss
```
Problem: Cannot update order status
Detection: Database operation timeout/error
Recovery:
1. Implement retry logic with exponential backoff
2. Queue failed operations for later processing
3. Use circuit breaker pattern for database calls
4. Alert monitoring system for intervention
```

## Timeout Handling

### Payment Timeouts
- **Session Timeout**: 30 minutes (Stripe default)
- **Payment Processing**: 5 minutes maximum
- **User Action**: 10 minutes for payment completion

### NFT Transfer Timeouts
- **Job Processing**: 2 minutes per transfer attempt
- **Blockchain Confirmation**: 10 minutes maximum wait
- **Retry Intervals**: 1min, 2min, 5min, 10min, 30min

## Retry Mechanisms

### Payment Retries
- **User-Initiated**: Allow user to retry failed payments
- **Session Recreation**: Create new Stripe session for retries
- **Limit**: 3 retry attempts per user per NFT

### Transfer Retries
- **Automatic Retry**: 5 attempts with exponential backoff
- **Retry Conditions**: Network errors, gas estimation failures
- **No Retry**: Invalid wallet address, contract errors
- **Manual Review**: After 5 failed attempts

## Webhook Security

### Stripe Webhook Verification
```javascript
// Verify webhook signature
const signature = request.headers['stripe-signature'];
const payload = request.body;
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

try {
  const event = stripe.webhooks.constructEvent(payload, signature, endpointSecret);
  // Process verified webhook
} catch (err) {
  // Invalid signature - reject webhook
  return response.status(400).send('Webhook signature verification failed');
}
```

### Idempotency Handling
```javascript
// Prevent duplicate webhook processing
const eventId = event.id;
const existingEvent = await WebhookEvent.findOne({ stripeEventId: eventId });

if (existingEvent) {
  return response.status(200).send('Event already processed');
}

// Process webhook and store event ID
await processWebhook(event);
await WebhookEvent.create({ stripeEventId: eventId, processedAt: new Date() });
```

## Performance Considerations

### Response Time Targets
- **Payment Initiation**: < 2 seconds
- **Webhook Processing**: < 1 second
- **NFT Transfer**: < 30 seconds
- **Status Queries**: < 500ms

### Concurrency Handling
- **Payment Sessions**: 100 concurrent Stripe sessions
- **NFT Transfers**: 10 concurrent blockchain transactions
- **Database Connections**: Connection pooling with 20 max connections
- **API Rate Limits**: Respect Stripe API limits (100 req/sec)

## Monitoring & Alerting

### Key Metrics
- **Payment Success Rate**: Target 95%+
- **NFT Transfer Success Rate**: Target 99%+
- **Average Payment Time**: Target < 5 minutes
- **Webhook Delivery Rate**: Target 99.9%+

### Alert Conditions
- Payment success rate drops below 90%
- NFT transfer failures > 5 in 10 minutes
- Webhook delivery failures > 3 in 5 minutes
- Database connection errors > 10 in 1 minute
- Order stuck in PROCESSING > 30 minutes

### Dashboard Metrics
- Real-time payment volume
- Success/failure rates by payment method
- Average processing times
- Queue of pending NFT transfers
- Failed orders requiring manual review

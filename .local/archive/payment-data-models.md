# Payment Data Models

## Overview
This document defines the database schemas and data models for the Sailing Whisky NFT Sale payment system, including relationships, indexes, and validation rules.

## Database Collections

### 1. PaymentOrder Collection (Generic Payment Model)

#### Schema Definition
```javascript
{
  // Core Identifiers
  _id: ObjectId,
  paymentOrderId: String, // UUID, unique index
  userId: String, // User wallet address, indexed
  
  // Product Information
  productType: String, // enum: "NFT", "SUBSCRIPTION", etc.
  productId: String, // Reference to specific product
  productMetadata: {
    nftType: String, // "WHISKY_NFT"
    tokenId: String,
    contractAddress: String,
    rarity: String,
    attributes: Object
  },
  
  // Payment Details
  amount: Number, // Payment amount in base currency
  currency: String, // "USD", "EUR", etc.
  
  // Stripe Integration
  stripeSessionId: String, // Unique, sparse index
  stripePaymentIntentId: String, // Unique, sparse index
  stripeChargeId: String, // Unique, sparse index
  stripeCustomerId: String, // Indexed for future use
  
  // Payment Status Tracking
  paymentStatus: String, // enum, indexed
  paymentRetryCount: Number, // default: 0
  paymentLastRetryAt: Date,
  paymentErrorDetails: {
    code: String,
    message: String,
    stripeErrorCode: String,
    declineCode: String
  },
  
  // Timestamps
  createdAt: Date, // indexed
  updatedAt: Date,
  paymentCompletedAt: Date,
  expiresAt: Date, // TTL index for cleanup
  
  // Admin & Audit
  adminNotes: String,
  metadata: Object // Additional flexible data
}
```

#### PaymentStatus Enum
```javascript
const PaymentStatus = {
  // Initial States
  CREATED: "CREATED",
  SESSION_CREATED: "SESSION_CREATED",
  PAYMENT_PENDING: "PAYMENT_PENDING",
  PAYMENT_PROCESSING: "PAYMENT_PROCESSING",
  
  // Success States
  PAYMENT_SUCCEEDED: "PAYMENT_SUCCEEDED",
  
  // Failure States
  PAYMENT_FAILED: "PAYMENT_FAILED",
  PAYMENT_CANCELLED: "PAYMENT_CANCELLED",
  PAYMENT_EXPIRED: "PAYMENT_EXPIRED",
  
  // Admin States
  PAYMENT_MANUAL_REVIEW: "PAYMENT_MANUAL_REVIEW",
  REFUND_PENDING: "REFUND_PENDING",
  REFUND_COMPLETED: "REFUND_COMPLETED"
};
```

### 2. NFTSaleOrder Collection (NFT-Specific Model)

#### Schema Definition
```javascript
{
  // Core Identifiers
  _id: ObjectId,
  nftOrderId: String, // UUID, unique index
  paymentOrderId: String, // FK to PaymentOrder, indexed
  
  // NFT Information
  tokenId: String, // NFT token ID
  contractAddress: String, // NFT contract address
  nftType: String, // enum: "WHISKY_NFT", "CASK_NFT"
  
  // Wallet Information
  userWalletAddress: String, // indexed
  adminWalletAddress: String, // Source wallet
  
  // Blockchain Transaction Details
  blockchainTxHash: String, // unique, sparse index
  blockchainConfirmations: Number, // default: 0
  gasUsed: Number,
  gasPrice: String, // in wei
  blockNumber: Number,
  
  // Transfer Status Tracking
  transferStatus: String, // enum, indexed
  transferRetryCount: Number, // default: 0
  transferLastRetryAt: Date,
  transferErrorDetails: {
    code: String,
    message: String,
    txHash: String,
    blockNumber: Number,
    gasEstimationError: String
  },
  
  // Timestamps
  createdAt: Date, // indexed
  updatedAt: Date,
  transferInitiatedAt: Date,
  transferCompletedAt: Date,
  
  // NFT Metadata
  nftMetadata: {
    name: String,
    description: String,
    image: String,
    attributes: Array,
    rarity: String,
    edition: Number
  },
  transferMetadata: Object
}
```

#### TransferStatus Enum
```javascript
const TransferStatus = {
  // Initial States
  TRANSFER_PENDING: "TRANSFER_PENDING",
  TRANSFER_QUEUED: "TRANSFER_QUEUED",
  TRANSFER_PROCESSING: "TRANSFER_PROCESSING",
  
  // Execution States
  TRANSFER_SUBMITTED: "TRANSFER_SUBMITTED",
  TRANSFER_CONFIRMING: "TRANSFER_CONFIRMING",
  TRANSFER_CONFIRMED: "TRANSFER_CONFIRMED",
  
  // Success State
  TRANSFER_COMPLETED: "TRANSFER_COMPLETED",
  
  // Error States
  TRANSFER_FAILED: "TRANSFER_FAILED",
  BLOCKCHAIN_ERROR: "BLOCKCHAIN_ERROR",
  INSUFFICIENT_BALANCE: "INSUFFICIENT_BALANCE",
  CONTRACT_ERROR: "CONTRACT_ERROR",
  WALLET_ERROR: "WALLET_ERROR",
  
  // Admin States
  TRANSFER_MANUAL_REVIEW: "TRANSFER_MANUAL_REVIEW",
  TRANSFER_ADMIN_PROCESSING: "TRANSFER_ADMIN_PROCESSING"
};
```

### 3. OrderStatusHistory Collection (Audit Trail)

#### Schema Definition
```javascript
{
  _id: ObjectId,
  historyId: String, // UUID, unique index
  orderId: String, // Reference to order, indexed
  orderType: String, // "PAYMENT_ORDER" or "NFT_ORDER"
  
  // Status Change Details
  fromStatus: String, // Previous status
  toStatus: String, // New status
  triggeredBy: String, // enum: "SYSTEM", "WEBHOOK", "ADMIN", "USER"
  triggerSource: String, // webhook_id, admin_user_id, etc.
  reason: String, // Reason for status change
  
  // Context Data
  metadata: {
    webhookEventId: String,
    adminUserId: String,
    errorDetails: Object,
    retryAttempt: Number
  },
  
  timestamp: Date // indexed
}
```

### 4. WebhookEvent Collection (Idempotency)

#### Schema Definition
```javascript
{
  _id: ObjectId,
  stripeEventId: String, // unique index
  eventType: String, // indexed
  paymentOrderId: String, // indexed
  
  // Processing Details
  processedAt: Date,
  processingStatus: String, // "SUCCESS", "FAILED", "RETRY"
  errorDetails: Object,
  retryCount: Number, // default: 0
  
  // Raw Data
  rawEventData: Object, // Full Stripe event payload
  
  createdAt: Date // indexed
}
```

## Database Indexes

### PaymentOrder Indexes
```javascript
// Primary indexes
db.paymentorders.createIndex({ "paymentOrderId": 1 }, { unique: true });
db.paymentorders.createIndex({ "userId": 1 });
db.paymentorders.createIndex({ "paymentStatus": 1 });
db.paymentorders.createIndex({ "createdAt": 1 });

// Stripe integration indexes
db.paymentorders.createIndex({ "stripeSessionId": 1 }, { unique: true, sparse: true });
db.paymentorders.createIndex({ "stripePaymentIntentId": 1 }, { unique: true, sparse: true });

// Compound indexes for queries
db.paymentorders.createIndex({ "paymentStatus": 1, "createdAt": 1 });
db.paymentorders.createIndex({ "userId": 1, "createdAt": -1 });
db.paymentorders.createIndex({ "productType": 1, "paymentStatus": 1 });

// Standard index for expiration queries
db.paymentorders.createIndex({ "expiresAt": 1 });
```

### NFTSaleOrder Indexes
```javascript
// Primary indexes
db.nftsaleorders.createIndex({ "nftOrderId": 1 }, { unique: true });
db.nftsaleorders.createIndex({ "paymentOrderId": 1 });
db.nftsaleorders.createIndex({ "transferStatus": 1 });
db.nftsaleorders.createIndex({ "userWalletAddress": 1 });

// Blockchain indexes
db.nftsaleorders.createIndex({ "blockchainTxHash": 1 }, { unique: true, sparse: true });
db.nftsaleorders.createIndex({ "tokenId": 1, "contractAddress": 1 });

// Compound indexes
db.nftsaleorders.createIndex({ "transferStatus": 1, "createdAt": 1 });
db.nftsaleorders.createIndex({ "userWalletAddress": 1, "createdAt": -1 });
```

## Data Relationships

### One-to-One Relationship
```javascript
// PaymentOrder (1) ←→ (1) NFTSaleOrder
// Relationship via paymentOrderId foreign key

// Example query to get complete order data
const completeOrder = await db.paymentorders.aggregate([
  { $match: { paymentOrderId: "pay_ord_123" } },
  {
    $lookup: {
      from: "nftsaleorders",
      localField: "paymentOrderId",
      foreignField: "paymentOrderId",
      as: "nftOrder"
    }
  },
  { $unwind: "$nftOrder" }
]);
```

## Validation Rules

### PaymentOrder Validation
```javascript
const paymentOrderSchema = {
  paymentOrderId: { type: String, required: true, match: /^pay_ord_[a-zA-Z0-9]+$/ },
  userId: { type: String, required: true, match: /^0x[a-fA-F0-9]{40}$/ },
  amount: { type: Number, required: true, min: 0.01, max: 10000 },
  currency: { type: String, required: true, enum: ["USD", "EUR", "GBP"] },
  paymentStatus: { type: String, required: true, enum: Object.values(PaymentStatus) },
  productType: { type: String, required: true, enum: ["NFT", "SUBSCRIPTION"] }
};
```

### NFTSaleOrder Validation
```javascript
const nftSaleOrderSchema = {
  nftOrderId: { type: String, required: true, match: /^nft_ord_[a-zA-Z0-9]+$/ },
  paymentOrderId: { type: String, required: true, match: /^pay_ord_[a-zA-Z0-9]+$/ },
  tokenId: { type: String, required: true },
  userWalletAddress: { type: String, required: true, match: /^0x[a-fA-F0-9]{40}$/ },
  transferStatus: { type: String, required: true, enum: Object.values(TransferStatus) },
  contractAddress: { type: String, required: true, match: /^0x[a-fA-F0-9]{40}$/ }
};
```

## Status Transition Rules

### Valid Payment Transitions
```javascript
const validPaymentTransitions = {
  CREATED: ["SESSION_CREATED"],
  SESSION_CREATED: ["PAYMENT_PENDING", "PAYMENT_EXPIRED"],
  PAYMENT_PENDING: ["PAYMENT_PROCESSING", "PAYMENT_CANCELLED"],
  PAYMENT_PROCESSING: ["PAYMENT_SUCCEEDED", "PAYMENT_FAILED"],
  PAYMENT_FAILED: ["PAYMENT_MANUAL_REVIEW"],
  PAYMENT_SUCCEEDED: [] // Terminal state
};
```

### Valid Transfer Transitions
```javascript
const validTransferTransitions = {
  TRANSFER_PENDING: ["TRANSFER_QUEUED"],
  TRANSFER_QUEUED: ["TRANSFER_PROCESSING"],
  TRANSFER_PROCESSING: ["TRANSFER_SUBMITTED", "TRANSFER_FAILED"],
  TRANSFER_SUBMITTED: ["TRANSFER_CONFIRMING", "CONTRACT_ERROR"],
  TRANSFER_CONFIRMING: ["TRANSFER_CONFIRMED", "BLOCKCHAIN_ERROR"],
  TRANSFER_CONFIRMED: ["TRANSFER_COMPLETED"],
  TRANSFER_FAILED: ["TRANSFER_MANUAL_REVIEW", "TRANSFER_PROCESSING"], // Retry
  TRANSFER_COMPLETED: [] // Terminal state
};
```

## Query Examples

### Get User Order History
```javascript
const userOrders = await db.paymentorders.aggregate([
  { $match: { userId: "0x742d35Cc6634C0532925a3b8D4C9db96590b5" } },
  { $sort: { createdAt: -1 } },
  { $limit: 20 },
  {
    $lookup: {
      from: "nftsaleorders",
      localField: "paymentOrderId",
      foreignField: "paymentOrderId",
      as: "nftOrder"
    }
  }
]);
```

### Get Orders Requiring Manual Review
```javascript
const manualReviewOrders = await db.nftsaleorders.find({
  transferStatus: { $in: ["TRANSFER_MANUAL_REVIEW", "TRANSFER_FAILED"] },
  transferRetryCount: { $gte: 5 }
}).sort({ updatedAt: 1 });
```

### Get Payment Success Rate
```javascript
const successRate = await db.paymentorders.aggregate([
  {
    $group: {
      _id: null,
      total: { $sum: 1 },
      successful: {
        $sum: { $cond: [{ $eq: ["$paymentStatus", "PAYMENT_SUCCEEDED"] }, 1, 0] }
      }
    }
  },
  {
    $project: {
      successRate: { $multiply: [{ $divide: ["$successful", "$total"] }, 100] }
    }
  }
]);
```

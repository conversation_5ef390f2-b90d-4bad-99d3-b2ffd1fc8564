# Stripe Integration Implementation Checklist

## Related Documentation
- **Integration Plan**: See `.local/whisky-nft-sale/stripe-integration-plan.md` for high-level architecture and flow
- **API Contracts**: See `.local/whisky-nft-sale/stripe-payment-api-contracts.md` for detailed API specifications

## Implementation Overview
This checklist implements the Stripe payment integration following the API contracts defined in the contracts document. All endpoints, request/response formats, and error handling must match the contract specifications exactly.

## Phase 1: Enhanced Stripe Gateway Service

### Pre-Implementation Setup
- [ ] Review existing stripe-gateway service structure
- [ ] Analyze current Stripe integration patterns
- [ ] Identify files that need modification vs. new files
- [ ] Set up development environment for stripe-gateway

### Step 1: Enhance existing stripe-gateway with order management

#### 1.1 Data Models & Interfaces
- [ ] Create `src/common/interfaces/payment-order.interface.ts`
- [ ] Create `src/common/interfaces/nft-sale-order.interface.ts`
- [ ] Create `src/common/enums/payment-status.enum.ts`
- [ ] Create `src/common/enums/transfer-status.enum.ts`
- [ ] Create `src/common/enums/product-type.enum.ts`
- [ ] Create `src/common/dto/create-payment-session.dto.ts`
- [ ] Create `src/common/dto/payment-completion-notification.dto.ts`
- [ ] Create `src/common/dto/bulk-status-query.dto.ts`
- [ ] Create `src/common/dto/payment-status-response.dto.ts`

#### 1.2 Database Schemas
- [ ] Create `src/schemas/payment-order.schema.ts` (MongoDB schema)
- [ ] Create `src/schemas/nft-sale-order.schema.ts` (MongoDB schema)
- [ ] Add database indexes for performance optimization
- [ ] Test schema validation and constraints

#### 1.3 Order Management Module
- [ ] Create `src/orders/orders.module.ts`
- [ ] Create `src/orders/orders.service.ts`
- [ ] Create `src/orders/orders.controller.ts`
- [ ] Create `src/orders/dto/` directory with DTOs
- [ ] Implement CRUD operations for PaymentOrder
- [ ] Implement status transition validation
- [ ] Add order expiration handling

### Step 2: Implement Stripe Checkout Session creation

#### 2.1 Enhanced Stripe Service
- [ ] Update `src/stripe/stripe.service.ts` with new methods
- [ ] Add `createCheckoutSession()` method
- [ ] Add session configuration for different product types
- [ ] Implement session metadata handling
- [ ] Add error handling for session creation

#### 2.2 Internal API Controller
- [ ] Create `src/internal/internal.controller.ts`
- [ ] Add `POST /internal/payments/create-session` endpoint
- [ ] Add `GET /internal/payments/{paymentOrderId}/status` endpoint
- [ ] Add `POST /internal/payments/notify-completion` endpoint
- [ ] Add `POST /internal/payments/bulk-status` endpoint
- [ ] Implement X-API-Key authentication middleware
- [ ] Add service discovery middleware for ECS communication
- [ ] Add request validation and sanitization
- [ ] Implement proper error responses with standardized format
- [ ] Add logging for debugging and audit trail

#### 2.3 Integration with Order Management
- [ ] Link Stripe sessions with PaymentOrder records
- [ ] Store session IDs in database
- [ ] Implement session expiration handling
- [ ] Add session status tracking

### Step 3: Add webhook handling for payment events

#### 3.1 Webhook Module
- [ ] Create `src/webhooks/webhooks.module.ts`
- [ ] Create `src/webhooks/webhooks.service.ts`
- [ ] Create `src/webhooks/webhooks.controller.ts`
- [ ] Add webhook signature verification
- [ ] Implement idempotency handling

#### 3.2 Webhook Event Handlers
- [ ] Handle `checkout.session.completed` event
- [ ] Handle `checkout.session.expired` event
- [ ] Handle `payment_intent.succeeded` event
- [ ] Handle `payment_intent.payment_failed` event
- [ ] Handle `payment_intent.canceled` event
- [ ] Add error handling for webhook processing
- [ ] Implement webhook event deduplication
- [ ] Add webhook event logging and audit trail

#### 3.3 Order Status Updates
- [ ] Update PaymentOrder status based on webhook events
- [ ] Trigger NFT transfer initiation on payment success
- [ ] Implement retry logic for failed webhook processing
- [ ] Add webhook event logging and audit trail

### Step 4: Create order tracking and status management

#### 4.1 Status Management Service
- [ ] Create `src/status/status.service.ts`
- [ ] Implement status transition validation
- [ ] Add status history tracking
- [ ] Create status change event emitters

#### 4.2 Order Tracking APIs
- [ ] Implement `GET /internal/payments/{paymentOrderId}/status` endpoint
- [ ] Add `POST /internal/payments/bulk-status` endpoint for batch queries
- [ ] Implement order status aggregation with payment and transfer details
- [ ] Add proper error handling for not found orders
- [ ] Add rate limiting for status queries

#### 4.3 Admin Management APIs
- [ ] Add `POST /admin/orders/{orderId}/status` endpoint
- [ ] Add `GET /admin/orders/pending` endpoint
- [ ] Add `POST /admin/orders/{orderId}/notes` endpoint
- [ ] Implement admin intervention workflows

### Step 5: Add environment-specific Stripe configuration

#### 5.1 Configuration Management
- [ ] Update `src/config/stripe.config.ts`
- [ ] Add environment-specific Stripe keys (sandbox/live)
- [ ] Configure webhook endpoints per environment
- [ ] Add X-API-Key configuration for service-to-service auth
- [ ] Add ECS Service Discovery configuration
- [ ] Add CloudMap service registration settings
- [ ] Add configuration validation and environment checks
- [ ] Configure success/cancel URLs per environment

#### 5.2 Environment Setup
- [ ] Configure development environment (sandbox)
- [ ] Configure testnet environment (sandbox)
- [ ] Configure production environment (live)
- [ ] Set up environment-specific webhook URLs
- [ ] Configure ECS Service Discovery per environment
- [ ] Set up CloudMap private DNS namespace (`dcasks.local`)
- [ ] Configure service registration for each environment

#### 5.3 Security & Validation
- [ ] Implement X-API-Key validation middleware
- [ ] Add webhook signature verification using Stripe webhook secret
- [ ] Configure CORS for different environments
- [ ] Add rate limiting and security headers
- [ ] Implement request ID tracking for audit trail
- [ ] Add input validation for all API endpoints
- [ ] Implement proper error response format with error codes
- [ ] Configure security groups for ECS service communication
- [ ] Add service discovery authentication validation

### Testing & Validation

#### 5.4 Unit Tests
- [ ] Write tests for OrdersService
- [ ] Write tests for StripeService enhancements
- [ ] Write tests for WebhooksService
- [ ] Write tests for status transitions
- [ ] Achieve >80% code coverage

#### 5.5 Integration Tests
- [ ] Test Stripe session creation flow
- [ ] Test webhook event processing
- [ ] Test order status transitions
- [ ] Test error handling scenarios

#### 5.6 End-to-End Testing
- [ ] Test complete payment flow with Stripe sandbox
- [ ] Test webhook delivery and processing
- [ ] Test order expiration handling
- [ ] Test admin intervention workflows

#### 5.7 API Contract Compliance Testing
- [ ] Test all internal API endpoints match contract specifications
- [ ] Validate request/response formats against API contracts
- [ ] Test error responses match standardized error codes
- [ ] Test authentication and authorization flows
- [ ] Validate GraphQL schema matches contract definitions
- [ ] Test service-to-service communication flows

### ECS Service Discovery Setup

#### 5.7 CloudMap Configuration
- [ ] Create private DNS namespace `dcasks.local`
- [ ] Configure CloudMap service for stripe-gateway
- [ ] Configure CloudMap service for dcasks-backend
- [ ] Set up health check endpoints (`/health`)
- [ ] Configure service discovery policies
- [ ] Test service registration and deregistration

#### 5.8 ECS Service Configuration
- [ ] Update ECS task definitions with service discovery
- [ ] Configure service discovery in ECS service definitions
- [ ] Set up security groups for inter-service communication
- [ ] Configure load balancing for service discovery
- [ ] Test service-to-service communication via CloudMap
- [ ] Validate X-API-Key authentication between services

### Documentation & Deployment

#### 5.9 Documentation
- [ ] Update API documentation with ECS service discovery
- [ ] Document new endpoints and schemas
- [ ] Create ECS deployment guide
- [ ] Update environment configuration docs
- [ ] Document service discovery configuration

#### 5.10 Deployment Preparation
- [ ] Update Docker configuration if needed
- [ ] Prepare environment variables including X-API-Keys
- [ ] Update CI/CD pipeline for ECS service discovery
- [ ] Create database migration scripts
- [ ] Prepare CloudMap service registration scripts

## Phase 2: DCasks Backend GraphQL Integration

### Step 1: GraphQL Schema & Types
- [ ] Create `src/payments/dto/create-nft-order-input.dto.ts`
- [ ] Create `src/payments/entities/nft-order.entity.ts`
- [ ] Create `src/payments/entities/payment-order.entity.ts`
- [ ] Define GraphQL schema for NFT order operations
- [ ] Add PaymentMethod enum (STRIPE, CRYPTO)
- [ ] Add order status types and enums

### Step 2: GraphQL Resolvers
- [ ] Create `src/payments/payments.resolver.ts`
- [ ] Implement `createNFTOrder` mutation
- [ ] Implement `nftOrder` query
- [ ] Implement `userOrders` query with pagination
- [ ] Add proper error handling and validation
- [ ] Add authentication guards for wallet-based auth

### Step 3: Payment Service Integration
- [ ] Create `src/payments/payments.service.ts`
- [ ] Implement Stripe Gateway API client with ECS service discovery
- [ ] Add X-API-Key authentication for internal API calls
- [ ] Add order creation and management logic
- [ ] Implement NFT transfer triggering
- [ ] Add order status tracking and updates
- [ ] Implement webhook notification handling

### Step 4: Database Integration
- [ ] Create MongoDB schemas for NFT orders
- [ ] Add database indexes for performance
- [ ] Implement order status history tracking
- [ ] Add proper data validation and constraints
- [ ] Test database operations and queries

## Success Criteria
- [ ] All internal API endpoints respond correctly with X-API-Key authentication
- [ ] ECS Service Discovery works correctly for service-to-service communication
- [ ] GraphQL mutations and queries work as specified in contracts
- [ ] Stripe integration works in sandbox environment
- [ ] Webhook processing is reliable and idempotent
- [ ] Order status tracking is accurate across both services
- [ ] Error handling follows standardized error codes
- [ ] Service-to-service communication via CloudMap is secure and reliable
- [ ] Health checks and service registration work properly
- [ ] Code follows existing patterns and standards
- [ ] All tests pass including integration tests
- [ ] Documentation is complete and accurate

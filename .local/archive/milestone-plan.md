# Milestone Plan: Sailing Whisky NFT Sale (v1)

## Problem Statement
Implement a comprehensive NFT sale system for the Sailing Whisky collection featuring a time-based sale with queue management, dual payment options (crypto via NOWPayments and card via Stripe), and automated NFT distribution. The system must handle limited concurrent users (100 max) with fair FIFO queue processing and batch management for optimal user experience.

## Requirements Analysis

### Functional Requirements
- **FR1**: Time-based sale system with configurable start time stored in database
- **FR2**: FIFO queue system limited to 100 concurrent users with crypto wallet authentication
- **FR3**: Batch processing of 10 users simultaneously with NFT locking mechanism
- **FR4**: Dual payment options: crypto (via NOWPayments) and Stripe (both with deferred NFT transfer)
- **FR5**: 5-minute NFT selection window + 5-minute payment completion window per user
- **FR6**: Real-time queue position updates via polling mechanism
- **FR7**: Persistent session storage in database for user state management
- **FR8**: NFT unlock and pool return mechanism for failed/expired payments
- **FR9**: Integration with existing dcasks-backend API and job services
- **FR10**: Enhanced Payment Gateway service supporting both Stripe and NOWPayments integration

### Non-Functional Requirements
- **Performance**: Queue position updates within 2 seconds, payment processing within 5 seconds
- **Scalability**: Support exactly 100 concurrent users with 10-user batch processing
- **Reliability**: 99.9% queue management uptime with persistent session recovery
- **Usability**: Seamless flow across landing → queue → selection → payment pages
- **Security**: Crypto wallet authentication, secure payment processing, NFT transfer validation

## Architecture Overview

The NFT Sale milestone consists of three interconnected epics that work together to provide a complete time-based NFT sale experience with queue management and dual payment options.

### System Components
- **Capcom Frontend**: Three-page flow (landing, queue, NFT selection/payment)
- **DCasks Backend**: Enhanced API and job services for queue and payment management
- **DCasks Job Service**: Batch processing, queue management, and payment monitoring
- **Enhanced Payment Gateway**: Unified service handling both Stripe (card) and NOWPayments (crypto) processing
- **Admin Wallet Smart Contract**: Simple NFT transfer functionality for post-payment distribution
- **Shared Database**: Queue state, session storage, sale configuration, and NFT inventory

### High-Level System Architecture

```mermaid
graph TB
    %% External Systems
    User[👤 User with Crypto Wallet]
    Blockchain[🔗 Blockchain Network]
    Stripe[💳 Stripe Payment Gateway]
    NOWPayments[🪙 NOWPayments Crypto Gateway]

    %% Frontend Components (Capcom Website)
    subgraph "Epic 1: Landing Page"
        LandingPage[🏠 Landing Page<br/>- Sale info & countdown<br/>- Wallet connection<br/>- Queue entry]
    end

    subgraph "Epic 2: Queue & Sale Management"
        QueuePage[⏳ Queue Management Page<br/>- Position display<br/>- Real-time updates<br/>- Batch processing status]
        NFTSelection[🎯 NFT Selection & Payment<br/>- Available NFTs display<br/>- Lock mechanism<br/>- Payment method choice]
    end

    %% Backend Services (Reusing existing dcasks infrastructure)
    subgraph "DCasks Backend Services"
        API[🔌 DCasks API<br/>- Queue management<br/>- NFT operations<br/>- Payment processing]
        JobService[⚙️ DCasks Job Service<br/>- Queue batch processor<br/>- Payment status monitor<br/>- NFT unlock scheduler]
        Database[(🗄️ Shared Database<br/>- Queue state<br/>- Session storage<br/>- Sale configuration<br/>- NFT inventory)]
    end

    %% Payment & Smart Contract Systems
    subgraph "Epic 3: Payment Integration"
        PaymentGateway[💰 Payment Gateway Service<br/>- Stripe integration<br/>- NOWPayments integration<br/>- Unified payment processing]
        AdminWallet[📜 Admin Wallet Smart Contract<br/>- NFT transfer functionality<br/>- Post-payment distribution]
        StripeGateway[� Stripe Gateway Service<br/>- Card payment processing<br/>- Payment confirmation]
    end

    %% User Flow Connections
    User -->|1.Connect Wallet| LandingPage
    LandingPage -->|2.Join Queue| QueuePage
    QueuePage -->|3.Enter Batch| NFTSelection
    NFTSelection -->|4a.Crypto Payment| PaymentGateway
    NFTSelection -->|4b.Card Payment| PaymentGateway

    %% Backend Flow Connections
    LandingPage <-->|Auth & Queue Status| API
    QueuePage <-->|Position Updates via Polling| API
    NFTSelection <-->|NFT Lock/Unlock & Payment Init| API

    API <--> Database
    JobService <--> Database
    JobService -->|Batch Processing & Queue Management| API
    JobService -->|Monitor Payments & Unlock Timeouts| PaymentGateway

    %% External Integrations
    PaymentGateway <--> Stripe
    PaymentGateway <--> NOWPayments
    AdminWallet <--> Blockchain
    API -->|NFT Transfer After Payment Confirmation| AdminWallet

    %% Configuration Flow
    Database -->|Sale Timing & Batch Size Config| JobService
```

## Epic Breakdown

### Epic 1: Landing Page
**Purpose**: Provide sale information and entry point to the queue system

**Key Components**:
- Sale countdown timer with database-driven start time
- Crypto wallet connection interface
- Queue entry mechanism with authentication
- Pre-sale vs active sale content switching

**Integration Points**:
- DCasks API for sale configuration and queue status
- Wallet authentication system
- Queue management service

### Epic 2: Queue & Sale Management
**Purpose**: Manage FIFO queue with batch processing and NFT selection

**Key Components**:
- Queue position tracking with real-time polling updates
- Batch processing system (10 users simultaneously)
- NFT selection interface with locking mechanism
- Session management with persistent storage
- Payment window enforcement (5min selection + 5min payment)

**Integration Points**:
- DCasks Job Service for batch processing
- Database for queue state and session persistence
- NFT inventory management system
- Payment initiation services

### Epic 3: Payment Integration
**Purpose**: Handle dual payment methods with unified payment processing and deferred NFT distribution

**Key Components**:
- Enhanced Payment Gateway service supporting both Stripe and NOWPayments
- Payment method selection interface (card vs crypto)
- Unified payment session management (20min timeout for NOWPayments, configurable for Stripe)
- NFT transfer coordination via Admin Wallet after payment confirmation

**Integration Points**:
- NOWPayments API for crypto payment processing with auto-conversion to target currency
- Stripe payment gateway for card payments
- DCasks Backend for payment orchestration and NFT transfer coordination
- Admin Wallet smart contract for post-payment NFT transfers
- Webhook systems for both Stripe and NOWPayments payment confirmations

## Data Flow Summary

1. **Pre-Sale Phase**: User visits landing page, sees countdown and sale information
2. **Queue Entry**: User connects wallet and joins FIFO queue (max 100 users)
3. **Queue Management**: Job service processes queue in batches of 10 users
4. **NFT Selection**: User gets 5-minute window to select and lock NFT
5. **Payment Processing**: User chooses crypto (NOWPayments) or card (Stripe) payment
6. **NFT Distribution**: Both payment methods trigger backend NFT transfer via Admin Wallet after confirmation
7. **Session Cleanup**: Failed/expired payments unlock NFTs and return to pool

## Key Architectural Decisions

1. **Reuse Existing Infrastructure**: Leverage dcasks-api and dcasks-job services for consistency
2. **Polling-Based Updates**: Simple real-time updates without WebSocket complexity
3. **Database-Driven Configuration**: Sale timing and batch size stored in database for flexibility
4. **Persistent Session Storage**: Database-backed session management for reliability
5. **NFT Locking Mechanism**: Prevent overselling during payment processing windows
6. **Unified Payment Architecture**: Single Payment Gateway service handling both NOWPayments (crypto) and Stripe (card) with consistent deferred NFT transfer
7. **Batch Processing**: 10 simultaneous users to balance system load and user experience

## Technical Constraints

- **Concurrent User Limit**: Exactly 100 users maximum in queue
- **Batch Size**: 10 users processed simultaneously
- **Time Windows**: 5 minutes for NFT selection + 5 minutes for payment completion
- **Polling Frequency**: Real-time updates via polling (no WebSockets)
- **Session Persistence**: Database storage for user sessions and queue state
- **Payment Integration**: Enhanced Payment Gateway service with NOWPayments and Stripe integration
- **Infrastructure**: Deploy on existing ECS infrastructure with dcasks services

## Integration Requirements

- **Frontend**: Three-page Capcom website flow
- **Backend**: Enhanced dcasks-backend API with queue management
- **Job Processing**: Enhanced dcasks-job service for batch and payment monitoring
- **Smart Contract**: Enhanced Admin Wallet contract for NFT transfers
- **Payment**: Enhanced Payment Gateway service with NOWPayments and Stripe integration
- **Database**: Shared MongoDB for all state management
- **Deployment**: AWS ECS following existing deployment patterns

## Success Criteria

- **Queue Management**: 100% accurate FIFO queue processing with position tracking
- **Batch Processing**: Smooth 10-user batch transitions with minimal wait times
- **Payment Success**: 99%+ payment completion rate for both NOWPayments (crypto) and Stripe (card)
- **NFT Distribution**: 100% accurate NFT transfers with no double-spending
- **Session Recovery**: Persistent session state across browser refreshes/reconnects
- **Performance**: < 2 second queue updates, < 5 second payment processing
- **User Experience**: Intuitive flow with clear status indicators and error handling

## Architecture Documentation

- **v1 (This Document)**: Epic-based architecture focusing on project milestones and user journey
- **v2 (Technical Architecture)**: Service-oriented architecture in `system-architecture-v2.md` focusing on technical implementation, service boundaries, and integration patterns

## Next Steps

1. **Detailed Epic Planning**: Create specific implementation plans for each epic
2. **API Design**: Define detailed API contracts between components (see v2 architecture)
3. **Database Schema**: Design queue, session, and NFT inventory data models
4. **Payment Gateway Enhancement**: Define NOWPayments integration requirements and unified payment processing
5. **Frontend Wireframes**: Create detailed UI/UX designs for three-page flow
6. **Testing Strategy**: Plan comprehensive testing approach for queue and payment flows

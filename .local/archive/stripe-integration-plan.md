# Project Plan: Stripe Payment Gateway Integration

## Problem Statement
Integrate Stripe as a general payment gateway service into the dcasks-mono ecosystem to enable credit card and digital wallet payments across multiple apps (starting with Capcom frontend). This will complement the existing blockchain payment system and provide users with traditional payment options for NFT purchases.

## Related Documentation
- **API Contracts**: See `.local/whisky-nft-sale/stripe-payment-api-contracts.md` for detailed API specifications
- **Implementation Checklist**: See `.local/whisky-nft-sale/stripe-integration-implementation-checklist.md` for detailed implementation tasks

## Requirements Analysis

### Functional Requirements
- **FR1**: General payment service usable across multiple apps in DCasks ecosystem
- **FR2**: Support credit/debit cards and digital wallets (Apple Pay/Google Pay)
- **FR3**: One-time payment processing using Stripe Checkout Sessions
- **FR4**: Multi-currency support with USD as base currency
- **FR5**: Order management and payment history tracking
- **FR6**: Integration with existing NFT transfer system (admin wallet → buyer)
- **FR7**: Webhook handling for payment status updates
- **FR8**: Environment-specific Stripe account configuration (sandbox/live)

### Non-Functional Requirements
- **Performance**: Payment processing within 5 seconds, webhook response < 2 seconds
- **Security**: PCI compliance through Stripe, no sensitive payment data storage
- **Scalability**: Support for concurrent payment processing across multiple apps
- **Usability**: Seamless checkout experience with minimal redirects
- **Reliability**: 99.9% payment processing uptime with proper error handling

## Architecture Overview

The integration follows a service-oriented architecture where the existing stripe-gateway service is enhanced to serve as a centralized payment processor for all DCasks applications.

### System Components
- **Capcom Frontend**: Initiates payment requests and handles checkout redirects
- **DCasks Backend**: Orchestrates payment flow, manages order data, and handles NFT transfers
- **Enhanced Stripe Gateway**: Centralized payment processing service
- **Stripe API**: External payment processor

### Data Flow
1. User initiates purchase on Capcom frontend
2. Frontend sends payment request to DCasks Backend
3. DCasks Backend creates order record and calls Stripe Gateway
4. Stripe Gateway creates Checkout Session and returns URL
5. User completes payment on Stripe Checkout
6. Stripe sends webhook to Stripe Gateway
7. Stripe Gateway updates order status and notifies DCasks Backend
8. DCasks Backend triggers NFT transfer from admin wallet to user
9. User receives confirmation and NFT in wallet

### Detailed Payment Flow Sequence

#### Payment Initiation Phase
1. **User** selects NFT product and clicks "Buy Now" on Capcom Frontend
2. **Capcom Frontend** sends GraphQL mutation: `createNFTOrder(input: CreateNFTOrderInput)`
3. **DCasks Backend** validates user and product, creates NFT order record in MongoDB
4. **DCasks Backend** calls Stripe Gateway: `POST /internal/payments/create-session`

#### Stripe Session Creation Phase
5. **Stripe Gateway** creates PaymentOrder record with session mapping in MongoDB
6. **Stripe Gateway** calls Stripe API to create checkout session with metadata
7. **Stripe API** returns session URL and session ID
8. **Stripe Gateway** returns `{paymentOrderId, checkoutUrl, expiresAt}` to DCasks Backend
9. **DCasks Backend** links NFT order with payment order ID in MongoDB
10. **DCasks Backend** returns `{nftOrderId, paymentOrderId, checkoutUrl}` to Capcom Frontend

#### Payment Processing Phase
11. **Capcom Frontend** redirects user to Stripe Checkout page
12. **User** completes payment using card or digital wallet (Apple Pay/Google Pay)
13. **Stripe API** processes payment and shows confirmation to user

#### Webhook & NFT Transfer Phase
14. **Stripe API** sends webhook `checkout.session.completed` to Stripe Gateway
15. **Stripe Gateway** verifies webhook signature and updates payment status to 'PAYMENT_SUCCEEDED'
16. **Stripe Gateway** calls DCasks Backend: `POST /internal/payments/notify-completion`
17. **DCasks Backend** updates NFT order status to 'TRANSFER_QUEUED' in MongoDB
18. **DCasks Backend** executes NFT transfer from admin wallet to user wallet
19. **DCasks Backend** updates transfer status to 'TRANSFER_COMPLETED' in MongoDB

#### Confirmation Phase
20. **Stripe API** redirects user to success page on Capcom Frontend with session_id
21. **Capcom Frontend** queries order status via GraphQL: `nftOrder(nftOrderId: ID!)`
22. **DCasks Backend** returns completed order with payment and transfer details
23. **Capcom Frontend** displays success message and NFT details to user

#### Error Handling Scenarios
- **Payment Failed**: Stripe webhook `payment_intent.payment_failed` → payment status 'PAYMENT_FAILED' → user redirected to failure page
- **NFT Transfer Failed**: DCasks Backend updates transfer status to 'TRANSFER_FAILED' → queued for manual intervention
- **Webhook Delivery Failed**: Stripe Gateway implements retry mechanism with exponential backoff
- **Session Expired**: Payment session expires → status 'PAYMENT_EXPIRED' → user can retry payment
- **Invalid Wallet**: NFT transfer fails due to invalid destination → status 'WALLET_ERROR' → admin review required

### Payment Flow Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Capcom as Capcom Frontend
    participant DCasks as DCasks Backend
    participant Stripe_GW as Stripe Gateway
    participant Stripe_API as Stripe API
    participant MongoDB
    participant User_Wallet as User Wallet

    Note over User, User_Wallet: Payment Initiation Phase
    User->>Capcom: Select NFT & Click "Buy Now"
    Capcom->>DCasks: GraphQL: createNFTOrder(input)
    DCasks->>MongoDB: Create NFT order record
    DCasks->>Stripe_GW: POST /internal/payments/create-session

    Note over Stripe_GW, Stripe_API: Stripe Session Creation
    Stripe_GW->>MongoDB: Create PaymentOrder with session mapping
    Stripe_GW->>Stripe_API: Create checkout session with metadata
    Stripe_API-->>Stripe_GW: Return session URL & ID
    Stripe_GW-->>DCasks: Return {paymentOrderId, checkoutUrl, expiresAt}
    DCasks->>MongoDB: Link NFT order with payment order ID
    DCasks-->>Capcom: Return {nftOrderId, paymentOrderId, checkoutUrl}

    Note over User, Stripe_API: Payment Processing Phase
    Capcom->>User: Redirect to Stripe Checkout
    User->>Stripe_API: Complete payment (card/wallet)
    Stripe_API->>User: Payment confirmation

    Note over Stripe_API, User_Wallet: Webhook & NFT Transfer Phase
    Stripe_API->>Stripe_GW: Webhook: checkout.session.completed
    Stripe_GW->>MongoDB: Update payment status to 'PAYMENT_SUCCEEDED'
    Stripe_GW->>DCasks: POST /internal/payments/notify-completion
    DCasks->>MongoDB: Update transfer status to 'TRANSFER_QUEUED'
    DCasks->>DCasks: Transfer NFT from admin wallet
    DCasks->>User_Wallet: Transfer NFT to buyer wallet
    DCasks->>MongoDB: Update transfer status to 'TRANSFER_COMPLETED'

    Note over User, Capcom: Confirmation Phase
    Stripe_API->>Capcom: Redirect to success page with session_id
    Capcom->>DCasks: GraphQL: nftOrder(nftOrderId)
    DCasks->>MongoDB: Query NFT order with payment details
    MongoDB-->>DCasks: Return completed order with transfer details
    DCasks-->>Capcom: Order completed + NFT transferred + blockchain tx
    Capcom->>User: Show success & NFT details with transaction hash

    Note over User, User_Wallet: Error Handling (Alternative Flow)
    alt Payment Failed
        Stripe_API->>Stripe_GW: Webhook: payment_intent.payment_failed
        Stripe_GW->>MongoDB: Update payment status to 'PAYMENT_FAILED'
        Stripe_API->>Capcom: Redirect to failure page
        Capcom->>User: Show payment failed message
    end

    alt NFT Transfer Failed
        DCasks->>MongoDB: Update transfer status to 'TRANSFER_FAILED'
        DCasks->>DCasks: Queue for manual intervention
        Note over DCasks: Admin can retry transfer manually
    end

    alt Session Expired
        Stripe_GW->>MongoDB: Update payment status to 'PAYMENT_EXPIRED'
        Capcom->>User: Show session expired, allow retry
    end
```

### Updated API Design

#### Internal Service APIs (Stripe Gateway)
**Base URL**: `https://dev-stripe.dcasks.co` (dev) | `https://stripe.dcasks.co` (prod)
**Service Discovery**: AWS ECS Service Discovery via CloudMap (`stripe-gateway.dcasks.local`)
**Authentication**: Service-to-service API key in `X-API-Key: {service_api_key}`

- **POST** `/internal/payments/create-session` - Create Stripe checkout session
- **GET** `/internal/payments/{paymentOrderId}/status` - Get payment order status
- **POST** `/internal/payments/notify-completion` - Payment completion notification
- **POST** `/internal/payments/bulk-status` - Bulk payment status query
- **POST** `/webhooks/stripe` - Handle Stripe webhooks (external endpoint)

#### DCasks Backend GraphQL APIs
**Base URL**: `https://dev-api-ecs.dcasks.co/graphql` (dev) | `https://api-ecs.dcasks.co/graphql` (prod)
**Service Discovery**: AWS ECS Service Discovery via CloudMap (`dcasks-backend.dcasks.local`)
**Authentication**: Wallet signature authentication with JWT token

- **Mutation** `createNFTOrder(input: CreateNFTOrderInput)` - Create NFT sale order
- **Query** `nftOrder(nftOrderId: ID!)` - Get NFT order details
- **Query** `userOrders(userId: ID!, limit: Int, offset: Int)` - Get user's orders with pagination

#### Service Communication Flow
1. **Capcom Frontend** → **DCasks Backend** (GraphQL with JWT)
2. **DCasks Backend** → **Stripe Gateway** (Internal API with X-API-Key via ECS Service Discovery)
3. **Stripe Gateway** → **DCasks Backend** (Internal API notification with X-API-Key via ECS Service Discovery)
4. **Stripe API** → **Stripe Gateway** (Webhook with signature verification)

## Tech Stack
- **Frontend**: Next.js 15 with App Router (existing Capcom setup)
- **Backend**: NestJS with GraphQL (existing DCasks Backend) + Enhanced Stripe Gateway
- **Database**: MongoDB (existing) for order and payment history storage
- **Payment Processing**: Stripe Checkout Sessions with webhooks
- **Authentication**: Existing wallet-based auth system + X-API-Key for service-to-service
- **Service Discovery**: AWS ECS Service Discovery with CloudMap
- **Testing**: Jest for unit tests, Playwright for E2E payment flows
- **Deployment**: AWS ECS (following existing deployment patterns)
- **Monitoring**: CloudWatch logs with Stripe Dashboard integration

## ECS Service Discovery Architecture

### CloudMap Configuration
- **Private DNS Namespace**: `dcasks.local`
- **Service Registration**: Automatic via ECS service configuration
- **Health Checks**: HTTP health checks on `/health` endpoint
- **Service Names**:
  - `stripe-gateway.dcasks.local` - Stripe Gateway service
  - `dcasks-backend.dcasks.local` - DCasks Backend service

### Service-to-Service Communication
- **Discovery Method**: DNS-based service discovery via CloudMap
- **Network Security**: Private VPC subnets with security group restrictions
- **Authentication**: X-API-Key header validation between services
- **Load Balancing**: Automatic round-robin across healthy service instances
- **Failover**: Automatic service deregistration on health check failures

### Benefits
- **Simplified Configuration**: No hardcoded service URLs
- **High Availability**: Automatic failover and load balancing
- **Security**: Private network communication with API key validation
- **Scalability**: Dynamic service registration for auto-scaling
- **Monitoring**: CloudWatch integration for service discovery metrics

## File Structure

```
apps/
├── stripe-gateway/                    # Enhanced existing service
│   ├── src/
│   │   ├── internal/                  # New internal API module
│   │   │   ├── internal.controller.ts # Service-to-service endpoints
│   │   │   ├── internal.service.ts    # Internal API logic
│   │   │   ├── internal.module.ts
│   │   │   └── dto/
│   │   │       ├── create-payment-session.dto.ts
│   │   │       ├── payment-completion-notification.dto.ts
│   │   │       ├── bulk-status-query.dto.ts
│   │   │       └── payment-status-response.dto.ts
│   │   ├── stripe/
│   │   │   ├── stripe.controller.ts   # Enhanced with session creation
│   │   │   ├── stripe.service.ts      # Enhanced payment logic
│   │   │   └── stripe.module.ts
│   │   ├── orders/                    # New module
│   │   │   ├── orders.controller.ts
│   │   │   ├── orders.service.ts
│   │   │   ├── orders.module.ts
│   │   │   └── dto/
│   │   ├── webhooks/                  # New module
│   │   │   ├── webhooks.controller.ts # External webhook endpoint
│   │   │   ├── webhooks.service.ts
│   │   │   └── webhooks.module.ts
│   │   └── common/
│   │       ├── interfaces/
│   │       ├── enums/
│   │       ├── guards/                # X-API-Key auth guards
│   │       ├── middleware/            # Service discovery middleware
│   │       └── config/
│   └── deployment/                    # ECS deployment configs
│       ├── ecs-service-definition.json
│       ├── cloudmap-service.json
│       └── task-definition.json
├── dcasks-backend/
│   └── src/
│       └── payments/                  # New GraphQL module
│           ├── payments.resolver.ts   # GraphQL mutations/queries
│           ├── payments.service.ts    # Business logic
│           ├── payments.module.ts
│           ├── dto/
│           │   └── create-nft-order-input.dto.ts
│           ├── entities/
│           │   ├── nft-order.entity.ts
│           │   └── payment-order.entity.ts
│           └── clients/
│               └── stripe-gateway.client.ts # Internal API client
└── capcom/
    └── src/
        ├── components/
        │   └── payment/               # Payment UI components
        ├── hooks/
        │   └── usePayment.ts         # Payment logic hook
        ├── services/
        │   └── payment.service.ts     # GraphQL API communication
        └── types/
            └── payment.types.ts       # TypeScript types for API
```

## Refactored Data Models

### 1. Generic Payment Order Model (Base)
This model handles all generic payment processing with Stripe and can be reused across different product types.

```typescript
interface PaymentOrder {
  // Core Identifiers
  paymentOrderId: string (UUID) - Unique payment order identifier
  userId: string - User identifier (wallet address or user ID)

  // Product Information (Generic)
  productType: enum - Type of product (NFT, SUBSCRIPTION, PHYSICAL_GOODS, DIGITAL_CONTENT)
  productId: string - Reference to specific product
  productMetadata: object - Flexible product-specific data

  // Payment Details
  amount: number - Payment amount in base currency
  currency: string - Payment currency (USD, EUR, etc.)

  // Stripe Integration
  stripeSessionId: string - Stripe checkout session ID
  stripePaymentIntentId: string - Stripe payment intent ID
  stripeChargeId: string - Stripe charge ID after successful payment
  stripeCustomerId: string - Stripe customer ID (for future use)

  // Payment Status Tracking
  paymentStatus: enum - Payment-specific status (see PaymentStatus enum)
  paymentRetryCount: number - Payment retry attempts
  paymentLastRetryAt: Date - Last payment retry timestamp
  paymentErrorDetails: object - Payment-specific error information

  // Timestamps
  createdAt: Date
  updatedAt: Date
  paymentCompletedAt: Date - When payment was confirmed
  expiresAt: Date - Payment session expiration

  // Admin & Audit
  adminNotes: string - Admin notes for manual intervention
  metadata: object - Additional flexible data
}
```

### 2. NFT Sale Order Model (Specialized)
This model extends the payment order for NFT-specific functionality.

```typescript
interface NFTSaleOrder {
  // Core Identifiers
  nftOrderId: string (UUID) - Unique NFT order identifier
  paymentOrderId: string - Foreign key to PaymentOrder

  // NFT-Specific Information
  tokenId: string - NFT token ID (for pre-minted NFTs)
  contractAddress: string - NFT contract address
  nftType: enum - Specific NFT type (WHISKY_NFT, CASK_NFT, BOTTLE_NFT)

  // Wallet Information
  userWalletAddress: string - Destination wallet for NFT transfer
  adminWalletAddress: string - Source wallet (admin wallet)

  // Blockchain Transaction Details
  blockchainTxHash: string - Transaction hash for NFT transfer
  blockchainConfirmations: number - Number of confirmations received
  gasUsed: number - Gas consumed for transaction
  gasPrice: string - Gas price in wei
  blockNumber: number - Block where transaction was mined

  // NFT Transfer Status Tracking
  transferStatus: enum - NFT transfer-specific status (see TransferStatus enum)
  transferRetryCount: number - Transfer retry attempts
  transferLastRetryAt: Date - Last transfer retry timestamp
  transferErrorDetails: object - Transfer-specific error information

  // Timestamps
  createdAt: Date
  updatedAt: Date
  transferInitiatedAt: Date - When NFT transfer started
  transferCompletedAt: Date - When NFT transfer confirmed

  // NFT Metadata
  nftMetadata: object - NFT-specific metadata (rarity, attributes, etc.)
  transferMetadata: object - Transfer-specific data
}
```

### 3. Model Relationship
The models use **composition with foreign key reference** for maximum flexibility:

```typescript
// Relationship Structure
PaymentOrder (1) ←→ (1) NFTSaleOrder
// One payment order can have one NFT sale order
// NFT sale order references payment order via paymentOrderId
```

### Separated Status Enums

#### PaymentStatus Enum (Generic - PaymentOrder)
Payment-related statuses that apply to all product types:

**Initial Payment States**
- **CREATED** - Payment order created, awaiting Stripe session
- **SESSION_CREATED** - Stripe checkout session created
- **PAYMENT_PENDING** - User redirected to Stripe checkout
- **PAYMENT_PROCESSING** - Stripe processing payment

**Payment Completion States**
- **PAYMENT_SUCCEEDED** - Payment confirmed by Stripe
- **PAYMENT_FAILED** - Payment declined or failed
- **PAYMENT_CANCELLED** - User cancelled during checkout
- **PAYMENT_EXPIRED** - Payment session expired

**Payment Error & Admin States**
- **PAYMENT_ERROR** - Unrecoverable payment error
- **REFUND_PENDING** - Refund initiated
- **REFUND_COMPLETED** - Refund processed
- **PAYMENT_MANUAL_REVIEW** - Payment requires admin review

#### TransferStatus Enum (NFT-Specific - NFTSaleOrder)
NFT transfer-related statuses specific to blockchain operations:

**Transfer Initiation States**
- **TRANSFER_PENDING** - Awaiting NFT transfer initiation
- **TRANSFER_QUEUED** - Transfer queued in blockchain service
- **TRANSFER_PROCESSING** - Blockchain transaction initiated

**Transfer Execution States**
- **TRANSFER_SUBMITTED** - Transaction submitted to blockchain
- **TRANSFER_CONFIRMING** - Awaiting blockchain confirmations
- **TRANSFER_CONFIRMED** - NFT transfer confirmed on blockchain

**Transfer Completion States**
- **TRANSFER_COMPLETED** - NFT successfully transferred to user

**Transfer Error States**
- **TRANSFER_FAILED** - NFT transfer failed
- **BLOCKCHAIN_ERROR** - Blockchain network error
- **INSUFFICIENT_BALANCE** - Admin wallet insufficient balance
- **CONTRACT_ERROR** - Smart contract execution error
- **WALLET_ERROR** - Invalid destination wallet

**Transfer Admin States**
- **TRANSFER_MANUAL_REVIEW** - Transfer requires admin intervention
- **TRANSFER_ADMIN_PROCESSING** - Admin manually processing transfer

#### Combined Order Status (Computed)
For API responses and UI display, combine both statuses:

```typescript
interface CombinedOrderStatus {
  paymentStatus: PaymentStatus
  transferStatus: TransferStatus
  overallStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'REQUIRES_ATTENTION'
  displayStatus: string // Human-readable status
}
```

### Status Transition Rules

#### Payment Order Flow (Generic)
```
CREATED → SESSION_CREATED → PAYMENT_PENDING → PAYMENT_PROCESSING → PAYMENT_SUCCEEDED
```

**Payment Failure Flows:**
```
PAYMENT_PENDING → PAYMENT_CANCELLED (user cancellation)
PAYMENT_PENDING → PAYMENT_EXPIRED (session timeout)
PAYMENT_PROCESSING → PAYMENT_FAILED (declined)
PAYMENT_PROCESSING → PAYMENT_ERROR (system error)
```

**Payment Admin Flows:**
```
PAYMENT_ERROR → PAYMENT_MANUAL_REVIEW → REFUND_PENDING → REFUND_COMPLETED
```

#### NFT Transfer Flow (Specialized)
```
TRANSFER_PENDING → TRANSFER_QUEUED → TRANSFER_PROCESSING →
TRANSFER_SUBMITTED → TRANSFER_CONFIRMING → TRANSFER_CONFIRMED → TRANSFER_COMPLETED
```

**Transfer Failure Flows:**
```
TRANSFER_PROCESSING → TRANSFER_FAILED (general failure)
TRANSFER_PROCESSING → BLOCKCHAIN_ERROR (network issues)
TRANSFER_PROCESSING → INSUFFICIENT_BALANCE (wallet empty)
TRANSFER_SUBMITTED → CONTRACT_ERROR (smart contract failure)
TRANSFER_CONFIRMING → WALLET_ERROR (invalid destination)
```

**Transfer Admin Flows:**
```
TRANSFER_FAILED → TRANSFER_MANUAL_REVIEW → TRANSFER_ADMIN_PROCESSING → TRANSFER_COMPLETED
BLOCKCHAIN_ERROR → TRANSFER_MANUAL_REVIEW → TRANSFER_ADMIN_PROCESSING → TRANSFER_COMPLETED
```

#### Combined Flow Coordination
The NFT transfer flow begins only after payment success:
```
PaymentOrder.PAYMENT_SUCCEEDED → NFTSaleOrder.TRANSFER_PENDING
```

### Dual-Model State Diagrams

#### Payment Order State Diagram (Generic Model)

```mermaid
stateDiagram-v2
    [*] --> CREATED : Payment order initiated

    %% Payment Flow
    CREATED --> SESSION_CREATED : Stripe session created
    SESSION_CREATED --> PAYMENT_PENDING : User redirected
    SESSION_CREATED --> PAYMENT_EXPIRED : Session timeout

    %% Payment Processing
    PAYMENT_PENDING --> PAYMENT_PROCESSING : Stripe processing
    PAYMENT_PENDING --> PAYMENT_CANCELLED : User cancelled
    PAYMENT_PROCESSING --> PAYMENT_SUCCEEDED : Payment confirmed
    PAYMENT_PROCESSING --> PAYMENT_FAILED : Payment declined
    PAYMENT_PROCESSING --> PAYMENT_ERROR : System error

    %% Payment Admin Flow
    PAYMENT_ERROR --> PAYMENT_MANUAL_REVIEW : Admin review needed
    PAYMENT_MANUAL_REVIEW --> REFUND_PENDING : Admin initiates refund
    REFUND_PENDING --> REFUND_COMPLETED : Refund processed

    %% Terminal States
    PAYMENT_SUCCEEDED --> [*] : Triggers product fulfillment
    PAYMENT_FAILED --> [*]
    PAYMENT_CANCELLED --> [*]
    PAYMENT_EXPIRED --> [*]
    REFUND_COMPLETED --> [*]

    class CREATED,SESSION_CREATED,PAYMENT_PENDING initialState
    class PAYMENT_PROCESSING processingState
    class PAYMENT_SUCCEEDED successState
    class PAYMENT_FAILED,PAYMENT_ERROR,PAYMENT_CANCELLED,PAYMENT_EXPIRED errorState
    class PAYMENT_MANUAL_REVIEW,REFUND_PENDING,REFUND_COMPLETED adminState
```

#### NFT Transfer State Diagram (Specialized Model)

```mermaid
stateDiagram-v2
    [*] --> TRANSFER_PENDING : Payment succeeded

    %% Transfer Flow
    TRANSFER_PENDING --> TRANSFER_QUEUED : Queued for processing
    TRANSFER_QUEUED --> TRANSFER_PROCESSING : Blockchain service picks up
    TRANSFER_PROCESSING --> TRANSFER_SUBMITTED : Transaction submitted
    TRANSFER_SUBMITTED --> TRANSFER_CONFIRMING : Awaiting confirmations
    TRANSFER_CONFIRMING --> TRANSFER_CONFIRMED : Confirmations received
    TRANSFER_CONFIRMED --> TRANSFER_COMPLETED : NFT transfer complete

    %% Transfer Errors
    TRANSFER_PROCESSING --> TRANSFER_FAILED : General failure
    TRANSFER_PROCESSING --> BLOCKCHAIN_ERROR : Network error
    TRANSFER_PROCESSING --> INSUFFICIENT_BALANCE : Wallet empty
    TRANSFER_SUBMITTED --> CONTRACT_ERROR : Smart contract error
    TRANSFER_CONFIRMING --> WALLET_ERROR : Invalid destination

    %% Transfer Admin Flow
    TRANSFER_FAILED --> TRANSFER_MANUAL_REVIEW : Admin review needed
    BLOCKCHAIN_ERROR --> TRANSFER_MANUAL_REVIEW : Admin review needed
    INSUFFICIENT_BALANCE --> TRANSFER_MANUAL_REVIEW : Admin review needed
    CONTRACT_ERROR --> TRANSFER_MANUAL_REVIEW : Admin review needed
    WALLET_ERROR --> TRANSFER_MANUAL_REVIEW : Admin review needed

    TRANSFER_MANUAL_REVIEW --> TRANSFER_ADMIN_PROCESSING : Admin intervention
    TRANSFER_ADMIN_PROCESSING --> TRANSFER_COMPLETED : Manual completion

    %% Terminal State
    TRANSFER_COMPLETED --> [*]

    class TRANSFER_PENDING,TRANSFER_QUEUED queueState
    class TRANSFER_PROCESSING,TRANSFER_SUBMITTED,TRANSFER_CONFIRMING,TRANSFER_CONFIRMED processingState
    class TRANSFER_COMPLETED successState
    class TRANSFER_FAILED,BLOCKCHAIN_ERROR,INSUFFICIENT_BALANCE,CONTRACT_ERROR,WALLET_ERROR errorState
    class TRANSFER_MANUAL_REVIEW,TRANSFER_ADMIN_PROCESSING adminState
```

### Future Extensibility Framework

#### Product Type Extensions
The generic PaymentOrder model can be extended for various product types:

```typescript
// Future Product Models (Examples)
interface SubscriptionOrder {
  subscriptionOrderId: string
  paymentOrderId: string // FK to PaymentOrder
  planId: string
  billingCycle: 'MONTHLY' | 'YEARLY'
  subscriptionStatus: SubscriptionStatus
  nextBillingDate: Date
  // ... subscription-specific fields
}

interface PhysicalGoodsOrder {
  physicalOrderId: string
  paymentOrderId: string // FK to PaymentOrder
  shippingAddress: Address
  trackingNumber: string
  fulfillmentStatus: FulfillmentStatus
  // ... shipping-specific fields
}

interface DigitalContentOrder {
  digitalOrderId: string
  paymentOrderId: string // FK to PaymentOrder
  downloadUrl: string
  accessExpiresAt: Date
  downloadCount: number
  // ... digital content-specific fields
}
```

#### Generic Product Fulfillment Interface
```typescript
interface ProductFulfillmentService {
  fulfillOrder(paymentOrderId: string): Promise<FulfillmentResult>
  getOrderStatus(paymentOrderId: string): Promise<OrderStatus>
  retryFulfillment(paymentOrderId: string): Promise<FulfillmentResult>
  cancelFulfillment(paymentOrderId: string): Promise<void>
}

// Implementations
class NFTFulfillmentService implements ProductFulfillmentService { ... }
class SubscriptionFulfillmentService implements ProductFulfillmentService { ... }
class PhysicalGoodsFulfillmentService implements ProductFulfillmentService { ... }
```

### Enhanced PaymentHistory
- paymentId: string (UUID)
- paymentOrderId: string - Reference to PaymentOrder
- userId: string
- amount: number
- currency: string
- paymentMethod: string - card, apple_pay, google_pay, bank_transfer
- status: enum - succeeded, failed, cancelled, refunded
- stripeChargeId: string
- stripePaymentIntentId: string
- stripeSessionId: string
- processedAt: Date
- failureReason: string (optional)
- refundId: string (optional) - Stripe refund ID if applicable
- refundAmount: number (optional) - Amount refunded
- refundedAt: Date (optional) - Refund timestamp

### OrderStatusHistory
- historyId: string (UUID)
- orderId: string - Reference to order
- fromStatus: enum - Previous status
- toStatus: enum - New status
- triggeredBy: enum - SYSTEM, WEBHOOK, ADMIN, USER
- triggerSource: string - Specific source (webhook_id, admin_user_id, etc.)
- reason: string - Reason for status change
- metadata: object - Additional context data
- timestamp: Date

### BlockchainTransaction
- txId: string (UUID)
- orderId: string - Reference to order
- txHash: string - Blockchain transaction hash
- fromAddress: string - Admin wallet address
- toAddress: string - User wallet address
- tokenId: string - NFT token ID
- contractAddress: string - NFT contract address
- gasUsed: number - Gas consumed
- gasPrice: string - Gas price in wei
- blockNumber: number - Block number where tx was mined
- confirmations: number - Current confirmation count
- status: enum - PENDING, CONFIRMED, FAILED
- submittedAt: Date
- confirmedAt: Date
- failureReason: string (optional)

### Status Transition Triggers

#### Automatic Triggers
- **CREATED → SESSION_CREATED**: Stripe session API success
- **PAYMENT_PENDING → PAYMENT_PROCESSING**: Stripe webhook `payment_intent.processing`
- **PAYMENT_PROCESSING → PAYMENT_SUCCEEDED**: Stripe webhook `payment_intent.succeeded`
- **PAYMENT_SUCCEEDED → TRANSFER_PENDING**: Automatic queue after payment confirmation
- **TRANSFER_PENDING → TRANSFER_PROCESSING**: Blockchain service picks up order
- **TRANSFER_PROCESSING → TRANSFER_CONFIRMING**: Transaction submitted to blockchain
- **TRANSFER_CONFIRMING → TRANSFER_CONFIRMED**: Required confirmations reached

#### Error Triggers
- **PAYMENT_PROCESSING → PAYMENT_FAILED**: Stripe webhook `payment_intent.payment_failed`
- **TRANSFER_PROCESSING → BLOCKCHAIN_ERROR**: Network timeout or contract error
- **TRANSFER_CONFIRMING → TRANSFER_ERROR**: Transaction reverted or failed
- **Any transfer state → INSUFFICIENT_BALANCE**: Admin wallet balance check fails

#### Manual Triggers
- **Error states → MANUAL_REVIEW**: Admin or system escalation
- **MANUAL_REVIEW → ADMIN_PROCESSING**: Admin begins manual intervention
- **ADMIN_PROCESSING → COMPLETED**: Admin manually completes order
- **ADMIN_PROCESSING → REFUND_PENDING**: Admin initiates refund process

### Implementation Considerations

### Updated Database Schema

#### PaymentOrder Collection
```javascript
// MongoDB Schema
{
  _id: ObjectId,
  paymentOrderId: String (UUID, unique),
  userId: String (indexed),
  productType: String (enum, indexed),
  productId: String,
  productMetadata: Object,
  amount: Number,
  currency: String,
  stripeSessionId: String (unique, sparse),
  stripePaymentIntentId: String (unique, sparse),
  stripeChargeId: String (unique, sparse),
  stripeCustomerId: String (indexed),
  paymentStatus: String (enum, indexed),
  paymentRetryCount: Number (default: 0),
  paymentLastRetryAt: Date,
  paymentErrorDetails: Object,
  createdAt: Date (indexed),
  updatedAt: Date,
  paymentCompletedAt: Date,
  expiresAt: Date (TTL index),
  adminNotes: String,
  metadata: Object
}
```

#### NFTSaleOrder Collection
```javascript
// MongoDB Schema
{
  _id: ObjectId,
  nftOrderId: String (UUID, unique),
  paymentOrderId: String (indexed, FK to PaymentOrder),
  tokenId: String,
  contractAddress: String,
  nftType: String (enum),
  userWalletAddress: String (indexed),
  adminWalletAddress: String,
  blockchainTxHash: String (unique, sparse),
  blockchainConfirmations: Number (default: 0),
  gasUsed: Number,
  gasPrice: String,
  blockNumber: Number,
  transferStatus: String (enum, indexed),
  transferRetryCount: Number (default: 0),
  transferLastRetryAt: Date,
  transferErrorDetails: Object,
  createdAt: Date (indexed),
  updatedAt: Date,
  transferInitiatedAt: Date,
  transferCompletedAt: Date,
  nftMetadata: Object,
  transferMetadata: Object
}
```

#### Database Indexing Strategy
**PaymentOrder Indexes:**
- `paymentOrderId` (unique)
- `userId` (for user order history)
- `paymentStatus` (for status-based queries)
- `stripeSessionId` (for webhook processing)
- `productType` (for product-specific queries)
- `{paymentStatus: 1, createdAt: 1}` (compound, for queue processing)
- `{userId: 1, createdAt: -1}` (compound, for user history)
- `expiresAt` (TTL index for automatic cleanup)

**NFTSaleOrder Indexes:**
- `nftOrderId` (unique)
- `paymentOrderId` (FK reference)
- `transferStatus` (for transfer queue processing)
- `userWalletAddress` (for wallet-based queries)
- `blockchainTxHash` (for transaction lookups)
- `{transferStatus: 1, createdAt: 1}` (compound, for transfer queue)
- `{userWalletAddress: 1, createdAt: -1}` (compound, for user NFTs)

#### Status Validation
- Implement state machine validation to prevent invalid transitions
- Log all status changes with timestamps and triggers
- Validate required fields for each status (e.g., `stripeSessionId` for SESSION_CREATED)

#### Retry Logic
- Implement exponential backoff for blockchain operations
- Maximum retry limits per operation type
- Dead letter queue for permanently failed orders
- Admin notification for orders requiring intervention

#### Monitoring & Alerting
- Alert on orders stuck in processing states > 30 minutes
- Monitor payment success/failure rates
- Track NFT transfer completion times
- Alert on high retry counts or error rates

## Implementation Steps

### Phase 1: Enhanced Stripe Gateway Service (Estimated: 3-4 days)
- Step 1: Enhance existing stripe-gateway with order management
- Step 2: Implement Stripe Checkout Session creation
- Step 3: Add webhook handling for payment events
- Step 4: Create order tracking and status management
- Step 5: Add environment-specific Stripe configuration
- Deliverables: Enhanced stripe-gateway service with order management

### Phase 2: DCasks Backend Integration (Estimated: 2-3 days)
- Step 1: Create payments GraphQL module in dcasks-backend
- Step 2: Implement payment initiation and order creation
- Step 3: Add payment status queries and history endpoints
- Step 4: Integrate with existing user/wallet system
- Step 5: Add NFT transfer triggering post-payment
- Deliverables: Payment GraphQL API in dcasks-backend

### Phase 3: Capcom Frontend Integration (Estimated: 2-3 days)
- Step 1: Create payment UI components using shadcn
- Step 2: Implement checkout flow with Stripe redirect
- Step 3: Add payment status tracking and confirmation
- Step 4: Create payment history display
- Step 5: Integrate with existing NFT display system
- Deliverables: Complete payment flow in Capcom frontend

### Phase 4: Deployment & Infrastructure (Estimated: 3-4 days)
- Step 1: Configure ECS Service Discovery with CloudMap
- Step 2: Configure ECS deployment for enhanced stripe-gateway
- Step 3: Set up environment-specific Stripe webhook endpoints
- Step 4: Configure AWS Secrets Manager for Stripe keys and X-API-Keys
- Step 5: Set up CloudWatch monitoring and alerting
- Step 6: Configure subdomain for webhook endpoints
- Step 7: Test service-to-service communication via ECS Service Discovery
- Deliverables: Production-ready deployment configuration with service discovery

### Phase 5: Testing & Documentation (Estimated: 2 days)
- Step 1: Write unit tests for payment services
- Step 2: Create E2E tests for complete payment flow
- Step 3: Test webhook reliability and error handling
- Step 4: Create API documentation and integration guides
- Step 5: Perform security and load testing
- Deliverables: Comprehensive test suite and documentation

## Testing Strategy
- **Unit Tests**: Payment service logic, order management, webhook processing
- **Integration Tests**: Stripe API integration, database operations, GraphQL resolvers
- **E2E Tests**: Complete payment flow from frontend to NFT transfer
- **Performance Tests**: Concurrent payment processing, webhook response times
- **Security Tests**: Payment data handling, webhook signature verification

## Security Considerations
- **Authentication**: Wallet-based user authentication for payment initiation
- **Authorization**: User can only access their own payment history and orders
- **Data Protection**: No sensitive payment data stored, Stripe handles PCI compliance
- **Input Validation**: Strict validation of payment amounts and order data
- **Webhook Security**: Stripe signature verification for all webhook events
- **Environment Isolation**: Separate Stripe accounts for dev/testnet/production

## Performance Considerations
- **Expected Load**: 100-500 concurrent payments during peak times
- **Response Time Targets**: Payment initiation < 2s, webhook processing < 1s
- **Optimization Strategies**: Database indexing on orderId/userId, webhook retry logic
- **Caching**: Order status caching for frequently accessed data
- **Rate Limiting**: API rate limiting to prevent abuse

## Risk Assessment
- **Risk 1**: Stripe webhook delivery failure → **Medium** → Implement retry mechanism and manual reconciliation
- **Risk 2**: Payment success but NFT transfer failure → **High** → Implement retry mechanism in DCasks Backend and manual intervention queue
- **Risk 3**: Currency conversion rate fluctuations → **Low** → Use real-time rates with small buffer
- **Risk 4**: Stripe API downtime → **Medium** → Implement graceful degradation and user notifications
- **Risk 5**: Webhook endpoint security breach → **Medium** → Implement signature verification and IP whitelisting

## Success Criteria
- **Functional**: 100% successful payment processing for valid transactions
- **Performance**: < 5 second end-to-end payment completion time
- **Reliability**: 99.9% webhook delivery success rate
- **Security**: Zero payment data breaches or unauthorized access
- **User Experience**: < 2% payment abandonment rate due to technical issues
- **Integration**: Seamless NFT transfer within 30 seconds of payment confirmation

## Timeline & Milestones
- **Milestone 1**: Week 1 - Enhanced Stripe Gateway Service completed
- **Milestone 2**: Week 2 - DCasks Backend integration completed
- **Milestone 3**: Week 2-3 - Capcom Frontend integration completed
- **Milestone 4**: Week 3 - Deployment and infrastructure setup completed
- **Milestone 5**: Week 3-4 - Testing and documentation completed
- **Final Delivery**: Week 4 - Production-ready Stripe payment integration

## Environment Configuration

### Development Environment
- Stripe Account: Sandbox/Test mode
- Webhook URL: `https://dev-stripe.dcasks.co/webhooks`
- Database: Development MongoDB instance
- Frontend URL: `http://localhost:3000` (Capcom dev)
- Service Discovery: `stripe-gateway.dcasks.local`, `dcasks-backend.dcasks.local`
- DCasks Backend URL: `https://dev-api-ecs.dcasks.co`

### Testnet Environment
- Stripe Account: Sandbox/Test mode
- Webhook URL: `https://testnet-stripe.dcasks.co/webhooks`
- Database: Testnet MongoDB instance
- Frontend URL: `https://testnet-capcom.dcasks.co`
- Service Discovery: `stripe-gateway.dcasks.local`, `dcasks-backend.dcasks.local`
- DCasks Backend URL: `https://testnet-api-ecs.dcasks.co`

### Production Environment
- Stripe Account: Live mode
- Webhook URL: `https://stripe.dcasks.co/webhooks`
- Database: Production MongoDB instance
- Frontend URL: `https://capcom.dcasks.co`
- Service Discovery: `stripe-gateway.dcasks.local`, `dcasks-backend.dcasks.local`
- DCasks Backend URL: `https://api-ecs.dcasks.co`

## Future Enhancements
- **Admin Dashboard**: Payment monitoring and management interface
- **Subscription Support**: Recurring payment capabilities
- **Multi-App Integration**: Extend to other DCasks ecosystem apps
- **Advanced Analytics**: Payment conversion and user behavior tracking
- **Refund System**: Automated and manual refund processing
- **Multi-Currency Pricing**: Dynamic pricing in user's preferred currency

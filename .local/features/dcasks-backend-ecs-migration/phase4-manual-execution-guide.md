# Phase 4: Shared ALB Setup - Completion Guide (ECS Domains)

**Updated**: 2025-06-09 04:15:00 UTC
**Status**: 85% Complete - Certificate validation and HTTPS listener remaining
**Architecture**: Cloudflare Proxy + ALB HTTPS (Full Strict SSL mode)
**Domains**: Using "-ecs" suffix to avoid breaking current API

## Prerequisites

### 1. Load Environment Variables
```bash
cd /Users/<USER>/Documents/Projects/dcasks/dcasks-mono
source dcasks-migration-resources.env
export AWS_PROFILE=dcasks-david
export AWS_REGION=ap-southeast-1
```

### 2. Verify AWS Access
```bash
aws sts get-caller-identity --profile dcasks-david
```

## ✅ Current Status: 85% Complete

### **Already Created Successfully:**
- ✅ **ALB**: dcasks-alb (Active)
- ✅ **Target Groups**: Production, Dev, Testnet (All created)
- ✅ **Certificate**: Requested for ECS domains
- ✅ **Resource File**: Updated with all ARNs

### **New ECS Domains:**
- `api-ecs.dcasks.co` (production)
- `dev-api-ecs.dcasks.co` (dev)
- `testnet-api-ecs.dcasks.co` (testnet)

## Step 1: Complete Certificate DNS Validation

### 1.1 Get DNS Validation Records
```bash
aws acm describe-certificate \
  --certificate-arn arn:aws:acm:ap-southeast-1:483805378365:certificate/5f770836-e245-4b30-8b3b-177375069db5 \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'Certificate.DomainValidationOptions[*].{Domain:DomainName,Name:ResourceRecord.Name,Value:ResourceRecord.Value,Type:ResourceRecord.Type}' \
  --output table
```

### 1.2 Add DNS Records to Cloudflare
1. Log into Cloudflare dashboard
2. Go to dcasks.co domain DNS settings
3. Add the CNAME records provided by the command above
4. **Important**: Set proxy status to "DNS only" (gray cloud) for validation records
5. Wait 5-10 minutes for validation

### 1.3 Verify Certificate Status
```bash
aws acm describe-certificate \
  --certificate-arn arn:aws:acm:ap-southeast-1:483805378365:certificate/5f770836-e245-4b30-8b3b-177375069db5 \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'Certificate.Status' \
  --output text
```
**Expected Output**: `ISSUED`

## Step 2: Load Environment Variables (Already Created Resources)

### 2.1 Load Resource ARNs
```bash
cd /Users/<USER>/Documents/Projects/dcasks/dcasks-mono
source dcasks-migration-resources.env

# Verify loaded resources
echo "ALB ARN: $ALB_ARN"
echo "ALB DNS: $ALB_DNS"
echo "Production TG: $PROD_TG_ARN"
echo "Dev TG: $DEV_TG_ARN"
echo "Testnet TG: $TESTNET_TG_ARN"
echo "Certificate ARN: $CERT_ARN"
```

**Note**: ALB and Target Groups are already created and active!

## Step 3: Create HTTPS Listener and Routing Rules (After Certificate Validation)

### 3.1 Create HTTPS Listener
```bash
LISTENER_ARN=$(aws elbv2 create-listener \
  --load-balancer-arn $ALB_ARN \
  --protocol HTTPS \
  --port 443 \
  --certificates CertificateArn=$CERT_ARN \
  --default-actions Type=fixed-response,FixedResponseConfig='{MessageBody="Not Found",StatusCode="404",ContentType="text/plain"}' \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'Listeners[0].ListenerArn' \
  --output text)

echo "Listener ARN: $LISTENER_ARN"
```

### 3.2 Create Host-based Routing Rules

#### Production Rule (api-ecs.dcasks.co)
```bash
aws elbv2 create-rule \
  --listener-arn $LISTENER_ARN \
  --priority 100 \
  --conditions Field=host-header,Values=api-ecs.dcasks.co \
  --actions Type=forward,TargetGroupArn=$PROD_TG_ARN \
  --profile dcasks-david \
  --region ap-southeast-1
```

#### Dev Rule (dev-api-ecs.dcasks.co)
```bash
aws elbv2 create-rule \
  --listener-arn $LISTENER_ARN \
  --priority 200 \
  --conditions Field=host-header,Values=dev-api-ecs.dcasks.co \
  --actions Type=forward,TargetGroupArn=$DEV_TG_ARN \
  --profile dcasks-david \
  --region ap-southeast-1
```

#### Testnet Rule (testnet-api-ecs.dcasks.co)
```bash
aws elbv2 create-rule \
  --listener-arn $LISTENER_ARN \
  --priority 300 \
  --conditions Field=host-header,Values=testnet-api-ecs.dcasks.co \
  --actions Type=forward,TargetGroupArn=$TESTNET_TG_ARN \
  --profile dcasks-david \
  --region ap-southeast-1
```

## Step 4: Update Resource Environment File

### 4.1 Add HTTPS Listener ARN
```bash
cat >> dcasks-migration-resources.env << EOF
export LISTENER_ARN=$LISTENER_ARN
EOF
```

### 4.2 Mark Phase 4 as Complete
```bash
# Update the comment in the resource file
sed -i 's/# Phase 4: Shared ALB Setup (IN PROGRESS)/# Phase 4: Shared ALB Setup (COMPLETED)/' dcasks-migration-resources.env
```

## Step 5: Validation and Testing

### 6.1 Verify ALB Status
```bash
aws elbv2 describe-load-balancers \
  --load-balancer-arns $ALB_ARN \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'LoadBalancers[0].{Name:LoadBalancerName,State:State.Code,DNS:DNSName,Scheme:Scheme}' \
  --output table
```

### 6.2 Verify Target Groups
```bash
aws elbv2 describe-target-groups \
  --target-group-arns $PROD_TG_ARN $DEV_TG_ARN $TESTNET_TG_ARN \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'TargetGroups[*].{Name:TargetGroupName,Protocol:Protocol,Port:Port,HealthCheck:HealthCheckPath}' \
  --output table
```

### 6.3 Verify HTTPS Listener
```bash
aws elbv2 describe-listeners \
  --load-balancer-arn $ALB_ARN \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'Listeners[*].{Protocol:Protocol,Port:Port,Certificate:Certificates[0].CertificateArn}' \
  --output table
```

### 6.4 Verify Routing Rules
```bash
aws elbv2 describe-rules \
  --listener-arn $LISTENER_ARN \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'Rules[*].{Priority:Priority,Host:Conditions[0].Values[0],TargetGroup:Actions[0].TargetGroupArn}' \
  --output table
```

## Step 6: Configure Cloudflare DNS

### 6.1 Update DNS Records in Cloudflare
1. Log into Cloudflare dashboard
2. Go to dcasks.co domain DNS settings
3. Add the following CNAME records (with proxy enabled - orange cloud):

```
api-ecs.dcasks.co → dcasks-alb-**********.ap-southeast-1.elb.amazonaws.com
dev-api-ecs.dcasks.co → dcasks-alb-**********.ap-southeast-1.elb.amazonaws.com
testnet-api-ecs.dcasks.co → dcasks-alb-**********.ap-southeast-1.elb.amazonaws.com
```

### 6.2 Configure Cloudflare SSL Settings
1. Go to SSL/TLS → Overview
2. Set encryption mode to **"Full (strict)"**
3. Verify "Always Use HTTPS" is enabled

## Success Criteria

✅ **Certificate Status**: ISSUED  
✅ **ALB Status**: active  
✅ **Target Groups**: 3 created (production, dev, testnet)  
✅ **HTTPS Listener**: Created with certificate  
✅ **Routing Rules**: 3 host-based rules created  
✅ **Cloudflare DNS**: Updated with ALB DNS name  
✅ **Cloudflare SSL**: Full (strict) mode enabled  

## Next Steps

After completing Phase 4:
1. **Phase 5**: CloudWatch log groups
2. **Phase 6**: IAM roles for ECS
3. **Phase 7**: Secrets Manager migration
4. **Phase 8**: ECS task definitions
5. **Phase 9**: Production migration and testing

## Troubleshooting

### Certificate Validation Issues
- Ensure DNS records are added correctly to Cloudflare
- Wait 5-10 minutes for DNS propagation
- Check certificate status with `aws acm describe-certificate`

### ALB Creation Issues
- Verify security group allows HTTPS (port 443)
- Ensure subnets are in different AZs
- Check VPC and subnet configurations

### Routing Issues
- Verify host header conditions match exactly
- Check target group health status
- Ensure ECS services are registered with target groups

# Phase 5: CloudWatch Log Groups - Manual Execution Guide

**Created**: 2025-06-09 08:22:00 UTC  
**Status**: Ready for manual execution due to AWS CLI timeout issues  
**Goal**: Create CloudWatch log groups for all ECS services and environments  

## Prerequisites

### 1. Load Environment Variables
```bash
cd /Users/<USER>/Documents/Projects/dcasks/dcasks-mono
source dcasks-migration-resources.env
export AWS_PROFILE=dcasks-david
export AWS_REGION=ap-southeast-1
```

### 2. Verify AWS Access
```bash
aws sts get-caller-identity --profile dcasks-david
```

## Step 1: Create API Service Log Groups

### 1.1 Production API Log Group (90-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-api-production \
  --retention-in-days 90 \
  --tags project=dcasks,service=api,environment=production \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Production API log group created"
```

### 1.2 Dev API Log Group (30-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-api-dev \
  --retention-in-days 30 \
  --tags project=dcasks,service=api,environment=dev \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Dev API log group created"
```

### 1.3 Testnet API Log Group (30-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-api-testnet \
  --retention-in-days 30 \
  --tags project=dcasks,service=api,environment=testnet \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Testnet API log group created"
```

## Step 2: Create Job Service Log Groups

### 2.1 Production Job Log Group (90-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-job-production \
  --retention-in-days 90 \
  --tags project=dcasks,service=job,environment=production \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Production Job log group created"
```

### 2.2 Dev Job Log Group (30-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-job-dev \
  --retention-in-days 30 \
  --tags project=dcasks,service=job,environment=dev \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Dev Job log group created"
```

### 2.3 Testnet Job Log Group (30-day retention)
```bash
aws logs create-log-group \
  --log-group-name /ecs/dcasks-job-testnet \
  --retention-in-days 30 \
  --tags project=dcasks,service=job,environment=testnet \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Testnet Job log group created"
```

## Step 3: Validation and Verification

### 3.1 List All Created Log Groups
```bash
aws logs describe-log-groups \
  --log-group-name-prefix /ecs/dcasks \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'logGroups[*].{LogGroupName:logGroupName,RetentionInDays:retentionInDays,CreationTime:creationTime}' \
  --output table
```

**Expected Output**: 6 log groups with correct retention policies

### 3.2 Get Log Group ARNs
```bash
aws logs describe-log-groups \
  --log-group-name-prefix /ecs/dcasks \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'logGroups[*].{LogGroupName:logGroupName,Arn:arn}' \
  --output json
```

### 3.3 Verify Retention Policies
```bash
echo "=== Verifying Retention Policies ==="
echo "Production log groups (should be 90 days):"
aws logs describe-log-groups \
  --log-group-name-prefix /ecs/dcasks \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'logGroups[?contains(logGroupName, `production`)].{LogGroupName:logGroupName,RetentionInDays:retentionInDays}' \
  --output table

echo "Dev/Testnet log groups (should be 30 days):"
aws logs describe-log-groups \
  --log-group-name-prefix /ecs/dcasks \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'logGroups[?contains(logGroupName, `dev`) || contains(logGroupName, `testnet`)].{LogGroupName:logGroupName,RetentionInDays:retentionInDays}' \
  --output table
```

## Step 4: Update Resource Environment File

### 4.1 Add Log Group Names
```bash
cat >> dcasks-migration-resources.env << 'EOF'

# Phase 5: CloudWatch Log Groups (COMPLETED)
export API_LOG_GROUP_PROD=/ecs/dcasks-api-production
export API_LOG_GROUP_DEV=/ecs/dcasks-api-dev
export API_LOG_GROUP_TESTNET=/ecs/dcasks-api-testnet
export JOB_LOG_GROUP_PROD=/ecs/dcasks-job-production
export JOB_LOG_GROUP_DEV=/ecs/dcasks-job-dev
export JOB_LOG_GROUP_TESTNET=/ecs/dcasks-job-testnet
EOF

echo "Resource environment file updated with log group names"
```

### 4.2 Mark Phase 5 as Complete
```bash
sed -i 's/# Phase 4: Shared ALB Setup (COMPLETED - HTTP)/# Phase 4: Shared ALB Setup (COMPLETED - HTTP)\n# Phase 5: CloudWatch Log Groups (COMPLETED)/' dcasks-migration-resources.env
```

## Step 5: Test Log Group Access (Optional)

### 5.1 Test Log Stream Creation
```bash
# Test creating a log stream in one of the log groups
aws logs create-log-stream \
  --log-group-name /ecs/dcasks-api-dev \
  --log-stream-name test-stream-$(date +%s) \
  --profile dcasks-david \
  --region ap-southeast-1

echo "Test log stream created successfully"
```

### 5.2 List Log Streams
```bash
aws logs describe-log-streams \
  --log-group-name /ecs/dcasks-api-dev \
  --profile dcasks-david \
  --region ap-southeast-1 \
  --query 'logStreams[*].{LogStreamName:logStreamName,CreationTime:creationTime}' \
  --output table
```

## Success Criteria

✅ **6 Log Groups Created**:
- `/ecs/dcasks-api-production` (90-day retention)
- `/ecs/dcasks-api-dev` (30-day retention)
- `/ecs/dcasks-api-testnet` (30-day retention)
- `/ecs/dcasks-job-production` (90-day retention)
- `/ecs/dcasks-job-dev` (30-day retention)
- `/ecs/dcasks-job-testnet` (30-day retention)

✅ **Retention Policies**: Production (90 days), Dev/Testnet (30 days)  
✅ **Tagging**: All log groups properly tagged  
✅ **Resource File**: Updated with log group names  
✅ **Validation**: All log groups accessible and functional  

## Log Group Configuration Summary

| Service | Environment | Log Group Name | Retention | Purpose |
|---------|-------------|----------------|-----------|---------|
| API | Production | `/ecs/dcasks-api-production` | 90 days | Production API logs |
| API | Dev | `/ecs/dcasks-api-dev` | 30 days | Development API logs |
| API | Testnet | `/ecs/dcasks-api-testnet` | 30 days | Testnet API logs |
| Job | Production | `/ecs/dcasks-job-production` | 90 days | Production job logs |
| Job | Dev | `/ecs/dcasks-job-dev` | 30 days | Development job logs |
| Job | Testnet | `/ecs/dcasks-job-testnet` | 30 days | Testnet job logs |

## Next Steps

After Phase 5 completion:
1. **Phase 6**: IAM Roles for ECS (task execution and task roles)
2. **Phase 7**: Secrets Manager migration
3. **Phase 8**: ECS task definitions (will reference these log groups)
4. **Phase 9**: ECS services deployment
5. **Phase 10**: Production migration and testing

## Troubleshooting

### Log Group Already Exists
If you get "ResourceAlreadyExistsException", the log group already exists. You can:
1. Skip that specific log group creation
2. Or delete and recreate: `aws logs delete-log-group --log-group-name <name>`

### Permission Issues
Ensure your AWS profile has CloudWatch Logs permissions:
- `logs:CreateLogGroup`
- `logs:DescribeLogGroups`
- `logs:PutRetentionPolicy`

### Retention Policy Issues
If retention policy fails to set, you can update it separately:
```bash
aws logs put-retention-policy \
  --log-group-name <log-group-name> \
  --retention-in-days <days> \
  --profile dcasks-david \
  --region ap-southeast-1
```

**Estimated Execution Time**: 10-15 minutes  
**Status After Completion**: Ready for Phase 6 (IAM Roles for ECS)

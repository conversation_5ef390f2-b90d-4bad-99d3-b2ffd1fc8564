# DCasks Backend ECS Migration Implementation Plan

**🚀 MIGRATION STATUS: Phase 9 - Dev Environment Completed (95%)**
**Last Updated**: 2025-06-11 08:40:00 UTC
**Current Phase**: Phase 9 (ECS Services Deployment) - Dev Environment Fixed and Running

## Overview
Migrate dcasks-backend service from EC2 Docker deployment to Amazon ECS using AWS Fargate. Split current docker-compose setup into separate ECS services for dcasks-api (scalable) and dcasks-job (single instance) while maintaining service dependencies and external connectivity.

## 🎯 Current Status Summary

### ✅ What's Working (Dev Environment)
- **dcasks-api-service-dev**: Fully operational, healthy, accessible via ALB
- **dcasks-job-service-dev**: Running successfully with external API connectivity
- **Infrastructure**: VPC, ALB, security groups, log groups all working
- **Health Checks**: Fixed and working with wget command
- **Service Naming**: Consistent naming convention established

### ⚠️ What Needs Fixing (Testnet/Production)
- **Docker Image URLs**: Task definitions need ECR image references instead of Docker Hub
- **Health Check Updates**: Apply wget health check fix to remaining environments
- **Service Validation**: Test and verify all services after fixes

### 📋 Immediate Next Steps
1. **For Testnet/Production Deployment** (when ready):
   - Update task definitions with correct ECR image URLs
   - Apply health check fixes (wget instead of curl)
   - Register new task definition revisions
   - Update services to use corrected task definitions
   - Verify all services start and run successfully

2. **Current State**: Dev environment is production-ready and stable

## ✅ Implementation Progress
- **✅ Prerequisites**: AWS CLI, permissions, environment setup
- **✅ Phase 1**: VPC and Network Infrastructure (COMPLETED)
- **✅ Phase 2**: Security Groups (COMPLETED)
- **✅ Phase 3**: ECS Clusters (COMPLETED)
- **✅ Phase 4**: Shared ALB Setup (COMPLETED - HTTP listener, target groups, routing)
- **✅ Phase 5**: CloudWatch Log Groups (COMPLETED - 6 log groups with retention policies)
- **✅ Phase 6**: IAM Roles for ECS (COMPLETED - Task execution and task roles with permissions)
- **✅ Phase 7**: Secrets Manager Migration (COMPLETED - Environment secrets with example values)
- **✅ Phase 8**: ECS Task Definitions (COMPLETED - 6 task definitions for API and Job services)
- **🔄 Phase 9**: ECS Services Deployment (95% COMPLETE - Dev environment fixed and running)
  - **✅ Dev Environment**: Both API and Job services running and healthy
  - **⚠️ Testnet Environment**: Services created but need Docker image fixes
  - **⚠️ Production Environment**: Services created but need Docker image fixes
- **⏳ Phase 10**: Production Migration & Testing (READY TO START after testnet/production fixes)

## Requirements
- [x] Migrate from EC2 Docker to ECS Fargate
- [x] Separate dcasks-api and dcasks-job into independent ECS services
- [x] Maintain service communication and dependencies
- [x] Support dev → testnet → production environments
- [x] Parallel deployment during transition period
- [x] Minimal resource configuration with API auto-scaling
- [x] Public internet access for MongoDB Atlas and blockchain APIs
- [x] Migrate secrets from .env files to AWS Secrets Manager
- [x] Update GitLab CI pipeline for ECS deployment

## Naming Conventions

### Environment Naming
Following existing GitLab CI environment structure:
- **dev**: Development environment (branch: `dev`, tag: `dev`) → **nprod cluster**
- **testnet**: Testing environment (branch: `testnet`, tag: `testnet`) → **nprod cluster**
- **production**: Production environment (branch: `main`, tag: `latest`) → **prod cluster**

### AWS Infrastructure Naming
```
VPC and Networking: ✅ IMPLEMENTED
- VPC: dcasks-vpc (vpc-015a028c94079948c) - ********/16
- Public Subnets: dcasks-public-subnet-1a (subnet-03efc31f4ddd3982c), dcasks-public-subnet-1b (subnet-091f7ea008ad881be)
- Internet Gateway: dcasks-igw (igw-0b85efe135245c538)
- Route Table: dcasks-public-rt (rtb-014390f1f3a0819ce)
- Shared ALB: dcasks-alb (single ALB for cost optimization) - PENDING

Security Groups: ✅ IMPLEMENTED
- API Security Group: dcasks-api-sg (sg-0d1af7867738fbfb7)
- Job Security Group: dcasks-job-sg (sg-0e70fc67741322cb5) - SIMPLIFIED
- ALB Security Group: dcasks-alb-sg (sg-0c30410ca1c3cccd3)

ECS Clusters: ✅ IMPLEMENTED
- Production: dcasks-cluster-prod (arn:aws:ecs:ap-southeast-1:************:cluster/dcasks-cluster-prod)
- Non-Production: dcasks-cluster-nprod (arn:aws:ecs:ap-southeast-1:************:cluster/dcasks-cluster-nprod)

Shared ALB: ✅ IMPLEMENTED
- ALB: dcasks-alb (dcasks-alb-**********.ap-southeast-1.elb.amazonaws.com)
- Target Groups: Production, Dev, Testnet (all created)
- HTTP Listener: Port 80 with path-based routing
- Status: Ready for ECS service deployment

Resource Naming Format: dcasks-{resource-type}-{environment}
Examples:
- Task Definitions: dcasks-api-task-{env}, dcasks-job-task-{env}
- Services: dcasks-api-service-{env}, dcasks-job-service-{env}
- Target Groups: dcasks-api-tg-{env}
- Log Groups: /ecs/dcasks-api-{env}, /ecs/dcasks-job-{env}
```

### Secrets Manager Naming
```
Format: dcasks/{environment}/{service-name}

Examples:
- dcasks/dev/dcasks-backend
- dcasks/testnet/dcasks-backend
- dcasks/production/dcasks-backend

Each secret contains all environment variables for the service:
{
  "PORT": "9000",
  "NODE_ENV": "development|testnet|production",
  "BLOCKCHAIN_NETWORK": "testnet|mainnet",
  "DATABASE_URI": "mongodb+srv://...",
  "JWT_SECRET": "...",
  "JWT_EXPIRE_TIME": "7d",
  "AWS_ACCESS_KEY_ID": "...",
  "AWS_SECRET_ACCESS_KEY": "...",
  "ADMIN_PRIVATE_KEY": "0x...",
  "WOKENS_HOLDER_PRIVATE_KEY": "0x...",
  "TWILIO_ACCOUNT_SID": "...",
  "TWILIO_AUTH_TOKEN": "...",
  "TWILIO_SERVICE_SID": "...",
  "AIRDROP_ENABLE": "0|1",
  "AIRDROP_REGISTER_EMAIL_AMOUNT": "0.001",
  "NETWORK_URL": "https://...",
  "WSS_NETWORK_URL": "wss://...",
  "ETH_NETWORK_URL": "https://...",
  "CONTRACT_USDT": "0x...",
  "CONTRACT_BOTTLE": "0x...",
  "CONTRACT_PROFIT_MODEL": "0x...",
  "CONTRACT_MARKETPLACE": "0x...",
  "CONTRACT_MEMBERSHIP": "0x...",
  "CONTRACT_WOKENS": "0x...",
  "CONTRACT_MULTI_TOKEN_TRANSFER": "0x...",
  "CONTRACT_WHOLE_CASK": "0x...",
  "CONTRACT_TULIP": "0x...",
  "BRC420_DEPLOY_IDS": "..."
}
```

## Architecture

### Current State Analysis
```yaml
# Current docker-compose setup
services:
  dcasks-api:
    - Port: 3000 (mapped to 8000/9020)
    - Environment: SERVICE_NAME=API
    - Volumes: .env file mount
    - Network: dcasks bridge network
  
  dcasks-job:
    - No exposed ports
    - Environment: SERVICE_NAME=JOB
    - Volumes: .env file mount
    - Network: dcasks bridge network
```

### Target ECS Architecture
```yaml
New VPC: dcasks-vpc (10.0.0.0/16)
├── Public Subnets: ********/24, ********/24 (Multi-AZ)
├── Internet Gateway: dcasks-igw
└── Shared ALB: dcasks-alb (cost-optimized single ALB)

ECS Cluster: dcasks-cluster-prod (Production)
├── dcasks-api-service-production (Fargate)
│   ├── Task Definition: dcasks-api-task-production
│   ├── Desired Count: 2-5 (auto-scaling)
│   ├── Load Balancer: dcasks-alb (shared, host-based routing)
│   ├── Subnets: Public subnets with IGW access
│   └── Port: 3000
└── dcasks-job-service-production (Fargate)
    ├── Task Definition: dcasks-job-task-production
    ├── Desired Count: 1 (fixed)
    ├── No Load Balancer
    ├── Subnets: Public subnets with IGW access
    └── External API connectivity (MongoDB, Blockchain, APIs)

ECS Cluster: dcasks-cluster-nprod (Non-Production)
├── dcasks-api-service-dev (Fargate)
│   ├── Task Definition: dcasks-api-task-dev
│   ├── Desired Count: 1-2 (auto-scaling)
│   ├── Load Balancer: dcasks-alb (shared, host-based routing)
│   ├── Subnets: Public subnets with IGW access
│   └── Port: 3000
├── dcasks-job-service-dev (Fargate)
│   ├── Task Definition: dcasks-job-task-dev
│   ├── Desired Count: 1 (fixed)
│   ├── Subnets: Public subnets with IGW access
│   └── External API connectivity
├── dcasks-api-service-testnet (Fargate)
│   ├── Task Definition: dcasks-api-task-testnet
│   ├── Desired Count: 1-2 (auto-scaling)
│   ├── Load Balancer: dcasks-alb (shared, host-based routing)
│   ├── Subnets: Public subnets with IGW access
│   └── Port: 3000
└── dcasks-job-service-testnet (Fargate)
    ├── Task Definition: dcasks-job-task-testnet
    ├── Desired Count: 1 (fixed)
    ├── Subnets: Public subnets with IGW access
    └── External API connectivity
```

## Implementation Phases

### ✅ Phase 1: VPC and Network Infrastructure Setup (COMPLETED)
**Goal**: Create new VPC with simplified, cost-optimized networking

**Tasks**:
1. ✅ Create new VPC (dcasks-vpc) with CIDR ********/16 (modified to avoid conflicts)
2. ✅ Set up public subnets (********/24, ********/24) across 2 AZs
3. ✅ Create Internet Gateway and attach to VPC
4. ✅ Configure route tables for public subnets (no private subnets needed)

**Deliverables**:
- ✅ VPC: `dcasks-vpc` (vpc-015a028c94079948c) with multi-AZ public subnets
- ✅ Public subnets with Internet Gateway routing
- ✅ Cost savings: No NAT Gateway charges (~$45/month per AZ)

### ✅ Phase 2: Security Groups Setup (COMPLETED)
**Goal**: Create security groups for different services and environments

**Tasks**:
1. ✅ Create API security group (dcasks-api-sg) for ECS API services
2. ✅ Create Job security group (dcasks-job-sg) for ECS Job services with external API access
3. ✅ Create ALB security group (dcasks-alb-sg) for Application Load Balancer
4. ✅ Configure security group integration (ALB → API services)

**Deliverables**:
- ✅ Security groups: `dcasks-api-sg`, `dcasks-job-sg`, `dcasks-alb-sg`
- ✅ Proper network access rules configured
- ✅ External API connectivity for job services

### ✅ Phase 3: ECS Clusters Setup (COMPLETED)
**Goal**: Create separate ECS clusters for production and non-production workloads

**Tasks**:
1. ✅ Create production ECS cluster (dcasks-cluster-prod) with Fargate capacity providers
2. ✅ Create non-production ECS cluster (dcasks-cluster-nprod) with Fargate capacity providers
3. ✅ Configure cluster settings and capacity providers
4. ✅ Set up CloudWatch Container Insights for both clusters

**Deliverables**:
- ✅ ECS cluster: `dcasks-cluster-prod` for production workloads
- ✅ ECS cluster: `dcasks-cluster-nprod` for dev/testnet workloads
- ✅ Fargate capacity providers configured
- ✅ Container Insights enabled for monitoring

### ✅ Phase 4: Shared Load Balancer Setup (COMPLETED)
**Goal**: Set up single ALB with routing for cost optimization

**Tasks**:
1. ✅ Create shared ALB (dcasks-alb) in public subnets
2. ✅ Configure target groups for each environment:
   - ✅ dcasks-api-tg-production
   - ✅ dcasks-api-tg-dev
   - ✅ dcasks-api-tg-testnet
3. ✅ Create HTTP listener for immediate functionality
4. ✅ Configure path-based routing rules (temporary)
5. ✅ Configure health checks for each target group

**Deliverables**:
- ✅ Single ALB: `dcasks-alb` with HTTP listener
- ✅ Environment-specific target groups
- ✅ Basic routing configuration (HTTP)
- ✅ Cost savings: ~$16/month per ALB eliminated
- ⏳ HTTPS/SSL setup (deferred to Phase 4.5)

### ✅ Phase 5: CloudWatch Log Groups (COMPLETED)
**Goal**: Set up CloudWatch log groups for ECS services

**Tasks**:
1. ✅ Create CloudWatch log groups for each environment and service:
   - ✅ /ecs/dcasks-api-production (90-day retention)
   - ✅ /ecs/dcasks-api-dev (30-day retention)
   - ✅ /ecs/dcasks-api-testnet (30-day retention)
   - ✅ /ecs/dcasks-job-production (90-day retention)
   - ✅ /ecs/dcasks-job-dev (30-day retention)
   - ✅ /ecs/dcasks-job-testnet (30-day retention)
2. ✅ Configure log retention policies (90 days prod, 30 days dev/testnet)
3. ✅ Validate log group functionality and accessibility

**Deliverables**:
- ✅ 6 CloudWatch log groups for all services and environments
- ✅ Proper log retention policies applied
- ✅ ECS task logging configuration ready for Phase 8

### Phase 6: IAM Roles for ECS (Week 2)
**Goal**: Create IAM roles and policies for ECS tasks and services

**Tasks**:
1. Create ECS task execution role
2. Create ECS task role with necessary permissions
3. Configure permissions for Secrets Manager access
4. Set up CloudWatch logs permissions
5. Configure external API access permissions

**Deliverables**:
- ECS task execution role
- ECS task role with proper permissions
- IAM policies for secrets and logging access

### ✅ Phase 7: Secrets Management Migration (COMPLETED)
**Goal**: Migrate environment variables to AWS Secrets Manager

**Tasks**:
1. ✅ Analyze current .env file structure for each environment
2. ✅ Create consolidated AWS Secrets Manager secrets:
   - ✅ dcasks/dev/dcasks-backend (all dev environment variables)
   - ✅ dcasks/testnet/dcasks-backend (all testnet environment variables)
   - ✅ dcasks/production/dcasks-backend (all production environment variables)
3. ✅ Validate IAM roles compatibility with Secrets Manager permissions
4. ✅ Create secret templates with example values
5. ✅ Validate secret key-value mappings and retrieval functionality

**Deliverables**:
- ✅ Secrets: `dcasks/dev/dcasks-backend`, `dcasks/testnet/dcasks-backend`, `dcasks/production/dcasks-backend`
- ✅ IAM policies for secret access per cluster (existing dcasks-secrets-manager-policy)
- ✅ Environment variable mapping documentation (32 variables per environment)
- ⚠️ **User Action Required**: Update example values with real production data

### Phase 8: ECS Task Definitions (Week 2)
**Goal**: Create separate task definitions for API and Job services across environments

**Tasks**:
1. Design dcasks-api task definitions for each environment with health checks
2. Design dcasks-job task definitions for each environment
3. Configure resource allocation:
   - Production: CPU: 512, Memory: 1024 (higher resources)
   - Dev/Testnet: CPU: 256, Memory: 512 (minimal resources)
4. Set up environment variables and secrets integration per cluster
5. Configure logging to CloudWatch with environment-specific log groups

**Deliverables**:
- Task definitions: `dcasks-api-task-{env}`, `dcasks-job-task-{env}`
- Environment-specific resource allocation
- CloudWatch log groups: `/ecs/dcasks-api-{env}`, `/ecs/dcasks-job-{env}`

### ✅ Phase 9: ECS Service Configuration (95% COMPLETED)
**Goal**: Create ECS services with appropriate scaling and networking per cluster

**✅ Completed Tasks**:
1. ✅ **Dev Environment Services** (FULLY OPERATIONAL):
   - ✅ dcasks-api-service-dev: RUNNING (1/1) + HEALTHY
   - ✅ dcasks-job-service-dev: RUNNING (1/1)
   - ✅ Fixed service naming: Removed `-v2` suffix for consistency
   - ✅ Fixed health check: Changed `curl` to `wget` command
   - ✅ Task Definition: dcasks-api-task-dev:8 (latest with fixes)
   - ✅ ALB Integration: Working via dev-api-ecs.dcasks.co
   - ✅ API Endpoints: Health check and GraphQL working correctly

2. ✅ **Services Created** (Need Docker Image Fixes):
   - ✅ dcasks-api-service-testnet: Created but not running (Docker image issue)
   - ✅ dcasks-job-service-testnet: Created but not running (Docker image issue)
   - ✅ dcasks-api-service-production: Created but not running (Docker image issue)
   - ✅ dcasks-job-service-production: Created but not running (Docker image issue)

**⚠️ Remaining Issues to Fix**:
1. **Docker Image References**: Testnet/Production task definitions use incorrect image URLs
   - Current: `dcasks/dcasks-backend:latest` (Docker Hub - fails)
   - Required: `************.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:latest` (ECR)
2. **Health Check Updates**: Apply `wget` health check fix to testnet/production
3. **Service Validation**: Verify all services start successfully after fixes

**✅ Deliverables Completed**:
- ✅ Dev environment fully operational with proper health checks
- ✅ All services created in correct clusters and subnets
- ✅ ALB integration working for dev environment
- ✅ Service naming consistency established
- ✅ Network connectivity validated for dev services

## 🔧 Issues Found and Fixes Applied

### Issue 1: Service Naming Inconsistency
**Problem**: Service named `dcasks-api-service-dev-v2` instead of `dcasks-api-service-dev`
**Root Cause**: Service definition file contained `-v2` suffix
**Solution**:
- Updated service definition file to remove `-v2` suffix
- Created new service with correct name
- Deleted old service with incorrect name
**Status**: ✅ FIXED

### Issue 2: ECS Health Check Failures
**Problem**: Tasks running but showing UNHEALTHY status
**Root Cause**: Health check using `curl` command which wasn't available in container
**Solution**:
- Changed health check command from `curl -f http://localhost:9000/health` to `wget --no-verbose --tries=1 --spider http://localhost:9000/health`
- Registered new task definition revision (dcasks-api-task-dev:8)
- Updated service to use new task definition
**Status**: ✅ FIXED

### Issue 3: Docker Image Pull Failures (Testnet/Production)
**Problem**: Tasks failing with `CannotPullContainerError`
**Root Cause**: Task definitions using incorrect Docker image URLs
- Incorrect: `dcasks/dcasks-backend:latest` (Docker Hub)
- Correct: `************.dkr.ecr.ap-southeast-1.amazonaws.com/dcasks-backend:latest` (ECR)
**Solution**: Need to update task definitions for testnet/production environments
**Status**: ⚠️ IDENTIFIED - Ready to fix when proceeding with testnet/production

### Lessons Learned
1. **Health Check Commands**: Use `wget` instead of `curl` for container health checks
2. **Image References**: Always use full ECR URLs, not Docker Hub shortcuts
3. **Service Naming**: Maintain consistent naming conventions across environments
4. **Incremental Deployment**: Fix dev environment first, then apply lessons to other environments

### Phase 10: Production Migration & Testing (Week 3-4)
**Goal**: Deploy and validate ECS services, then migrate production traffic

**Tasks**:
1. Deploy to dev environment (nprod cluster) and validate functionality
2. Test service-to-service communication within nprod cluster
3. Validate external API connectivity (MongoDB, blockchain) from both clusters
4. Deploy to testnet environment (nprod cluster)
5. Deploy to production environment (prod cluster)
6. Run parallel deployment (EC2 + ECS) for both environments
7. Gradual traffic shift using DNS routing:
   - Testnet: EC2 → nprod cluster
   - Production: EC2 → prod cluster
8. Monitor performance and stability across both clusters
9. Production deployment validation and EC2 decommission

**Deliverables**:
- Dev environment validation report (nprod cluster)
- Testnet migration completion (nprod cluster)
- Production migration completion (prod cluster) with zero downtime
- Performance benchmarks for both clusters
- EC2 infrastructure cleanup
- Post-migration monitoring and optimization

## Technical Specifications

### ECS Task Definitions

#### dcasks-api-task
```json
{
  "family": "dcasks-api-task",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "containerDefinitions": [{
    "name": "dcasks-api",
    "image": "registry.gitlab.com/dcasks/dcasks-backend:latest",
    "portMappings": [{"containerPort": 3000}],
    "environment": [{"name": "SERVICE_NAME", "value": "API"}],
    "secrets": [{"name": "DATABASE_URI", "valueFrom": "arn:aws:secretsmanager:..."}],
    "healthCheck": {
      "command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"],
      "interval": 30,
      "timeout": 5,
      "retries": 3
    }
  }]
}
```

#### dcasks-job-task
```json
{
  "family": "dcasks-job-task",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [{
    "name": "dcasks-job",
    "image": "registry.gitlab.com/dcasks/dcasks-backend:latest",
    "environment": [{"name": "SERVICE_NAME", "value": "JOB"}],
    "secrets": [
      {"name": "DATABASE_URI", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:DATABASE_URI::"},
      {"name": "NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:NETWORK_URL::"},
      {"name": "ETH_NETWORK_URL", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:ETH_NETWORK_URL::"},
      {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:JWT_SECRET::"},
      {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:AWS_ACCESS_KEY_ID::"},
      {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:AWS_SECRET_ACCESS_KEY::"},
      {"name": "CONTRACT_USDT", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:CONTRACT_USDT::"},
      {"name": "CONTRACT_BOTTLE", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:CONTRACT_BOTTLE::"},
      {"name": "CONTRACT_MARKETPLACE", "valueFrom": "arn:aws:secretsmanager:region:account:secret:dcasks/${ENVIRONMENT}/dcasks-backend:CONTRACT_MARKETPLACE::"}
    ],
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/dcasks-job",
        "awslogs-region": "ap-southeast-1",
        "awslogs-stream-prefix": "ecs"
      }
    }
  }]
}
```

#### dcasks-job-service Network Configuration

**Production (Public Subnets with Internet Gateway)**
```json
{
  "serviceName": "dcasks-job-service-production",
  "cluster": "dcasks-cluster-prod",
  "taskDefinition": "dcasks-job-task-production",
  "desiredCount": 1,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": ["subnet-public-1a", "subnet-public-1b"],
      "securityGroups": ["sg-dcasks-job-production"],
      "assignPublicIp": "ENABLED"
    }
  }
}
```

**Non-Production (Public Subnets with Internet Gateway)**
```json
{
  "serviceName": "dcasks-job-service-${ENVIRONMENT}",
  "cluster": "dcasks-cluster-nprod",
  "taskDefinition": "dcasks-job-task-${ENVIRONMENT}",
  "desiredCount": 1,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": ["subnet-public-1a", "subnet-public-1b"],
      "securityGroups": ["sg-dcasks-job-${ENVIRONMENT}"],
      "assignPublicIp": "ENABLED"
    }
  }
}
```

### Security Group Configuration

#### dcasks-job Security Group (sg-dcasks-job-{env})
```json
{
  "GroupName": "dcasks-job-sg-${ENVIRONMENT}",
  "Description": "Security group for dcasks-job service with external API access",
  "VpcId": "vpc-dcasks",
  "SecurityGroupRules": [
    {
      "IpPermissions": [
        {
          "IpProtocol": "tcp",
          "FromPort": 443,
          "ToPort": 443,
          "IpRanges": [{"CidrIp": "0.0.0.0/0", "Description": "HTTPS outbound for external APIs"}]
        },
        {
          "IpProtocol": "tcp",
          "FromPort": 80,
          "ToPort": 80,
          "IpRanges": [{"CidrIp": "0.0.0.0/0", "Description": "HTTP outbound for external APIs"}]
        },
        {
          "IpProtocol": "tcp",
          "FromPort": 27017,
          "ToPort": 27017,
          "IpRanges": [{"CidrIp": "0.0.0.0/0", "Description": "MongoDB Atlas connectivity"}]
        },
        {
          "IpProtocol": "tcp",
          "FromPort": 8545,
          "ToPort": 8545,
          "IpRanges": [{"CidrIp": "0.0.0.0/0", "Description": "Blockchain RPC connectivity"}]
        }
      ]
    }
  ]
}
```

### Auto-Scaling Configuration

**Production Cluster (Higher Capacity)**
```json
{
  "serviceArn": "dcasks-api-service-production",
  "scalableDimension": "ecs:service:DesiredCount",
  "minCapacity": 2,
  "maxCapacity": 5,
  "targetTrackingScalingPolicies": [{
    "targetValue": 70.0,
    "predefinedMetricSpecification": {
      "predefinedMetricType": "ECSServiceAverageCPUUtilization"
    }
  }]
}
```

**Non-Production Cluster (Minimal Capacity)**
```json
{
  "serviceArn": "dcasks-api-service-${ENVIRONMENT}",
  "scalableDimension": "ecs:service:DesiredCount",
  "minCapacity": 1,
  "maxCapacity": 2,
  "targetTrackingScalingPolicies": [{
    "targetValue": 80.0,
    "predefinedMetricSpecification": {
      "predefinedMetricType": "ECSServiceAverageCPUUtilization"
    }
  }]
}
```

## Rollback Strategy

### Immediate Rollback (< 5 minutes)
1. Route 53 weighted routing back to EC2 (100% traffic)
2. Scale down ECS services to 0
3. Verify EC2 services are healthy

### Service-Level Rollback
1. Revert ECS task definition to previous version
2. Force new deployment with previous task definition
3. Monitor service health and performance

### Complete Rollback
1. Disable ECS services
2. Restore EC2 deployment scripts
3. Redeploy to EC2 using existing GitLab CI jobs

## Monitoring & Validation

### Health Checks
- ALB health check for dcasks-api (HTTP /health endpoint)
- ECS service health monitoring
- CloudWatch container insights

### Key Metrics
- Service CPU/Memory utilization
- Request latency and error rates
- Task startup time and stability
- Auto-scaling behavior

### Validation Criteria
- [ ] All API endpoints respond correctly
- [ ] Job service processes tasks successfully
- [ ] External API connectivity maintained (MongoDB Atlas, Blockchain APIs)
- [ ] dcasks-job service can fetch external data successfully
- [ ] Internet Gateway and public subnet routing working
- [ ] Security group egress rules allow required external connections
- [ ] Auto-scaling triggers appropriately for dcasks-api
- [ ] Logs are properly captured in CloudWatch

## Risk Mitigation

### High Risk
- **External API connectivity failure**: Test MongoDB Atlas and blockchain API access thoroughly
- **Internet Gateway routing issues**: Validate public subnet and IGW configuration
- **Shared ALB routing conflicts**: Test host-based routing between environments
- **Service communication failure**: Test internal networking thoroughly
- **Secret access issues**: Validate IAM permissions before migration

### Medium Risk
- **Security group misconfiguration**: Ensure proper egress rules for external APIs
- **Auto-scaling misconfiguration**: Start with conservative scaling policies
- **ALB target group health checks**: Validate health check configurations
- **CI/CD pipeline failures**: Maintain parallel deployment capability

### Low Risk
- **Cost overruns**: Optimized with shared ALB and no NAT Gateway
- **Logging issues**: Configure CloudWatch early in process

## Success Criteria

### ✅ Dev Environment (COMPLETED)
- [x] **dcasks-api-service-dev**: Running on ECS Fargate (1/1) + HEALTHY
- [x] **dcasks-job-service-dev**: Running on ECS Fargate (1/1)
- [x] **External integrations**: Working (MongoDB Atlas, blockchain APIs)
- [x] **ALB integration**: Working via dev-api-ecs.dcasks.co
- [x] **Health checks**: Proper health check configuration with wget
- [x] **Service naming**: Consistent naming convention established
- [x] **API functionality**: Health endpoint and GraphQL working
- [x] **Logging**: CloudWatch logs operational for dev services

### ⚠️ Testnet Environment (NEEDS FIXES)
- [ ] dcasks-api-service-testnet: Created but needs Docker image fix
- [ ] dcasks-job-service-testnet: Created but needs Docker image fix
- [ ] Apply health check fixes from dev environment
- [ ] Verify external integrations after fixes

### ⚠️ Production Environment (NEEDS FIXES)
- [ ] dcasks-api-service-production: Created but needs Docker image fix
- [ ] dcasks-job-service-production: Created but needs Docker image fix
- [ ] Auto-scaling configured for dcasks-api (2-5 instances)
- [ ] Apply health check fixes from dev environment
- [ ] Verify external integrations after fixes

### 🎯 Overall Migration Goals
- [x] **Public subnet and Internet Gateway routing**: Operational
- [x] **Shared ALB with host-based routing**: Working for dev
- [x] **Security groups**: Allow required external API access
- [ ] **GitLab CI**: Deploy to appropriate ECS clusters (pending testnet/prod fixes)
- [x] **Monitoring and logging**: Operational for dev environment
- [ ] **EC2 infrastructure decommissioned**: Pending completion of all environments
- [x] **Cost optimization achieved**:
  - [x] No NAT Gateway charges (saves ~$90/month)
  - [x] Single ALB instead of 3 (saves ~$32/month)
  - [x] Minimal resource allocation for non-production

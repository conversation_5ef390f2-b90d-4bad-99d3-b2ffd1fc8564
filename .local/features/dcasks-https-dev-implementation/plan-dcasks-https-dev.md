# DCasks Dev Environment HTTPS Implementation Plan

**Target**: Enable HTTPS access for `dev-api-ecs.dcasks.co/health` endpoint  
**Date**: 2025-06-11  
**Status**: AWAITING APPROVAL  
**Estimated Duration**: 45-75 minutes  

## Overview

Enable HTTPS access for the dev environment API while maintaining existing HTTP functionality. Leverage existing ACM certificate and ALB infrastructure to provide secure access to the dev API endpoint.

## Prerequisites Verification

### ✅ Existing Infrastructure
- ALB: `dcasks-alb` (operational with HTTP listener)
- ACM Certificate: `arn:aws:acm:ap-southeast-1:483805378365:certificate/d1bf9679-95ba-4923-8f72-b19e4a963be6`
- Dev API Service: `dcasks-api-service-dev` (running and healthy)
- Target Group: `dcasks-api-tg-dev` (configured for port 9000)
- Security Groups: `dcasks-alb-sg`, `dcasks-api-sg` (configured)

### 🔍 Pre-Implementation Checks
**GO/NO-GO Decision Point 1**: Verify these before proceeding
- [ ] ACM certificate validation status (must be "ISSUED")
- [ ] Current HTTP access working: `curl http://dev-api-ecs.dcasks.co/health`
- [ ] ALB target health: All targets healthy
- [ ] Cloudflare DNS pointing to ALB

## Implementation Steps

### Phase 1: Certificate Validation (15-30 min)
**Objective**: Ensure ACM certificate is ready for use

1. **Check Certificate Status**
   ```bash
   aws acm describe-certificate --certificate-arn <cert-arn> --query 'Certificate.Status'
   ```

2. **If Certificate Pending Validation**:
   - Retrieve DNS validation records
   - Add CNAME records to Cloudflare DNS
   - Wait for validation (5-10 minutes)

**GO/NO-GO Decision Point 2**: Certificate must show "ISSUED" status

### Phase 2: ALB HTTPS Listener (10-15 min)
**Objective**: Add HTTPS listener while preserving HTTP

1. **Add Security Group Rule**
   ```bash
   # Allow HTTPS traffic to ALB
   aws ec2 authorize-security-group-ingress --group-id <alb-sg-id> --protocol tcp --port 443 --cidr 0.0.0.0/0
   ```

2. **Create HTTPS Listener**
   ```bash
   # Add HTTPS listener with ACM certificate
   aws elbv2 create-listener --load-balancer-arn <alb-arn> --protocol HTTPS --port 443 --certificates CertificateArn=<cert-arn> --default-actions Type=forward,TargetGroupArn=<target-group-arn>
   ```

**GO/NO-GO Decision Point 3**: HTTPS listener created successfully

### Phase 3: Cloudflare SSL Configuration (5-10 min)
**Objective**: Configure Cloudflare for end-to-end encryption

1. **Set SSL/TLS Mode**: Change to "Full (Strict)" in Cloudflare dashboard
2. **Enable Security Features**: 
   - Always Use HTTPS (optional)
   - HSTS (optional)

### Phase 4: Testing & Validation (15-20 min)
**Objective**: Verify HTTPS functionality and maintain HTTP compatibility

1. **HTTPS Access Test**
   ```bash
   curl -v https://dev-api-ecs.dcasks.co/health
   # Expected: 200 OK with JSON health response
   ```

2. **HTTP Backward Compatibility**
   ```bash
   curl -v http://dev-api-ecs.dcasks.co/health
   # Expected: 200 OK (or 301 redirect if configured)
   ```

3. **SSL Certificate Validation**
   ```bash
   openssl s_client -connect dev-api-ecs.dcasks.co:443 -servername dev-api-ecs.dcasks.co
   # Expected: Valid certificate chain
   ```

**GO/NO-GO Decision Point 4**: All tests pass successfully

## Success Criteria

### Must Have ✅
- [ ] HTTPS endpoint accessible: `https://dev-api-ecs.dcasks.co/health` returns 200
- [ ] Valid SSL certificate with proper domain validation
- [ ] HTTP functionality maintained (no service disruption)
- [ ] ECS service health remains stable throughout implementation
- [ ] ALB target group shows healthy targets

### Should Have 📋
- [ ] SSL Labs rating A- or higher
- [ ] Proper certificate chain validation
- [ ] Cloudflare Full (Strict) SSL mode configured

## Rollback Plan

### Immediate Rollback (< 5 minutes)
1. **Remove HTTPS Listener**
   ```bash
   aws elbv2 delete-listener --listener-arn <https-listener-arn>
   ```

2. **Revert Cloudflare SSL Mode**: Change back to previous setting

3. **Remove Security Group Rule** (if needed)
   ```bash
   aws ec2 revoke-security-group-ingress --group-id <alb-sg-id> --protocol tcp --port 443 --cidr 0.0.0.0/0
   ```

### Validation After Rollback
- Verify HTTP access still works
- Confirm no service disruption occurred

## Risk Assessment

### High Risk 🔴
- **Certificate validation failure**: Mitigate by verifying DNS access and CNAME format
- **ALB listener misconfiguration**: Mitigate by testing incrementally

### Medium Risk 🟡
- **Cloudflare SSL mode mismatch**: Mitigate by starting with "Full" mode
- **Security group issues**: Mitigate by adding rules incrementally

### Low Risk 🟢
- **DNS propagation delays**: Wait for full propagation
- **Temporary access issues**: Monitor during implementation

## Timeline

| Phase | Duration | Critical Path |
|-------|----------|---------------|
| Certificate Validation | 15-30 min | Yes |
| ALB HTTPS Listener | 10-15 min | Yes |
| Cloudflare Configuration | 5-10 min | No |
| Testing & Validation | 15-20 min | Yes |
| **Total** | **45-75 min** | |

## Implementation Checklist

### Pre-Implementation
- [ ] Verify current HTTP functionality
- [ ] Check ACM certificate status
- [ ] Confirm ALB and target group health
- [ ] Document current Cloudflare settings

### During Implementation
- [ ] Monitor ECS service health continuously
- [ ] Test each phase before proceeding
- [ ] Document any issues encountered

### Post-Implementation
- [ ] Update documentation with HTTPS endpoints
- [ ] Monitor service performance for 24 hours
- [ ] Update health check scripts if needed

---

**⚠️ APPROVAL REQUIRED**: This plan requires explicit approval before proceeding with any AWS infrastructure changes. No AWS CLI commands, ALB modifications, or Cloudflare configuration changes will be executed until approved.

**Next Steps**: 
1. Review this implementation plan
2. Provide approval or feedback
3. Execute implementation with continuous monitoring
4. Validate success criteria and document results

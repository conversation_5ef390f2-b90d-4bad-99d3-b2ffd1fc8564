# DCasks Backend - Background Jobs & Scheduled Processes Analysis

## 📋 **Executive Summary**

The DCasks backend system runs **8 active background jobs** and has **6 disabled jobs** for future use. All jobs are managed through the DCasks Job Service (`job.module.ts`) which runs as a separate service instance from the main API service.

---

## 🔄 **Active Background Jobs**

### **1. Blockchain Event Handling**
| Property | Value |
|----------|-------|
| **Job Name** | Blockchain Event Processor |
| **Schedule** | Every 100ms (0.1 seconds) |
| **Purpose** | Process blockchain events from queue |
| **Dependencies** | MongoDB, Blockchain Event Service |
| **Resource Impact** | High frequency, low individual execution time |
| **Configuration** | `INTERVAL_HANDLE_EVENTS = 100ms` |

**Responsibilities:**
- Process stored blockchain events from database
- Handle DCask bottle, marketplace, NFT, membership, and tulip events
- Update system state based on blockchain activity
- Trigger notifications and system activities

### **2. Blockchain Event Querying**
| Property | Value |
|----------|-------|
| **Job Name** | Blockchain Event Querier |
| **Schedule** | Dynamic (2s gap + configurable interval) |
| **Purpose** | Query historical blockchain events |
| **Dependencies** | Blockchain RPC, MongoDB |
| **Resource Impact** | Medium, depends on block range |
| **Configuration** | `GAP_QUERY_FILTER_EVENTS = 2000ms`, `INTERVAL_QUERY_FILTER_EVENTS = configurable` |

**Responsibilities:**
- Query blockchain for historical events in batches
- Sync from last processed block to current block
- Store events in database for processing
- Handle both regular and catch-up scenarios

### **3. Blockchain Event Listening**
| Property | Value |
|----------|-------|
| **Job Name** | Blockchain Event Listener |
| **Schedule** | Real-time WebSocket listeners |
| **Purpose** | Listen for new blockchain events |
| **Dependencies** | WebSocket RPC, MongoDB |
| **Resource Impact** | Low, event-driven |
| **Configuration** | `EVENT_LISTEN_DELAY = 1000ms` (startup delay) |

**Responsibilities:**
- Listen to real-time blockchain events via WebSocket
- Store incoming events immediately
- Handle multiple contract event types
- Maintain persistent connections

### **4. NFT Sale Pool Processing**
| Property | Value |
|----------|-------|
| **Job Name** | Pool Processor |
| **Schedule** | Every 30 seconds |
| **Purpose** | Manage NFT sale queue and active pool |
| **Dependencies** | MongoDB (NFT Sale collections) |
| **Resource Impact** | Medium, database-intensive |
| **Configuration** | Fixed 30-second interval |

**Responsibilities:**
- Process active pool for timeouts and completions
- Move waiting users from queue to active pool
- Handle heartbeat timeouts (5-minute threshold)
- Clean up expired sessions
- Manage pool capacity and user advancement

### **5. NFT Sale Payment Monitoring**
| Property | Value |
|----------|-------|
| **Job Name** | Payment Monitor |
| **Schedule** | Every 1 minute |
| **Purpose** | Monitor payment status and handle timeouts |
| **Dependencies** | MongoDB, Payment Gateways (Stripe, NOWPayments) |
| **Resource Impact** | Medium, external API calls |
| **Configuration** | Fixed 60-second interval |

**Responsibilities:**
- Check pending payment statuses
- Expire timed-out payments
- Retry failed payments where applicable
- Update order and session statuses
- Handle payment gateway webhooks

### **6. NFT Sale Payment Cleanup**
| Property | Value |
|----------|-------|
| **Job Name** | Payment Cleanup |
| **Schedule** | Every 24 hours |
| **Purpose** | Clean up old payment records and data |
| **Dependencies** | MongoDB |
| **Resource Impact** | Low frequency, high data volume |
| **Configuration** | Fixed 24-hour interval, 1-minute startup delay |

**Responsibilities:**
- Remove old completed/failed payment records
- Archive important data for analytics
- Clean up expired sessions and orders
- Maintain database performance

### **7. NFT Sale Configuration Management**
| Property | Value |
|----------|-------|
| **Job Name** | Config Manager |
| **Schedule** | Every 30 seconds |
| **Purpose** | Reload sale configuration and admin actions |
| **Dependencies** | MongoDB (sale_config, admin_actions) |
| **Resource Impact** | Low, configuration queries only |
| **Configuration** | Fixed 30-second interval |

**Responsibilities:**
- Load and cache sale configuration
- Check for admin actions and configuration changes
- Update system behavior based on config changes
- Maintain configuration cache for performance

### **8. Blockchain Event Storage**
| Property | Value |
|----------|-------|
| **Job Name** | Event Storage |
| **Schedule** | Real-time (event-driven) |
| **Purpose** | Store blockchain events in database |
| **Dependencies** | MongoDB |
| **Resource Impact** | Low, simple database writes |
| **Configuration** | Event-driven, no fixed schedule |

**Responsibilities:**
- Store incoming blockchain events
- Maintain event order and integrity
- Handle multiple contract addresses
- Provide data for event processor

---

## 💤 **Disabled/Commented Jobs**

### **1. Airdrop Processing**
```typescript
// (async () => {
//   while (true) {
//     const tokenAirdrop = await this.airdropService.consumeSendTokenAirdrop();
//     const emailAirdrop = await this.airdropService.consumeSendEmailAirdrop();
//     if (!tokenAirdrop && !emailAirdrop) {
//       await this.wait(INTERVAL_CONSUME_AIRDROP); // 10 seconds
//     }
//   }
// })();
```
**Purpose:** Process token and email airdrops
**Schedule:** Every 10 seconds (when no airdrops to process)

### **2. BRC420 Minted Transactions Sync**
```typescript
// (async () => {
//   while (true) {
//     await this.brc420Service.startSyncMintedTxs();
//     await this.waitMs(10 * 60 * 1000); // 10 minutes
//   }
// })();
```
**Purpose:** Sync BRC420 minted transactions
**Schedule:** Every 10 minutes

### **3. BRC420 Traded Transactions Sync**
```typescript
// (async () => {
//   await sleep(10000); // 10 seconds startup delay
//   while (true) {
//     await this.brc420Service.startSyncTradedTxs();
//     await this.waitMs(10 * 60 * 1000); // 10 minutes
//   }
// })();
```
**Purpose:** Sync BRC420 traded transactions
**Schedule:** Every 10 minutes (10-second startup delay)

### **4. BRC420 Transfer Transactions Sync**
```typescript
// (async () => {
//   await sleep(30000); // 30 seconds startup delay
//   while (true) {
//     await this.brc420Service.startSyncTransferTxs();
//     await this.waitMs(30 * 60 * 1000); // 30 minutes
//   }
// })();
```
**Purpose:** Sync BRC420 transfer transactions
**Schedule:** Every 30 minutes (30-second startup delay)

### **5. Metaplex NFT Sync**
```typescript
// (async () => {
//   await sleep(10000); // 10 seconds startup delay
//   while (true) {
//     await this.metaplexService.syncAllNfts();
//     await this.waitMs(60 * 1000); // 1 minute
//   }
// })();
```
**Purpose:** Sync Solana NFTs via Metaplex
**Schedule:** Every 1 minute (10-second startup delay)

### **6. Daily Scheduled Jobs (Cron)**
```typescript
// Daily at 00:01 UTC
// schedule.scheduleJob(rule, async () => {
//   await this.airdropService.createAirdropV2();
//   await this.airdropService.sendAirdropV2();
// });

// schedule.scheduleJob(rule, async () => {
//   await this.miningService.createMining();
//   await this.miningService.sendMining();
// });
```
**Purpose:** Daily airdrop and mining operations
**Schedule:** Daily at 00:01 UTC

---

## 📊 **Job Execution Timeline**

```mermaid
gantt
    title DCasks Background Jobs Timeline
    dateFormat X
    axisFormat %L
    
    section High Frequency
    Blockchain Event Processing    :0, 100
    
    section Medium Frequency  
    Config Management             :0, 30000
    Pool Processing               :0, 30000
    Payment Monitoring            :0, 60000
    
    section Low Frequency
    Payment Cleanup               :60000, 86400000
    
    section Real-time
    Event Listening               :active, 0, 86400000
    Event Storage                 :active, 0, 86400000
    
    section Dynamic
    Event Querying                :2000, 600000
```

---

## 🔧 **Configuration Parameters**

### **Environment Variables**
| Variable | Default | Purpose |
|----------|---------|---------|
| `INTERVAL_QUERY_FILTER_EVENTS` | 10 minutes | Blockchain event query interval |
| `EVENT_LISTEN_DELAY` | 1000ms | Startup delay for event listeners |
| `SERVICE_NAME` | - | Determines if job service runs |

### **Constants (src/config/constants.ts)**
| Constant | Value | Purpose |
|----------|-------|---------|
| `INTERVAL_HANDLE_EVENTS` | 100ms | Event processing frequency |
| `GAP_QUERY_FILTER_EVENTS` | 2000ms | Gap between query batches |
| `INTERVAL_CONSUME_AIRDROP` | 10000ms | Airdrop processing interval |
| `CONFIRMED_BLOCK` | 0 | Block confirmation requirement |
| `MAX_BLOCK_STEP` | 10,000 | Maximum blocks per query |

### **NFT Sale Timeouts (sale-config.model.ts)**
| Setting | Default | Purpose |
|---------|---------|---------|
| `selectionTimeoutMinutes` | 5 minutes | NFT selection window |
| `paymentTimeoutMinutes` | 5 minutes | Payment completion window |
| `queueSessionTimeoutMinutes` | - | Queue session expiration |
| `stripeTimeoutMinutes` | - | Stripe payment timeout |
| `nowpaymentsTimeoutMinutes` | 20 minutes | NOWPayments timeout |

---

## 📈 **Resource Usage Analysis**

### **High Impact Jobs**
1. **Blockchain Event Processing** (100ms) - Continuous database operations
2. **Pool Processing** (30s) - Complex queue management logic
3. **Payment Monitoring** (60s) - External API calls and database updates

### **Medium Impact Jobs**
1. **Event Querying** (dynamic) - Blockchain RPC calls and batch processing
2. **Config Management** (30s) - Lightweight database queries

### **Low Impact Jobs**
1. **Payment Cleanup** (24h) - Infrequent but potentially large data operations
2. **Event Listening** (real-time) - Event-driven, minimal resource usage
3. **Event Storage** (real-time) - Simple database writes

### **Estimated Execution Times**
- **Event Processing**: 10-100ms per batch
- **Pool Processing**: 500ms-2s depending on queue size
- **Payment Monitoring**: 200ms-1s per payment check
- **Config Management**: 50-200ms
- **Event Querying**: 1-10s depending on block range
- **Payment Cleanup**: 5-30s depending on data volume

## 📋 **Quick Reference Table**

| Job Name | Status | Schedule | Purpose | Dependencies | Impact |
|----------|--------|----------|---------|--------------|--------|
| **Blockchain Event Processor** | ✅ Active | 100ms | Process blockchain events | MongoDB, Event Service | High |
| **Blockchain Event Querier** | ✅ Active | Dynamic (2s + config) | Query historical events | Blockchain RPC, MongoDB | Medium |
| **Blockchain Event Listener** | ✅ Active | Real-time | Listen for new events | WebSocket RPC, MongoDB | Low |
| **Pool Processor** | ✅ Active | 30s | Manage NFT sale queue | MongoDB (NFT Sale) | Medium |
| **Payment Monitor** | ✅ Active | 1min | Monitor payment status | MongoDB, Payment Gateways | Medium |
| **Payment Cleanup** | ✅ Active | 24h | Clean old payment data | MongoDB | Low |
| **Config Manager** | ✅ Active | 30s | Reload configurations | MongoDB | Low |
| **Event Storage** | ✅ Active | Event-driven | Store blockchain events | MongoDB | Low |
| **Airdrop Processing** | ❌ Disabled | 10s | Process airdrops | MongoDB, External APIs | - |
| **BRC420 Minted Sync** | ❌ Disabled | 10min | Sync BRC420 minted txs | BRC420 API, MongoDB | - |
| **BRC420 Traded Sync** | ❌ Disabled | 10min | Sync BRC420 traded txs | BRC420 API, MongoDB | - |
| **BRC420 Transfer Sync** | ❌ Disabled | 30min | Sync BRC420 transfers | BRC420 API, MongoDB | - |
| **Metaplex NFT Sync** | ❌ Disabled | 1min | Sync Solana NFTs | Metaplex API, MongoDB | - |
| **Daily Cron Jobs** | ❌ Disabled | 00:01 UTC | Daily operations | Various services | - |

## 🎯 **Monitoring Recommendations**

### **Critical Metrics to Monitor**
1. **Event Processing Lag** - Blockchain event processing delay
2. **Queue Processing Time** - Pool processor execution duration
3. **Payment Success Rate** - Payment monitoring effectiveness
4. **Database Connection Pool** - Resource utilization
5. **Job Failure Rate** - Error frequency across all jobs

### **Alert Thresholds**
- **Event Processing**: > 5 seconds lag
- **Pool Processing**: > 10 seconds execution time
- **Payment Monitoring**: > 30 seconds execution time
- **Database Queries**: > 2 seconds response time
- **Job Failures**: > 5% failure rate in 5-minute window

### **Performance Optimization**
1. **Database Indexing** - Ensure proper indexes for job queries
2. **Connection Pooling** - Optimize database connection usage
3. **Batch Processing** - Group operations where possible
4. **Error Handling** - Implement retry logic with exponential backoff
5. **Resource Limits** - Set appropriate memory and CPU limits

This comprehensive analysis provides complete visibility into all automated processes running in the DCasks backend system, enabling effective monitoring, maintenance planning, and troubleshooting.

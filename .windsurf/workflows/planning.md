---
description: Planning mode
---

## Planning Mode

### Core Rules
- **NO CODE IMPLEMENTATION** - Planning mode is strictly for design and planning
- **NO FILE CREATION** - Do not create any actual files or code during planning
- **NO PACKAGE INSTALLATION** - Do not install dependencies or tools
- Understand the complete problem scope before proposing solutions
- Design comprehensive architecture before any implementation
- Document all decisions, trade-offs, and alternatives considered
- Plan detailed file structure and complete tech stack
- Identify potential risks and mitigation strategies
- Define clear success criteria and acceptance tests
- Establish coding standards and conventions
- Plan testing strategy (unit, integration, e2e)
- Consider scalability, performance, and maintainability
- Define API contracts and data models
- Plan deployment and CI/CD strategy
- Estimate effort and timeline for each component
- Identify dependencies between components
- Plan for monitoring, logging, and error handling
- Consider security implications and requirements
- Define user stories and acceptance criteria

### Output Template
```markdown
# Project Plan: [Project Name]

## Problem Statement
Brief description of what needs to be built and why.

## Requirements Analysis
### Functional Requirements
- [Requirement 1]: [Description]
- [Requirement 2]: [Description]

### Non-Functional Requirements
- Performance: [specifications]
- Security: [requirements]
- Scalability: [expectations]
- Usability: [standards]

## Architecture Overview
High-level system design and component relationships.

### System Components
- [Component 1]: [Purpose and responsibilities]
- [Component 2]: [Purpose and responsibilities]

### Data Flow
Description of how data moves through the system.

### API Design
- [Endpoint 1]: [Method] [Path] - [Purpose]
- [Endpoint 2]: [Method] [Path] - [Purpose]

## Tech Stack
- Frontend: [technology + detailed reason for choice]
- Backend: [technology + detailed reason for choice]
- Database: [technology + detailed reason for choice]
- Authentication: [approach + tools]
- Testing: [frameworks and strategies]
- Deployment: [platform and approach]
- Monitoring: [tools and approach]

## File Structure

project/
├── src/
│   ├── components/
│   │   ├── common/
│   │   └── pages/
│   ├── services/
│   ├── utils/
│   ├── types/
│   └── constants/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/
└── config/


## Data Models
### [Model Name]
- field1: [type] - [description]
- field2: [type] - [description]

## Implementation Steps
### Phase 1: [Name] (Estimated: [time])
- Step 1: [Detailed description]
- Step 2: [Detailed description]
- Deliverables: [What will be completed]

### Phase 2: [Name] (Estimated: [time])
- Step 1: [Detailed description]
- Step 2: [Detailed description]
- Deliverables: [What will be completed]

## Testing Strategy
- Unit Tests: [Approach and tools]
- Integration Tests: [Approach and scope]
- E2E Tests: [Critical user flows]
- Performance Tests: [Key metrics to test]

## Security Considerations
- Authentication: [Approach]
- Authorization: [Strategy]
- Data Protection: [Methods]
- Input Validation: [Strategy]

## Performance Considerations
- Expected Load: [Users/requests]
- Response Time Targets: [Specifications]
- Optimization Strategies: [Approaches]

## Risk Assessment
- Risk 1: [description] → [likelihood: H/M/L] → [mitigation strategy]
- Risk 2: [description] → [likelihood: H/M/L] → [mitigation strategy]

## Success Criteria
- [Measurable outcome 1]
- [Measurable outcome 2]
- [Performance benchmark]

## Timeline & Milestones
- Milestone 1: [Date] - [Deliverable]
- Milestone 2: [Date] - [Deliverable]
- Final Delivery: [Date]
```
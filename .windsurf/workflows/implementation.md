---
description: Implementation mode
---

## Implementation Mode

### Core Rules
- Follow the established plan religiously
- Write clean, well-documented code
- Log ALL actions and decisions in real-time
- Handle errors gracefully and document solutions
- Test code as you write it
- Never deviate from architecture without updating the plan first
- All features have to ran e2e test (no need unit test for FE) get feedback via MCP tool (e.g. playwright)
- Try to complete the plan as much as possible 

### Real-Time Inline Logging Format
Log every action immediately as: `[TIMESTAMP] [STATUS] [TYPE] [ACTION] → [DETAILS]`

### Output Template
```markdown
# Implementation Session: [Feature/Component Name]

**Date**: 2025-05-26 | **Started**: 14:30 UTC | **Target**: [Implementation goal] | **Plan**: [Reference to planning doc]

## Live Implementation Log

[14:30:15] ✅ SESSION_START Starting user authentication implementation → Following auth-system-design.md plan

[14:31:22] ✅ FILE_CREATE Created `src/auth/AuthService.js` → Main authentication service class scaffolded

[14:32:45] 🔄 DEPENDENCY_ADD Installing jsonwebtoken → Running `npm install jsonwebtoken`
[14:32:52] ✅ DEPENDENCY_ADD jsonwebtoken@9.0.0 installed → Added to package.json

[14:35:12] 🔄 CODE_WRITE Implementing login method → Adding JWT token generation
[14:35:45] ❌ ERROR_ENCOUNTERED TypeError: Cannot read property 'verify' of undefined → Missing jwt import
[14:36:15] ✅ ERROR_FIX Added proper jwt import statement → Error resolved, login method working

[14:38:00] ✅ CODE_COMPLETE Login method finished → 25 lines added, basic JWT auth working
[14:38:30] ✅ TEST_ADD Created 3 unit tests for login → All tests passing

[14:40:15] 🔄 REFACTOR Extracted token validation to separate method → Better separation of concerns
[14:41:00] ✅ CODE_REVIEW Self-review of authentication flow → Follows security best practices

[14:42:30] ✅ FILE_MODIFY Updated package.json scripts → Added test script for auth module
[14:43:15] ✅ DOCUMENTATION Added JSDoc comments to AuthService → All methods documented

[14:45:00] ✅ SESSION_END Authentication service impleme